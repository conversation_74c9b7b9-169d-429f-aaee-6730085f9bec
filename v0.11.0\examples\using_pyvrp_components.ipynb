{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Using PyVRP's components\n", "\n", "We have relied on the `Model` interface to solve VRP instances in the examples so far.\n", "That high-level interface hides a lot of the components that available in PyVRP, which uses [a hybrid genetic search algorithm](https://pyvrp.org/setup/introduction_to_hgs.html) under the hood.\n", "In this notebook we will investigate these components in more detail to build our own `solve` function based on hybrid genetic search.\n", "\n", "Along the way we will solve the `RC208.vrp` instance, one of the well-known VRPTW benchmark instances of Solomon.\n", "This instance consists of 100 clients."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.161939Z", "iopub.status.busy": "2025-05-15T14:42:22.161747Z", "iopub.status.idle": "2025-05-15T14:42:22.249517Z", "shell.execute_reply": "2025-05-15T14:42:22.248968Z"}}, "outputs": [], "source": ["from pyvrp import read\n", "\n", "INSTANCE = read(\"data/RC208.vrp\", \"dimacs\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We will implement a `solve()` function that will take a `stop` stopping criterion, and a `seed` for the random number generator.\n", "This definition is very close to that of `Model.solve`.\n", "The signature is\n", "```python\n", "def solve(stop: Stopping<PERSON>rite<PERSON>, seed: int) -> Solution: ...\n", "```"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## A tour of PyVRP\n", "\n", "We need to understand the separate components in PyVRP before we are ready to implement this function.\n", "PyVRP uses a hybrid genetic search algorithm under the hood.\n", "The `GeneticAlgorithm` object manages a population of solutions.\n", "In each iteration, two solutions are selected from this population for *crossover* using a crossover operator from `pyvrp.crossover`, which generates a new offspring solution.\n", "That offspring solution is then improved using a method from `pyvrp.search`.\n", "The improved offspring solution is then added to the population.\n", "This process continues until a stopping condition is reached (see `pyvrp.stop` for different conditions).\n", "Let's have a look at the different parts of `pyvrp` that implement this algorithm."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To instantiate the `GeneticAlgorithm`, we first need to specify an (initial) population, search method, penalty manager and random number generator.\n", "Let's start with the random number generator because it is the easiest to set up.\n", "\n", "##### Random number generator"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.251968Z", "iopub.status.busy": "2025-05-15T14:42:22.251716Z", "iopub.status.idle": "2025-05-15T14:42:22.255489Z", "shell.execute_reply": "2025-05-15T14:42:22.254951Z"}}, "outputs": [], "source": ["from pyvrp import RandomNumberGenerator\n", "\n", "rng = RandomNumberGenerator(seed=42)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["##### Search method\n", "\n", "Let's now define the search method.\n", "PyVRP currently implements a `LocalSearch` method that is very customisable with different operators and search neighbourhoods.\n", "Different operators search different parts of the solution space, which can be beneficial in finding better solutions.\n", "The neighbourhood defines which edges are evaluated.\n", "By restricting that set of edges the local search method works much faster.\n", "\n", "We provide default operator sets and neighbourhoods, which can be used as follows.  "]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.257680Z", "iopub.status.busy": "2025-05-15T14:42:22.257494Z", "iopub.status.idle": "2025-05-15T14:42:22.263558Z", "shell.execute_reply": "2025-05-15T14:42:22.263017Z"}}, "outputs": [], "source": ["from pyvrp.search import (\n", "    LocalSearch,\n", "    NODE_OPERATORS,\n", "    ROUTE_OPERATORS,\n", "    compute_neighbours,\n", ")\n", "\n", "neighbours = compute_neighbours(INSTANCE)\n", "ls = LocalSearch(INSTANCE, rng, neighbours)\n", "\n", "for node_op in NODE_OPERATORS:\n", "    ls.add_node_operator(node_op(INSTANCE))\n", "\n", "for route_op in ROUTE_OPERATORS:\n", "    ls.add_route_operator(route_op(INSTANCE))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["##### Solution representation and evaluation\n", "\n", "We now have a functioning local search method.\n", "All we need are two additional components to make it work: a `Solution` that described a set of routes, and a `CostEvaluator` that can be used to evaluate different moves.\n", "Let's define those."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.266503Z", "iopub.status.busy": "2025-05-15T14:42:22.265705Z", "iopub.status.idle": "2025-05-15T14:42:22.269740Z", "shell.execute_reply": "2025-05-15T14:42:22.269254Z"}}, "outputs": [], "source": ["from pyvrp import Solution, CostEvaluator\n", "\n", "cost_evaluator = CostEvaluator(\n", "    load_penalties=[20],\n", "    tw_penalty=20,\n", "    dist_penalty=0,\n", ")\n", "sol = Solution.make_random(INSTANCE, rng)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The random solution `sol` that we created just yet is not feasible.\n", "This is not a problem, because PyVRP internally uses penalties to evaluate infeasibilities in each solution.\n", "This is done using the `CostEvaluator`'s `penalised_cost` function, which allows us to determine the quality of infeasible solutions as well."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.272173Z", "iopub.status.busy": "2025-05-15T14:42:22.271979Z", "iopub.status.idle": "2025-05-15T14:42:22.276754Z", "shell.execute_reply": "2025-05-15T14:42:22.276188Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["79611\n"]}], "source": ["assert not sol.is_feasible()\n", "print(cost_evaluator.penalised_cost(sol))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Let's see if the local search can improve this solution further."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.278818Z", "iopub.status.busy": "2025-05-15T14:42:22.278630Z", "iopub.status.idle": "2025-05-15T14:42:22.289869Z", "shell.execute_reply": "2025-05-15T14:42:22.289454Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8565\n"]}], "source": ["new_sol = ls.search(sol, cost_evaluator)\n", "\n", "assert not sol.is_feasible()\n", "print(cost_evaluator.penalised_cost(new_sol))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Much better! \n", "But the new solution is not yet feasible.\n", "Can we hammer out the infeasibilities by increasing the penalties?"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.291683Z", "iopub.status.busy": "2025-05-15T14:42:22.291507Z", "iopub.status.idle": "2025-05-15T14:42:22.301122Z", "shell.execute_reply": "2025-05-15T14:42:22.300693Z"}}, "outputs": [], "source": ["cost_evaluator = CostEvaluator([200], 200, 0)\n", "new_sol = ls.search(sol, cost_evaluator)\n", "\n", "assert new_sol.is_feasible()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["How good is this solution?"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.302683Z", "iopub.status.busy": "2025-05-15T14:42:22.302510Z", "iopub.status.idle": "2025-05-15T14:42:22.305298Z", "shell.execute_reply": "2025-05-15T14:42:22.304760Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8579\n"]}], "source": ["print(cost_evaluator.penalised_cost(new_sol))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Pretty good!\n", "This is how PyVRP manages infeasibilities: it adjusts the penalty parameters to ensure sufficiently many solutions are feasible.\n", "Too few feasible solutions and the penalties go up; too many and they go down.\n", "This ensures a balanced population of feasible and infeasible solutions, which is good for diversity and crossover.\n", "\n", "The object in charge of managing the penalty terms is the `PenaltyManager`, which can be asked to provide a `CostEvaluator` of the form we saw above."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.306906Z", "iopub.status.busy": "2025-05-15T14:42:22.306733Z", "iopub.status.idle": "2025-05-15T14:42:22.310343Z", "shell.execute_reply": "2025-05-15T14:42:22.309741Z"}}, "outputs": [], "source": ["from pyvrp import PenaltyManager\n", "\n", "pen_manager = PenaltyManager.init_from(INSTANCE)\n", "cost_evaluator = pen_manager.cost_evaluator()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["##### Population management\n", "\n", "We are nearly there.\n", "All we still need to provide is a `Population`, and a set of initial (random) solutions.\n", "Let's tackle the `Population`."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.312214Z", "iopub.status.busy": "2025-05-15T14:42:22.311888Z", "iopub.status.idle": "2025-05-15T14:42:22.314827Z", "shell.execute_reply": "2025-05-15T14:42:22.314240Z"}}, "outputs": [], "source": ["from pyvrp import Population\n", "from pyvrp.diversity import broken_pairs_distance\n", "\n", "pop = Population(broken_pairs_distance)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The population tracks the diversity of its solutions.\n", "Computing the diversity (dissimilarity) of two solutions can be done in several ways.\n", "Functions to do so are provided in `pyvrp.diversity`, and can be provided to the `Population`.\n", "Here, we use the `broken_pairs_distance`, which computes a number in $[0, 1]$ based on the number of dissimilar edges in the solutions.\n", "\n", "A new population starts off empty:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.316511Z", "iopub.status.busy": "2025-05-15T14:42:22.316174Z", "iopub.status.idle": "2025-05-15T14:42:22.318969Z", "shell.execute_reply": "2025-05-15T14:42:22.318402Z"}}, "outputs": [], "source": ["assert len(pop) == 0"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We can add new solutions to the population using `Population.add`.\n", "Recall that `sol` and `new_sol` are, respectively, infeasible and feasible solutions."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.320872Z", "iopub.status.busy": "2025-05-15T14:42:22.320524Z", "iopub.status.idle": "2025-05-15T14:42:22.323595Z", "shell.execute_reply": "2025-05-15T14:42:22.323135Z"}}, "outputs": [], "source": ["assert not sol.is_feasible()\n", "pop.add(sol, cost_evaluator)\n", "\n", "assert new_sol.is_feasible()\n", "pop.add(new_sol, cost_evaluator)\n", "\n", "assert len(pop) == 2\n", "assert pop.num_feasible() == 1\n", "assert pop.num_infeasible() == 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### The genetic algorithm and crossover"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["A set of initial solution can be constructed easily, by generating a list of random solutions."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.325308Z", "iopub.status.busy": "2025-05-15T14:42:22.324942Z", "iopub.status.idle": "2025-05-15T14:42:22.328962Z", "shell.execute_reply": "2025-05-15T14:42:22.328518Z"}}, "outputs": [], "source": ["init_sols = [Solution.make_random(INSTANCE, rng) for _ in range(25)]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We are now ready to construct the genetic algorithm.\n", "This object additionally takes a crossover operator from `pyvrp.crossover`.\n", "We will use the selective route exchange (SREX) method."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.330696Z", "iopub.status.busy": "2025-05-15T14:42:22.330348Z", "iopub.status.idle": "2025-05-15T14:42:22.333443Z", "shell.execute_reply": "2025-05-15T14:42:22.332952Z"}}, "outputs": [], "source": ["from pyvrp import GeneticAlgorithm\n", "from pyvrp.crossover import selective_route_exchange as srex\n", "\n", "algo = GeneticAlgorithm(\n", "    INSTANCE,\n", "    pen_manager,\n", "    rng,\n", "    pop,\n", "    ls,\n", "    srex,\n", "    init_sols,\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We can call `algo.run`, which iterates until a stopping criterion is met.\n", "These stopping criteria can be imported from `pyvrp.stop` - see [the API documentation](https://pyvrp.org/api/stop.html) for details."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:22.335153Z", "iopub.status.busy": "2025-05-15T14:42:22.334808Z", "iopub.status.idle": "2025-05-15T14:42:25.413710Z", "shell.execute_reply": "2025-05-15T14:42:25.413118Z"}}, "outputs": [], "source": ["from pyvrp.stop import MaxIterations, MaxRuntime\n", "\n", "iter_res = algo.run(stop=MaxIterations(500))\n", "time_res = algo.run(stop=MaxRuntime(1))  # seconds"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Let's investigate the solutions!"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:25.415789Z", "iopub.status.busy": "2025-05-15T14:42:25.415413Z", "iopub.status.idle": "2025-05-15T14:42:25.418391Z", "shell.execute_reply": "2025-05-15T14:42:25.417803Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solution results\n", "================\n", "    # routes: 4\n", "     # trips: 4\n", "   # clients: 100\n", "   objective: 7761\n", "    distance: 7761\n", "    duration: 17761\n", "# iterations: 500\n", "    run-time: 2.07 seconds\n", "\n", "Routes\n", "------\n", "Route #1: 90 65 82 99 52 83 64 49 19 18 48 21 23 25 77 58 75 97 59 87 74 86 57 24 22 20 66\n", "Route #2: 94 92 95 67 62 50 34 31 29 27 26 28 30 32 33 76 89 63 85 51 84 56 91 80\n", "Route #3: 61 42 44 39 38 36 35 37 40 43 41 72 71 93 96 54 81\n", "Route #4: 69 98 88 2 6 7 79 73 78 12 14 47 17 16 15 13 9 11 10 53 60 8 46 4 45 5 3 1 70 100 55 68\n", "\n"]}], "source": ["print(iter_res)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:25.420111Z", "iopub.status.busy": "2025-05-15T14:42:25.419782Z", "iopub.status.idle": "2025-05-15T14:42:25.422972Z", "shell.execute_reply": "2025-05-15T14:42:25.422402Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solution results\n", "================\n", "    # routes: 4\n", "     # trips: 4\n", "   # clients: 100\n", "   objective: 7761\n", "    distance: 7761\n", "    duration: 17761\n", "# iterations: 285\n", "    run-time: 1.00 seconds\n", "\n", "Routes\n", "------\n", "Route #1: 90 65 82 99 52 83 64 49 19 18 48 21 23 25 77 58 75 97 59 87 74 86 57 24 22 20 66\n", "Route #2: 94 92 95 67 62 50 34 31 29 27 26 28 30 32 33 76 89 63 85 51 84 56 91 80\n", "Route #3: 61 42 44 39 38 36 35 37 40 43 41 72 71 93 96 54 81\n", "Route #4: 69 98 88 2 6 7 79 73 78 12 14 47 17 16 15 13 9 11 10 53 60 8 46 4 45 5 3 1 70 100 55 68\n", "\n"]}], "source": ["print(time_res)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## The `solve` function\n", "\n", "Let's put everything we have learned together into a `solve` function."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:25.424845Z", "iopub.status.busy": "2025-05-15T14:42:25.424501Z", "iopub.status.idle": "2025-05-15T14:42:25.428403Z", "shell.execute_reply": "2025-05-15T14:42:25.427942Z"}}, "outputs": [], "source": ["def solve(stop, seed):\n", "    rng = RandomNumberGenerator(seed=seed)\n", "    pm = PenaltyManager.init_from(INSTANCE)\n", "    pop = Population(broken_pairs_distance)\n", "\n", "    neighbours = compute_neighbours(INSTANCE)\n", "    ls = LocalSearch(INSTANCE, rng, neighbours)\n", "\n", "    for node_op in NODE_OPERATORS:\n", "        ls.add_node_operator(node_op(INSTANCE))\n", "\n", "    for route_op in ROUTE_OPERATORS:\n", "        ls.add_route_operator(route_op(INSTANCE))\n", "\n", "    init = [Solution.make_random(INSTANCE, rng) for _ in range(25)]\n", "    algo = GeneticAlgorithm(INSTANCE, pm, rng, pop, ls, srex, init)\n", "\n", "    return algo.run(stop)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Very good.\n", "Let's solve the instance again, now using the `solve` function."]}, {"cell_type": "code", "execution_count": 19, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:25.430120Z", "iopub.status.busy": "2025-05-15T14:42:25.429775Z", "iopub.status.idle": "2025-05-15T14:42:29.357830Z", "shell.execute_reply": "2025-05-15T14:42:29.357253Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solution results\n", "================\n", "    # routes: 4\n", "     # trips: 4\n", "   # clients: 100\n", "   objective: 7761\n", "    distance: 7761\n", "    duration: 17761\n", "# iterations: 1000\n", "    run-time: 3.92 seconds\n", "\n", "Routes\n", "------\n", "Route #1: 90 65 82 99 52 83 64 49 19 18 48 21 23 25 77 58 75 97 59 87 74 86 57 24 22 20 66\n", "Route #2: 94 92 95 67 62 50 34 31 29 27 26 28 30 32 33 76 89 63 85 51 84 56 91 80\n", "Route #3: 61 42 44 39 38 36 35 37 40 43 41 72 71 93 96 54 81\n", "Route #4: 69 98 88 2 6 7 79 73 78 12 14 47 17 16 15 13 9 11 10 53 60 8 46 4 45 5 3 1 70 100 55 68\n", "\n"]}], "source": ["res = solve(stop=MaxIterations(1000), seed=1)\n", "print(res)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["PyVRP also provides many plotting tools that can be used to investigate a data instance or solution result."]}, {"cell_type": "code", "execution_count": 20, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:29.359680Z", "iopub.status.busy": "2025-05-15T14:42:29.359488Z", "iopub.status.idle": "2025-05-15T14:42:29.734304Z", "shell.execute_reply": "2025-05-15T14:42:29.733678Z"}}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "from pyvrp.plotting import plot_result"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:29.736625Z", "iopub.status.busy": "2025-05-15T14:42:29.736141Z", "iopub.status.idle": "2025-05-15T14:42:30.420863Z", "shell.execute_reply": "2025-05-15T14:42:30.420241Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = plt.figure(figsize=(12, 8))\n", "\n", "plot_result(res, INSTANCE, fig=fig)\n", "plt.tight_layout()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The top-left figure shows the average diversity of the feasible and infeasible populations.\n", "The periodic spikes are due to survivor selection: when the population grows too large, bad solutions are purged.\n", "It is clear from this figure that periodic survivor selection improves diversity.\n", "The middle-left figure shows the best and average objectives of both sub-populations, which improve over time as the search progresses.\n", "The bottom-left figure shows average iteration runtimes (in seconds).\n", "Finally, the figure on the right plots the best observed solution."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook we have used some of the different components in PyVRP to implement our own `solve` function.\n", "Along the way we learned about how PyVRP works internally.\n", "\n", "The components we saw in this notebook can also be used to create different search algorithms altogether.\n", "For example, our `LocalSearch` search method could be used to quickly implement an iterated local search scheme.\n", "This modularity allows for a lot of reuse of the PyVRP package."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}