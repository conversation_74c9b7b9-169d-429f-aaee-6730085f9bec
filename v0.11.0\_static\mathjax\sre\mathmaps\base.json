{"base/rules/clearspeak_base.min": {"locale": "base", "domain": "clearspeak", "modality": "speech", "kind": "abstract", "rules": [["Rule", "direct-speech", "default", "[t] @ext-speech", "self::*[@ext-speech]", "priority=Infinity"], ["Rule", "stree", "default", "[n] ./*[1]", "self::stree"], ["Rule", "unknown", "default", "[n] text()", "self::unknown"], ["Rule", "protected", "default", "[n] text() (grammar:ignoreCaps)", "self::number", "contains(@grammar, \"protected\")"], ["Rule", "omit-empty", "default", "[p] (pause:short)", "self::empty"], ["Rule", "omit-font", "default", "[n] self::* (grammar:ignoreFont=@font)", "self::identifier[@font=\"italic\"]", "string-length(text())=1", "not(contains(@grammar, \"ignoreFont\"))"], ["SpecializedRule", "omit-font", "default", "Caps_SayCaps"], ["Rule", "text", "default", "[n] text()", "self::text"], ["Rule", "capital", "default", "[n] text() (pitch:0.6, grammar:ignoreCaps)", "self::identifier", "@role=\"latinletter\" or @role=\"greekletter\" or @role=\"simple function\"", ".[@category=\"Lu\"]"], ["Rule", "capital-cap", "Caps_SayCaps", "[n] text()", "self::identifier", "@role=\"latinletter\" or @role=\"greekletter\"", ".[@category=\"Lu\"]"], ["Rule", "capital-cap-l", "Caps_SayCaps", "[p] (pause:short); [n] text()", "self::identifier", "@role=\"latinletter\" or @role=\"greekletter\"", ".[@category=\"Lu\"]", "preceding-sibling::*[1]", "not(name(preceding-sibling::*[1])=\"function\")", "not(contains(@grammar, \"angle\"))"], ["Rule", "capital-cap-r", "Caps_SayCaps", "[n] text() (pause:short)", "self::identifier", "@role=\"latinletter\" or @role=\"greekletter\"", ".[@category=\"Lu\"]", "following-sibling::*[1]"], ["Rule", "capital-cap-lr", "Caps_SayCaps", "[p] (pause:short); [n] text() (pause:short)", "self::identifier", "@role=\"latinletter\" or @role=\"greekletter\"", ".[@category=\"Lu\"]", "preceding-sibling::*[1]", "following-sibling::*[1]", "not(name(preceding-sibling::*[1])=\"function\")", "not(contains(@grammar, \"angle\"))"], ["Precondition", "collapsed", "default", "self::*[@alternative]", "not(contains(@grammar, \"collapsed\"))"], ["Precondition", "font", "default", "self::*", "@font", "not(contains(@grammar, \"ignoreFont\"))", "@font!=\"normal\""], ["Specialized", "font", "default", "Caps_SayCaps"], ["<PERSON><PERSON>", "font", "self::identifier[@font=\"normal\"]", "string-length(text())=1", "not(contains(@grammar, \"ignoreFont\"))", "@role!=\"unit\""], ["Precondition", "punctuation-lr", "default", "self::punctuation", "@role=\"comma\""], ["Precondition", "punctuation", "default", "self::punctuation", "@role=\"comma\"", "not(preceding-sibling::*[1]/children)", "not(following-sibling::*[1]/children)"], ["Precondition", "punctuation-l", "default", "self::punctuation", "@role=\"comma\"", "not(following-sibling::*[1]/children)"], ["Precondition", "punctuation-r", "default", "self::punctuation", "@role=\"comma\"", "not(preceding-sibling::*[1]/children)"], ["Precondition", "ellipsis", "Ellipses_AndSoOn", "self::punctuation", "@role=\"ellipsis\"", "not(following-sibling::*[1])", "not(preceding-sibling::*[last()][@role=\"ellipsis\"])"], ["Precondition", "ellipsis-andsoon", "Ellipses_AndSoOn", "self::punctuation", "@role=\"ellipsis\"", "preceding-sibling::*[1]", "following-sibling::*[1]"], ["Precondition", "vbar-evaluated", "default", "self::punctuated", "@role=\"endpunct\"", "content/*[1][@role=\"vbar\"]", "content/*[1][@embellished]", "name(content/*[1])=\"subscript\""], ["Precondition", "vbar-evaluated-both", "default", "self::punctuated", "@role=\"endpunct\"", "content/*[1][@role=\"vbar\"]", "content/*[1][@embellished]", "name(content/*[1])=\"superscript\"", "name(content/*[1]/children/*[1])=\"subscript\""], ["Precondition", "vbar-such-that", "VerticalLine_SuchThat", "self::punctuation", "@role=\"vbar\"", "not(parent::*/parent::*[@embellished=\"punctuation\"])"], ["Precondition", "vbar-divides", "default", "self::punctuation", "@role=\"vbar\"", "not(parent::*/parent::*[@embellished=\"punctuation\"])", "parent::*/parent::*[@role=\"sequence\"]"], ["Precondition", "vbar-always-divides", "VerticalLine_Divides", "self::punctuation", "@role=\"vbar\"", "not(parent::*/parent::*[@embellished=\"punctuation\"])"], ["Precondition", "vbar-given", "VerticalLine_Given", "self::punctuation", "@role=\"vbar\"", "not(parent::*/parent::*[@embellished=\"punctuation\"])"], ["Precondition", "member", "default", "self::operator", "@role=\"element\""], ["Precondition", "member-member", "SetMemberSymbol_Member", "self::operator", "@role=\"element\""], ["Precondition", "member-element", "SetMemberSymbol_Element", "self::operator", "@role=\"element\""], ["Precondition", "member-in", "SetMemberSymbol_In", "self::operator", "@role=\"element\""], ["Precondition", "member-belongs", "SetMemberSymbol_Belongs", "self::operator", "@role=\"element\""], ["Precondition", "not-member", "default", "self::operator", "@role=\"nonelement\""], ["Precondition", "not-member-member", "SetMemberSymbol_Member", "self::operator", "@role=\"nonelement\""], ["Precondition", "not-member-element", "SetMemberSymbol_Element", "self::operator", "@role=\"nonelement\""], ["Precondition", "not-member-in", "SetMemberSymbol_In", "self::operator", "@role=\"nonelement\""], ["Precondition", "not-member-belongs", "SetMemberSymbol_Belongs", "self::operator", "@role=\"nonelement\""], ["Precondition", "set-member", "default", "self::operator", "@role=\"element\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-member", "SetMemberSymbol_Member", "self::operator", "@role=\"element\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-element", "SetMemberSymbol_Element", "self::operator", "@role=\"element\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-in", "SetMemberSymbol_In", "self::operator", "@role=\"element\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-belongs", "SetMemberSymbol_Belongs", "self::operator", "@role=\"element\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member", "default", "self::operator", "@role=\"nonelement\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-member", "SetMemberSymbol_Member", "self::operator", "@role=\"nonelement\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-element", "SetMemberSymbol_Element", "self::operator", "@role=\"nonelement\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-in", "SetMemberSymbol_In", "self::operator", "@role=\"nonelement\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-belongs", "SetMemberSymbol_Belongs", "self::operator", "@role=\"nonelement\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "prime", "default", "self::superscript", "children/*[2]", "children/*[2][@role=\"prime\"]", "self::*"], ["Precondition", "degrees", "default", "self::punctuated[@role=\"sequence\"]", "content/*[1][@role=\"degree\"]"], ["Precondition", "feet", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"number\"", "children/*[2][text()=\"′\"]", "not(contains(@grammar, \"degree\"))"], ["Precondition", "foot", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"number\"", "children/*[2][text()=\"′\"]", "children/*[1][text()=\"1\"]", "not(contains(@grammar, \"degree\"))"], ["Precondition", "inches", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"number\"", "children/*[2][text()=\"″\"]", "not(contains(@grammar, \"degree\"))"], ["Precondition", "inch", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"number\"", "children/*[2][text()=\"″\"]", "children/*[1][text()=\"1\"]", "not(contains(@grammar, \"degree\"))"], ["Precondition", "minutes", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "contains(@grammar, \"degree\")"], ["Precondition", "minute", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "contains(@grammar, \"degree\")", "children/*[1][text()=\"1\"]"], ["Precondition", "seconds", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "contains(@grammar, \"degree\")"], ["Precondition", "second", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "contains(@grammar, \"degree\")", "children/*[1][text()=\"1\"]"], ["Precondition", "degrees-angle", "default", "self::punctuation", "@role=\"degree\""], ["Precondition", "degree-angle", "default", "self::punctuation", "@role=\"degree\"", "preceding-sibling::*[text()=\"1\"]"], ["Precondition", "minutes-angle", "<PERSON><PERSON><PERSON><PERSON>", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "not(contains(@grammar, \"degree\"))", "name(children/*[1])=\"number\" or (children/*[1][@role=\"latinletter\"] and \"\"=translate(children/*[1]/text(),\"abcdefghijklmnopqrstuvwxyz\", \"\"))"], ["Precondition", "minute-angle", "<PERSON><PERSON><PERSON><PERSON>", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "not(contains(@grammar, \"degree\"))", "children/*[1][text()=\"1\"]"], ["Precondition", "seconds-angle", "<PERSON><PERSON><PERSON><PERSON>", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "not(contains(@grammar, \"degree\"))", "name(children/*[1])=\"number\" or (children/*[1][@role=\"latinletter\"] and \"\"=translate(children/*[1]/text(),\"abcdefghijklmnopqrstuvwxyz\", \"\"))"], ["Precondition", "second-angle", "<PERSON><PERSON><PERSON><PERSON>", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "not(contains(@grammar, \"degree\"))", "children/*[1][text()=\"1\"]"], ["Precondition", "feet-length", "Prime_Length", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "not(contains(@grammar, \"degree\"))", "name(children/*[1])=\"number\" or (children/*[1][@role=\"latinletter\"] and \"\"=translate(children/*[1]/text(),\"abcdefghijklmnopqrstuvwxyz\", \"\"))"], ["Precondition", "foot-length", "Prime_Length", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "not(contains(@grammar, \"degree\"))", "children/*[1][text()=\"1\"]"], ["Precondition", "inches-length", "Prime_Length", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "not(contains(@grammar, \"degree\"))", "name(children/*[1])=\"number\" or (children/*[1][@role=\"latinletter\"] and \"\"=translate(children/*[1]/text(),\"abcdefghijklmnopqrstuvwxyz\", \"\"))"], ["Precondition", "inch-length", "Prime_Length", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "not(contains(@grammar, \"degree\"))", "children/*[1][text()=\"1\"]"], ["Precondition", "punctuated", "default", "self::punctuated"], ["Precondition", "function", "default", "self::function"], ["Precondition", "article", "default", "self::*[contains(@grammar, \"addArticle\")]", "not(contains(@grammar, \"noArticle\"))"], ["Precondition", "appl", "default", "self::appl"], ["Precondition", "appl-simple", "default", "self::appl", "@role=\"simple function\"", "name(children/*[2])=\"appl\""], ["Precondition", "appl-simple-fenced", "default", "self::appl", "@role=\"simple function\"", "name(children/*[2])=\"fenced\"", "name(children/*[2]/children/*[1])=\"appl\""], ["Precondition", "appl-times", "Functions_None", "self::appl"], ["Precondition", "function-prefix", "default", "self::appl", "@role=\"prefix function\""], ["Precondition", "binary-operation", "ImpliedTimes_MoreImpliedTimes", "self::appl", "@role=\"prefix function\"", "parent::*/parent::infixop[@role=\"implicit\"]", "following-sibling::*", "not(contains(@grammar, \"impliedTimes\"))"], ["Precondition", "function-prefix-simple-arg", "default", "self::appl", "@role=\"prefix function\"", "name(children/*[2])=\"fenced\"", "contains(children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\")", "name(children/*[2]/children/*[1])!=\"number\"", "name(children/*[2]/children/*[1])!=\"identifier\"", "name(children/*[2]/children/*[1])!=\"appl\""], ["Precondition", "function-prefix-embell", "default", "self::appl", "@role=\"prefix function\"", "name(children/*[1])!=\"function\""], ["Precondition", "function-prefix-fenced-or-frac-arg", "default", "self::appl", "@role=\"prefix function\"", "(name(children/*[2])=\"fenced\" and not(contains(children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\"))) or name(children/*[2])=\"fraction\" or (name(children/*[2])!=\"fenced\" and not(contains(children/*[2]/@annotation, \"clearspeak:simple\")))", "self::*"], ["Precondition", "function-prefix-subscript", "default", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"subscript\"", "self::*"], ["Precondition", "function-ln", "default", "self::appl", "@role=\"prefix function\"", "content/*[2][text()=\"ln\"]", "not(following-sibling::*)", "not(contains(@grammar, \"NatLog\"))"], ["Precondition", "function-ln-pause", "default", "self::appl", "@role=\"prefix function\"", "content/*[2][text()=\"ln\"]", "not(contains(@grammar, \"NatLog\"))"], ["Precondition", "function-ln-of", "default", "self::appl", "@role=\"prefix function\"", "content/*[2][text()=\"ln\"]", "name(children/*[2])=\"fenced\"", "not(contains(@grammar, \"NatLog\"))"], ["Precondition", "function-ln-natlog", "Log_LnAsNaturalLog", "self::appl", "@role=\"prefix function\"", "content/*[2][text()=\"ln\"]", "not(following-sibling::*)", "not(contains(@grammar, \"NatLog\"))"], ["Precondition", "function-ln-natlog-pause", "Log_LnAsNaturalLog", "self::appl", "@role=\"prefix function\"", "content/*[2][text()=\"ln\"]", "not(contains(@grammar, \"NatLog\"))"], ["Precondition", "function-prefix-as-exp", "default", "self::appl", "@role=\"prefix function\"", "name(parent::*/parent::*)=\"superscript\"", "not(following-sibling::*)", "(name(children/*[2])=\"fenced\" and not(contains(children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\"))) or name(children/*[2])=\"fraction\" or (name(children/*[2])!=\"fenced\" and not(contains(children/*[2]/@annotation, \"clearspeak:simple\")))"], ["Precondition", "function-prefix-subscript-as-exp", "default", "self::appl", "@role=\"prefix function\"", "name(parent::*/parent::*)=\"superscript\"", "not(following-sibling::*)", "name(children/*[1])=\"subscript\""], ["Precondition", "function-prefix-hyper", "default", "self::appl", "@role=\"prefix function\"", "children/*[1][@category=\"Hyperbolic\"]"], ["Precondition", "function-prefix-inverse", "default", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "appl-triginverse", "Trig_TrigInverse", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]"], ["Precondition", "function-prefix-arc-simple", "Trig_ArcTrig", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "function-prefix-arc-simple-fenced", "Trig_ArcTrig", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "name(children/*[2])=\"fenced\"", "children/*[2]/children/*[1][@role=\"prefix function\"]", "contains(children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\")", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "function-prefix-arc", "Trig_ArcTrig", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))", "(name(children/*[2])=\"fenced\" and not(contains(children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\"))) or (name(children/*[2])=\"fraction\" and children/*[2][@role!=\"vulgar\"])"], ["Precondition", "function-inverse", "default", "self::superscript", "@role=\"prefix function\" or @role=\"simple function\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "superscript-prefix-function", "default", "self::superscript", "@role=\"prefix function\"", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]"], ["<PERSON><PERSON>", "superscript-prefix-function", "self::superscript[@role=\"prefix function\"]", "name(children/*[2])=\"identifier\""], ["Precondition", "function-no-inverse", "Functions_None", "self::superscript", "@role=\"prefix function\" or @role=\"simple function\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "superscript", "default", "self::superscript"], ["Precondition", "superscript-simple-exponent", "default", "self::superscript", "not(descendant::superscript)"], ["Precondition", "superscript-simple-exponent-end", "default", "self::superscript", "not(descendant::superscript)", "not(following-sibling::*)"], ["<PERSON><PERSON>", "superscript-simple-exponent", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"number\"", "contains(children/superscript/children/*[1]/@annotation, \"clearspeak:simple\")"], ["<PERSON><PERSON>", "superscript-simple-exponent", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"fraction\"", "contains(children/superscript/children/*[1]/@annotation, \"clearspeak:simple\")"], ["<PERSON><PERSON>", "superscript-simple-exponent", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"identifier\""], ["<PERSON><PERSON>", "superscript-simple-exponent", "self::superscript", "children/*[2][@role=\"implicit\"]", "count(children/*[2]/children/*)=2", "contains(children/*[2]/children/*[1]/@annotation, \"simple\")", "name(children/*[2]/children/*[2])=\"superscript\"", "(name(children/*[2]/children/*[2]/children/*[1])=\"number\" and contains(children/*[2]/children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\")) or name(children/*[2]/children/*[2]/children/*[1])=\"identifier\"", "children/*[2]/children/*[2]/children/*[2][text()=\"2\"] or children/*[2]/children/*[2]/children/*[2][text()=\"3\"]"], ["Precondition", "superscript-ordinal", "default", "self::superscript", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]"], ["<PERSON><PERSON>", "superscript-ordinal", "self::superscript", "name(children/*[2])=\"identifier\"", "children/*[2][@role=\"latinletter\" or @role=\"greekletter\" or @role=\"otherletter\"]"], ["Precondition", "superscript-non-ordinal", "default", "self::superscript", "children/*[2][@role=\"negative\"]", "name(children/*[2]/children/*[1])=\"number\"", "children/*[2]/children/*[1][@role=\"integer\"]"], ["Precondition", "superscript-simple-function", "default", "self::superscript", "name(children/*[1])=\"identifier\"", "children/*[1][@role=\"simple function\"]", "children/*[2][@role!=\"prime\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "superscript-simple-function-none", "Functions_None", "self::superscript", "name(children/*[1])=\"identifier\"", "children/*[1][@role=\"simple function\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "superscript-ordinal-number", "Exponent_Ordinal", "self::superscript", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]"], ["<PERSON><PERSON>", "superscript-ordinal-number", "self::superscript", "name(children/*[2])=\"identifier\"", "children/*[2][@role=\"latinletter\" or @role=\"greekletter\" or @role=\"otherletter\"]"], ["Precondition", "superscript-ordinal-negative", "Exponent_Ordinal", "self::superscript", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "name(children/*[2]/children/*[1])=\"number\"", "children/*[2]/children/*[1][@role=\"integer\"]"], ["Precondition", "superscript-ordinal-default", "Exponent_Ordinal", "self::superscript", "children//superscript"], ["Precondition", "superscript-ordinal-power-number", "Exponent_OrdinalPower", "self::superscript", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]"], ["Precondition", "superscript-ordinal-power-negative", "Exponent_OrdinalPower", "self::superscript", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "name(children/*[2]/children/*[1])=\"number\"", "children/*[2]/children/*[1][@role=\"integer\"]"], ["Precondition", "superscript-ordinal-power-identifier", "Exponent_OrdinalPower", "self::superscript", "name(children/*[2])=\"identifier\"", "children/*[2][@role=\"latinletter\" or @role=\"greekletter\" or @role=\"otherletter\"]"], ["Precondition", "superscript-ordinal-power-default", "Exponent_OrdinalPower", "self::superscript", "children//superscript"], ["Precondition", "superscript-power", "Exponent_AfterPower", "self::superscript"], ["Precondition", "superscript-power-default", "Exponent_AfterPower", "self::superscript", "children//superscript"], ["Precondition", "exponent", "default", "self::identifier", "contains(@grammar, \"ordinal\")"], ["Precondition", "exponent-number", "default", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()!=\"0\"", "not(contains(@annotation, \"general:basenumber\"))"], ["Precondition", "exponent-ordinal", "Exponent_Ordinal", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()!=\"0\"", "not(contains(@annotation, \"general:basenumber\"))"], ["Precondition", "exponent-ordinal-zero", "Exponent_Ordinal", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()=\"0\""], ["Precondition", "exponent-ordinalpower", "Exponent_OrdinalPower", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()!=\"0\"", "not(contains(@annotation, \"general:basenumber\"))"], ["Precondition", "exponent-ordinalpower-zero", "Exponent_OrdinalPower", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()=\"0\""], ["Precondition", "square", "default", "self::superscript", "@role!=\"unit\"", "children/*[2][text()=\"2\"]", "name(children/*[1])!=\"text\" or not(name(children/*[1])=\"text\" and (name(../../../punctuated[@role=\"text\"]/..)=\"stree\" or name(..)=\"stree\"))", "self::*"], ["Precondition", "cube", "default", "self::superscript", "@role!=\"unit\"", "children/*[2][text()=\"3\"]", "name(children/*[1])!=\"text\" or not(name(children/*[1])=\"text\" and (name(../../../punctuated[@role=\"text\"]/..)=\"stree\" or name(..)=\"stree\"))", "self::*"], ["Precondition", "paren-simple", "default", "self::fenced", "@role=\"leftright\"", "contains(children/*[1]/@annotation, \"clearspeak:simple\")", "name(../..)!=\"superscript\" and name(../..)!=\"subscript\""], ["Precondition", "paren-simple-exp", "default", "self::fenced", "@role=\"leftright\"", "name(../..)=\"superscript\"", "children/*[1][@role=\"integer\"] or children/*[1][@role=\"float\"] or (children/*[1][@role=\"vulgar\"] and contains(children/*[1]/@annotation, \"clearspeak:simple\")) or children/*[1][@role=\"latinletter\"] or children/*[1][@role=\"greekletter\"] or children/*[1][@role=\"otherletter\"]"], ["Precondition", "paren-simple-nested-func", "default", "self::fenced", "@role=\"leftright\"", "name(../*[1])=\"identifier\" or name(../*[1])=\"function\"", "parent::*/parent::*[@role=\"simple function\" or @role=\"prefix function\"]", "children/*[1][@role=\"simple function\" or @role=\"prefix function\"]", "contains(children/*[1]/children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\") or name(children/*[1]/children/*[2]/children/*[1])=\"subscript\" or name(children/*[1]/children/*[2]/children/*[1])=\"superscript\" or children/*[1]/children/*[2]/children/*[1][@role=\"vulgar\"] "], ["Precondition", "paren-simple-nested-func-no-bracket", "Functions_None", "self::fenced", "@role=\"leftright\"", "name(../*[1])=\"identifier\" or name(../*[1])=\"function\"", "parent::*/parent::*[@role=\"simple function\" or @role=\"prefix function\"]", "children/*[1][@role=\"simple function\" or @role=\"prefix function\"]", "name(children/*[1]/children/*[1])=\"identifier\" or name(children/*[1]/children/*[1])=\"function\"", "contains(children/*[1]/children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\")", "name(children/*[1]/children/*[2]/children/*[1])=\"identifier\" or name(children/*[1]/children/*[2]/children/*[1])=\"number\""], ["Precondition", "fences-open-close", "default", "self::fenced", "@role=\"leftright\""], ["Specialized", "fences-open-close", "default", "self::fenced", "@role=\"metric\""], ["Precondition", "paren-simple-nested-func-default", "default", "self::fenced", "@role=\"leftright\"", "name(../*[1])=\"identifier\" or name(../*[1])=\"function\"", "parent::*/parent::*[@role=\"simple function\" or @role=\"prefix function\"]", "not(contains(children/*[1]/@annotation, \"clearspeak:simple\"))"], ["Precondition", "paren-simple-nested-func-none", "Functions_None", "self::fenced", "@role=\"leftright\"", "name(../*[1])=\"identifier\" or name(../*[1])=\"function\"", "parent::*/parent::*[@role=\"simple function\" or @role=\"prefix function\"]", "children/*[1][@role=\"simple function\" or @role=\"prefix function\"]", "contains(children/*[1]/children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\") or name(children/*[1]/children/*[2]/children/*[1])=\"subscript\" or name(children/*[1]/children/*[2]/children/*[1])=\"superscript\" or children/*[1]/children/*[2]/children/*[1][@role=\"vulgar\"] "], ["Specialized", "fences-open-close", "default", "<PERSON><PERSON>_<PERSON>"], ["<PERSON><PERSON>", "fences-open-close", "self::fenced", "@role=\"composed function\""], ["Precondition", "fence-silent", "<PERSON><PERSON>_<PERSON>", "self::fenced"], ["Precondition", "fences-open-close-none", "ImpliedTimes_None", "self::fenced", "@role=\"leftright\"", "parent::*/parent::*[@role!=\"simple function\"]", "parent::*/parent::*[@role!=\"prefix function\"]"], ["Precondition", "fence-nesting", "<PERSON><PERSON>_SpeakNestingLevel", "self::fence", "contains(@grammar, \"spokenFence\")", "CQFmatchingFences"], ["Precondition", "fence-no-nesting", "<PERSON><PERSON>_SpeakNestingLevel", "self::fence"], ["Precondition", "fences-points", "Paren_CoordPoint", "self::fenced", "name(children/*[1])=\"punctuated\"", "children/*[1][@role=\"sequence\"]"], ["Precondition", "fences-interval", "<PERSON><PERSON>_Interval", "self::fenced", "not(contains(@grammar, \"interval\"))", "name(children/*[1])=\"punctuated\"", "children/*[1][@role=\"sequence\"]", "count(./children/*[1]/content/*)=1", "children/*[1]/content/*[1][@role=\"comma\"]"], ["Precondition", "interval-open", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\")\""], ["Precondition", "interval-closed-open", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"[\"", "content/*[2]/text()=\")\""], ["Precondition", "interval-open-closed", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\"]\""], ["Precondition", "interval-closed", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"[\"", "content/*[2]/text()=\"]\""], ["Precondition", "interval-open-inf-r", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\")\"", "children/*[1]/children/*[3]/text()=\"∞\" or (name(children/*[1]/children/*[3])=\"prefixop\" and children/*[1]/children/*[3]/children/*[1]/text()=\"∞\")"], ["Precondition", "interval-open-inf-l", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\")\"", "children/*[1]/children/*[1]/text()=\"∞\" or (name(children/*[1]/children/*[1])=\"prefixop\" and children/*[1]/children/*[1]/children/*[1]/text()=\"∞\")"], ["Precondition", "interval-open-inf-lr", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\")\"", "children/*[1]/children/*[3]/text()=\"∞\" or (name(children/*[1]/children/*[3])=\"prefixop\" and children/*[1]/children/*[3]/children/*[1]/text()=\"∞\")", "children/*[1]/children/*[1]/text()=\"∞\" or (name(children/*[1]/children/*[1])=\"prefixop\" and children/*[1]/children/*[1]/children/*[1]/text()=\"∞\")"], ["Precondition", "interval-closed-open-inf", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"[\"", "content/*[2]/text()=\")\"", "children/*[1]/children/*[3]/text()=\"∞\" or (name(children/*[1]/children/*[3])=\"prefixop\" and children/*[1]/children/*[3]/children/*[1]/text()=\"∞\")"], ["Precondition", "interval-open-closed-inf", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\"]\"", "children/*[1]/children/*[1]/text()=\"∞\" or (name(children/*[1]/children/*[1])=\"prefixop\" and children/*[1]/children/*[1]/children/*[1]/text()=\"∞\")"], ["Precondition", "paren-nested-embellished-funcs", "Functions_None", "self::fenced", "@role=\"leftright\"", "name(../..)=\"appl\"", "name(children/*[1]) = \"appl\"", "preceding-sibling::*/descendant-or-self::*[@role=\"subsup\"] or children/*[1]/descendant-or-self::*[@role=\"subsup\"]"], ["Precondition", "set-empty", "default", "self::fenced", "@role=\"set empty\""], ["Precondition", "set-extended", "default", "self::fenced", "@role=\"set extended\""], ["Precondition", "set-collection", "default", "self::fenced", "@role=\"set collection\""], ["<PERSON><PERSON>", "set-collection", "self::fenced", "@role=\"set singleton\""], ["Precondition", "set-extended-woall", "Sets_woAll", "self::fenced", "@role=\"set extended\""], ["Precondition", "set-collection-<PERSON><PERSON><PERSON>", "Sets_SilentBracket", "self::fenced", "@role=\"set collection\""], ["Precondition", "subscript", "default", "self::subscript"], ["Precondition", "logarithm-base", "default", "self::subscript", "children/*[1][@category=\"Logarithm\"]"], ["Precondition", "subscript-index", "default", "self::subscript", "contains(@grammar, \"simpleDet\")"], ["Precondition", "fraction", "default", "self::fraction"], ["Precondition", "fraction-none", "Functions_None", "self::fraction", "name(children/*[1])=\"appl\" or name(children/*[2])=\"appl\""], ["Precondition", "simple-fraction", "default", "self::fraction", "contains(children/*[1]/@annotation, \"clearspeak:simple\") or contains(children/*[1]/@annotation, \"clearspeak:unit\")", "contains(children/*[2]/@annotation, \"clearspeak:simple\") or contains(children/*[2]/@annotation, \"clearspeak:unit\")"], ["Precondition", "simple-vulgar-fraction", "default", "self::fraction", "@role=\"vulgar\""], ["Precondition", "simple-text-fraction", "default", "self::fraction", "name(children/*[1])=\"text\"", "name(children/*[2])=\"text\""], ["<PERSON><PERSON>", "simple-text-fraction", "self::fraction", "name(children/*[1])=\"infixop\"", "children/*[1][@role=\"unit\"]", "name(children/*[2])=\"text\""], ["Precondition", "vulgar-fraction", "default", "self::fraction", "@role=\"vulgar\"", "CQFvulgarFractionSmall"], ["Precondition", "fraction-over", "Fraction_Over", "self::fraction"], ["Precondition", "fraction-overendfrac", "Fraction_OverEndFrac", "self::fraction"], ["Precondition", "fraction-fracover", "Fraction_FracOver", "self::fraction"], ["Precondition", "fraction-per", "Fraction_Per", "self::fraction"], ["Precondition", "fraction-general<PERSON><PERSON><PERSON>", "Fraction_GeneralEndFrac", "self::fraction"], ["Precondition", "fraction-general", "Fraction_General", "self::fraction"], ["Precondition", "simple-vulgar-fraction-ordinal", "Fraction_Ordinal", "self::fraction", "@role=\"vulgar\""], ["Precondition", "fraction-endfrac", "Fraction_EndFrac", "self::fraction", "not(contains(@grammar, \"endfrac\"))", "not(contains(children/*[1]/@annotation, \"clearspeak:unit\"))", "not(contains(children/*[2]/@annotation, \"clearspeak:unit\"))"], ["Precondition", "vulgar-fraction-endfrac", "Fraction_EndFrac", "self::fraction", "name(children/*[1])=\"fraction\"", "name(children/*[2])=\"fraction\"", "contains(children/*[1]/@annotation, \"clearspeak:simple\")", "contains(children/*[2]/@annotation, \"clearspeak:simple\")"], ["Precondition", "simple-vulgar-fraction-endfrac", "Fraction_EndFrac", "self::fraction", "@role=\"vulgar\"", "contains(@annotation, \"clearspeak:simple\")", "self::*"], ["Precondition", "sqrt", "default", "self::sqrt"], ["Precondition", "sqrt-nested", "default", "self::sqrt", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root"], ["Precondition", "negative-sqrt", "default", "self::prefixop", "@role=\"negative\"", "name(children/*[1])=\"sqrt\""], ["Precondition", "negative-sqrt-default", "default", "self::prefixop", "@role=\"negative\"", "name(children/*[1])=\"sqrt\"", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root"], ["Precondition", "sqrt-plus-minus", "Roots_PosNegSqRoot", "self::sqrt", "parent::stree or not(parent::*/parent::infixop[@role=\"addition\"]) or (parent::*/parent::*[1]/text()!=\"±\" and parent::*/parent::*/text()!=\"∓\")"], ["Precondition", "sqrt-nested-plus-minus", "Roots_PosNegSqRoot", "self::sqrt", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root", "parent::stree or not(parent::*/parent::infixop[@role=\"addition\"]) or (parent::*/parent::*[1]/text()!=\"±\" and parent::*/parent::*/text()!=\"∓\")"], ["Precondition", "sqrt-plus-minus-posnegsqrootend", "Roots_PosNegSqRootEnd", "self::sqrt", "parent::stree or not(parent::*/parent::infixop[@role=\"addition\"]) or (parent::*/parent::*[1]/text()!=\"±\" and parent::*/parent::*/text()!=\"∓\")"], ["Precondition", "sqrt-nested-plus-minus-posnegsqrootend", "Roots_PosNegSqRootEnd", "self::sqrt", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root", "parent::stree or not(parent::*/parent::infixop[@role=\"addition\"]) or (parent::*/parent::*[1]/text()!=\"±\" and parent::*/parent::*/text()!=\"∓\")"], ["Precondition", "sqrt-endroot", "Roots_RootEnd", "self::sqrt", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "negative-sqrt-endroot", "Roots_RootEnd", "self::prefixop", "@role=\"negative\"", "name(children/*[1])=\"sqrt\"", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "sqrt-posnegsqrootend", "Roots_PosNegSqRootEnd", "self::sqrt", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "negative-sqrt-posnegsqrootend", "Roots_PosNegSqRootEnd", "self::prefixop", "@role=\"negative\"", "name(children/*[1])=\"sqrt\"", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "cubic", "default", "self::root", "children/*[1][text()=\"3\"]"], ["Precondition", "cubic-nested", "default", "self::root", "children/*[1][text()=\"3\"]", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root"], ["Precondition", "root", "default", "self::root"], ["Precondition", "root-nested", "default", "self::root", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root"], ["Precondition", "root-endroot", "Roots_RootEnd", "self::root", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "root-posnegsqrootend", "Roots_PosNegSqRootEnd", "self::root", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "negative", "default", "self::prefixop", "@role=\"negative\""], ["Precondition", "positive", "default", "self::prefixop", "@role=\"positive\""], ["Precondition", "angle-measure", "default", "self::infixop", "content/*[1]/text()=\"∠\"", "children/*[1][text()=\"m\"]"], ["Precondition", "prefix", "default", "self::prefixop"], ["Precondition", "postfix", "default", "self::postfixop"], ["Precondition", "set-prefix-operators", "default", "self::*", "contains(@grammar,\"prefix\")", "descendant-or-self::*/text()=\"∩\" or descendant-or-self::*/text()=\"∪\"", "self::*", "self::*", "self::*"], ["Precondition", "binary-operation-default", "default", "self::infixop"], ["Precondition", "division", "default", "self::infixop", "@role=\"division\"", "count(children/*)=2"], ["Precondition", "binary-operation-moreimpliedtimes", "ImpliedTimes_MoreImpliedTimes", "self::infixop", "@role=\"implicit\""], ["Precondition", "binary-operation-pause", "default", "self::infixop", "@role=\"implicit\"", "name(children/*[1])=\"appl\""], ["Precondition", "binary-operation-pause-r", "default", "self::infixop", "@role=\"implicit\"", "name(children/*[last()])=\"appl\""], ["Precondition", "binary-operation-pause-lr", "default", "self::infixop", "@role=\"implicit\"", "name(children/*[1])=\"appl\"", "name(children/*[last()])=\"appl\""], ["Precondition", "implicit-times", "default", "self::operator", "@role=\"multiplication\"", "text()=\"⁢\""], ["Precondition", "implicit-times-default", "default", "self::operator", "@role=\"multiplication\"", "text()=\"⁢\"", "CQFsimpleArguments"], ["Precondition", "implicit-times-simple", "default", "self::operator", "@role=\"multiplication\"", "text()=\"⁢\"", "CQFfencedArguments"], ["Precondition", "implicit-times-moreimpliedtimes", "ImpliedTimes_MoreImpliedTimes", "self::operator", "@role=\"multiplication\"", "text()=\"⁢\""], ["Precondition", "implicit-times-none", "ImpliedTimes_None", "self::operator", "@role=\"multiplication\"", "text()=\"⁢\""], ["Precondition", "binary-operation-simple", "default", "self::infixop", "@role=\"implicit\"", "contains(@annotation, \"clearspeak:simple\")", "not(contains(@grammar, \"inFrac\"))"], ["Precondition", "simple-in-fraction", "default", "self::*", "contains(@annotation, \"clearspeak:simple\")", "not(contains(@grammar, \"inFrac\"))", "name(.)!=\"identifier\"", "name(.)!=\"function\"", "name(.)!=\"number\"", "name(parent::*/parent::*)=\"fraction\"", "not(preceding-sibling::*)"], ["Precondition", "operators-after-power", "Exponent_AfterPower", "self::infixop", "@role=\"implicit\"", "contains(@grammar, \"afterPower\")"], ["Precondition", "relseq", "default", "self::relseq"], ["Precondition", "mult<PERSON>", "default", "self::multirel"], ["Precondition", "natural-numbers", "default", "self::identifier[@role=\"numbersetletter\"]", "text()=\"ℕ\" or (text()=\"N\" and @font=\"double-struck\")"], ["Precondition", "integers", "default", "self::identifier[@role=\"numbersetletter\"]", "text()=\"ℤ\" or (text()=\"Z\" and @font=\"double-struck\")"], ["Precondition", "rational-numbers", "default", "self::identifier[@role=\"numbersetletter\"]", "text()=\"ℚ\" or (text()=\"Q\" and @font=\"double-struck\")"], ["Precondition", "real-numbers", "default", "self::identifier[@role=\"numbersetletter\"]", "text()=\"ℝ\" or (text()=\"R\" and @font=\"double-struck\")"], ["Precondition", "complex-numbers", "default", "self::identifier[@role=\"numbersetletter\"]", "text()=\"ℂ\" or (text()=\"C\" and @font=\"double-struck\")"], ["Precondition", "natural-numbers-super", "default", "self::superscript", "children/*[1]/text()=\"ℕ\" or (children/*[1]/text()=\"N\" and children/*[1]/@font=\"double-struck\")", "self::*", "self::*", "self::*"], ["Precondition", "integers-super", "default", "self::superscript", "children/*[1]/text()=\"ℤ\" or (children/*[1]/text()=\"Z\" and children/*[1]/@font=\"double-struck\")", "self::*", "self::*", "self::*"], ["Precondition", "rational-numbers-super", "default", "self::superscript", "children/*[1]/text()=\"ℚ\" or (children/*[1]/text()=\"Q\" and children/*[1]/@font=\"double-struck\")", "self::*", "self::*", "self::*"], ["Precondition", "real-numbers-super", "default", "self::superscript", "children/*[1]/text()=\"ℝ\" or (children/*[1]/text()=\"R\" and children/*[1]/@font=\"double-struck\")", "self::*", "self::*", "self::*"], ["Precondition", "complex-numbers-super", "default", "self::superscript", "children/*[1]/text()=\"ℂ\" or (children/*[1]/text()=\"C\" and children/*[1]/@font=\"double-struck\")", "self::*", "self::*", "self::*"], ["Precondition", "natural-numbers-with-zero", "default", "self::subscript", "children/*[1]/text()=\"ℕ\" or (children/*[1]/text()=\"N\" and children/*[1]/@font=\"double-struck\")", "children/*[2]/text()=\"0\""], ["Precondition", "positive-integers", "default", "self::superscript", "children/*[1]/text()=\"ℤ\" or (children/*[1]/text()=\"Z\" and children/*[1]/@font=\"double-struck\")", "children/*[2]/text()=\"+\"", "self::*", "self::*", "self::*"], ["Precondition", "negative-integers", "default", "self::superscript", "children/*[1]/text()=\"ℤ\" or (children/*[1]/text()=\"Z\" and children/*[1]/@font=\"double-struck\")", "children/*[2]/text()=\"-\"", "self::*", "self::*", "self::*"], ["Precondition", "positive-rational-numbers", "default", "self::superscript", "children/*[1]/text()=\"ℚ\" or (children/*[1]/text()=\"Q\" and children/*[1]/@font=\"double-struck\")", "children/*[2]/text()=\"+\"", "self::*", "self::*", "self::*"], ["Precondition", "negative-rational-numbers", "default", "self::superscript", "children/*[1]/text()=\"ℚ\" or (children/*[1]/text()=\"Q\" and children/*[1]/@font=\"double-struck\")", "children/*[2]/text()=\"-\"", "self::*", "self::*", "self::*"], ["Precondition", "fences-neutral", "default", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-neutral-absend", "AbsoluteValue_AbsEnd", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-neutral-cardinality", "AbsoluteValue_Cardinality", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-neutral-determinant", "AbsoluteValue_Determinant", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-metric", "default", "self::fenced", "@role=\"metric\""], ["Precondition", "fences-metric-absend", "AbsoluteValue_AbsEnd", "self::fenced", "@role=\"metric\""], ["Precondition", "matrix", "default", "self::matrix"], ["Precondition", "matrix-simple", "default", "self::matrix", "count(children/*)<4", "count(children/*[1]/children/*)<4", "CQFcellsSimple"], ["Precondition", "matrix-trivial", "default", "self::vector", "@role=\"squarematrix\""], ["Precondition", "determinant", "default", "self::matrix", "@role=\"determinant\"", "count(children/*)<4", "CQFcellsSimple"], ["Precondition", "determinant-simple", "default", "self::matrix", "@role=\"determinant\""], ["Precondition", "matrix-vector", "default", "self::vector"], ["Specialized", "matrix-vector", "default", "Matrix_SpeakColNum"], ["Precondition", "matrix-vector-simple", "default", "self::vector", "count(children/*)<4", "CQFcellsSimple", "@role!=\"squarematrix\""], ["Precondition", "matrix-vector-simple-silentcolnum", "Matrix_SilentColNum", "self::vector"], ["Precondition", "matrix-row-vector", "default", "self::matrix", "@role=\"rowvector\""], ["Specialized", "matrix-row-vector", "default", "Matrix_SpeakColNum"], ["Precondition", "matrix-row-vector-simple", "default", "self::matrix", "@role=\"rowvector\"", "count(children/*[1]/children/*)<4", "CQFcellsSimple"], ["Precondition", "matrix-row-vector-simple-silentcolnum", "Matrix_SilentColNum", "self::matrix", "@role=\"rowvector\""], ["Precondition", "matrix-row-simple", "default", "self::row", "contains(@grammar, \"simpleDet\")"], ["Precondition", "matrix-row-simple-silent<PERSON>lnum", "Matrix_SilentColNum", "self::row"], ["Precondition", "line-simple", "default", "self::line", "contains(@grammar, \"simpleDet\")"], ["Precondition", "matrix-row", "default", "self::row"], ["Specialized", "matrix-row", "default", "Matrix_SpeakColNum"], ["Precondition", "matrix-cell", "default", "self::cell"], ["Precondition", "matrix-end-matrix", "Matrix_EndMatrix", "self::matrix", "not(contains(@grammar, \"EndMatrix\"))"], ["Precondition", "matrix-end-vector", "Matrix_EndMatrix", "self::vector", "not(contains(@grammar, \"EndMatrix\"))"], ["Precondition", "matrix-end-determinant", "Matrix_EndMatrix", "self::matrix", "@role=\"determinant\"", "not(contains(@grammar, \"EndMatrix\"))"], ["Precondition", "vector", "Matrix_Vector", "self::vector"], ["Specialized", "vector", "Matrix_Vector", "Matrix_EndVector"], ["Precondition", "vector-simple", "Matrix_Vector", "self::vector", "count(children/*)<4", "CQFcellsSimple"], ["Specialized", "vector-simple", "Matrix_Vector", "Matrix_EndVector"], ["Precondition", "row-vector", "Matrix_Vector", "self::matrix", "@role=\"rowvector\""], ["Specialized", "row-vector", "Matrix_Vector", "Matrix_EndVector"], ["Precondition", "row-vector-simple", "Matrix_Vector", "self::matrix", "@role=\"rowvector\"", "count(children/*[1]/children/*)<4", "CQFcellsSimple"], ["Specialized", "row-vector-simple", "Matrix_Vector", "Matrix_EndVector"], ["Precondition", "vector-end-matrix", "Matrix_EndVector", "self::matrix", "not(contains(@grammar, \"EndMatrix\"))", "self::*"], ["Precondition", "vector-end-vector", "Matrix_EndVector", "self::vector", "not(contains(@grammar, \"EndMatrix\"))", "self::*"], ["Precondition", "vector-end-vector-endvector", "Matrix_EndVector", "self::matrix", "@role=\"rowvector\"", "not(contains(@grammar, \"EndMatrix\"))", "self::*"], ["Precondition", "vector-end-determinant", "Matrix_EndVector", "self::matrix", "@role=\"determinant\"", "not(contains(@grammar, \"EndMatrix\"))", "self::*"], ["Precondition", "binomial", "Matrix_Combinatoric", "self::vector", "@role=\"binomial\""], ["Precondition", "lines-summary", "default", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))", "self::*"], ["<PERSON><PERSON>", "lines-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))", "self::*"], ["Precondition", "lines-summary-none", "MultiLineOverview_None", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))", "self::*"], ["<PERSON><PERSON>", "lines-summary-none", "self::table", "not(contains(@grammar, \"layoutSummary\"))", "self::*"], ["Precondition", "cases-summary", "default", "self::cases", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "cases-summary-none", "MultiLineOverview_None", "self::cases", "not(contains(@grammar, \"layoutSummary\"))", "self::*"], ["Precondition", "lines", "default", "self::table"], ["<PERSON><PERSON>", "lines", "self::multiline"], ["Precondition", "line", "default", "self::line"], ["Precondition", "row-medium", "default", "self::row", "@role=\"table\""], ["<PERSON><PERSON>", "row-medium", "self::row", "@role=\"cases\""], ["Precondition", "row-long", "MultiLinePausesBetweenColumns_Long", "self::row", "@role=\"table\""], ["<PERSON><PERSON>", "row-long", "self::row", "@role=\"cases\""], ["Precondition", "row-short", "MultiLinePausesBetweenColumns_Short", "self::row", "@role=\"table\""], ["<PERSON><PERSON>", "row-short", "self::row", "@role=\"cases\""], ["Precondition", "blank-cell", "default", "self::cell", "count(children/*)=0"], ["Precondition", "blank-line", "default", "self::line", "count(children/*)=0"], ["Precondition", "blank-cell-empty", "default", "self::empty", "count(../*)=1", "name(../..)=\"cell\""], ["Precondition", "blank-line-empty", "default", "self::empty", "count(../*)=1", "name(../..)=\"line\""], ["Precondition", "cases", "default", "self::cases"], ["Precondition", "lines-cases-summary", "MultiLineLabel_Case", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))"], ["<PERSON><PERSON>", "lines-cases-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "lines-cases", "MultiLineLabel_Case", "self::table"], ["<PERSON><PERSON>", "lines-cases", "self::multiline"], ["Precondition", "lines-equations-summary", "MultiLineLabel_Equation", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))"], ["<PERSON><PERSON>", "lines-equations-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "lines-equations", "MultiLineLabel_Equation", "self::table"], ["<PERSON><PERSON>", "lines-equations", "self::multiline"], ["Precondition", "lines-steps-summary", "MultiLineLabel_Step", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))"], ["<PERSON><PERSON>", "lines-steps-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "lines-steps", "MultiLineLabel_Step", "self::table"], ["<PERSON><PERSON>", "lines-steps", "self::multiline"], ["Precondition", "lines-rows-summary", "MultiLineLabel_Row", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))"], ["<PERSON><PERSON>", "lines-rows-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "lines-rows", "MultiLineLabel_Row", "self::table"], ["<PERSON><PERSON>", "lines-rows", "self::multiline"], ["Precondition", "lines-constraints-summary", "MultiLineLabel_Constraint", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))"], ["<PERSON><PERSON>", "lines-constraints-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "lines-constraints", "MultiLineLabel_Constraint", "self::table"], ["<PERSON><PERSON>", "lines-constraints", "self::multiline"], ["Precondition", "lines-none", "MultiLineLabel_None", "self::table", "contains(@grammar, \"layoutSummary\")"], ["<PERSON><PERSON>", "lines-none", "self::multiline", "contains(@grammar, \"layoutSummary\")"], ["<PERSON><PERSON>", "lines-none", "self::cases", "contains(@grammar, \"layoutSummary\")"], ["Precondition", "bigop", "default", "self::bigop"], ["Precondition", "limboth", "default", "self::limboth"], ["Precondition", "limlower", "default", "self::lim<PERSON>er"], ["Precondition", "limupper", "default", "self::limupper"], ["Precondition", "integral", "default", "self::integral"], ["Precondition", "integral-novar", "default", "self::integral", "name(children/*[3])=\"empty\""], ["Precondition", "overscript", "default", "self::overscore"], ["Precondition", "overscript-accent", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]"], ["Precondition", "overscript-limits", "default", "self::overscore", "children/*[2][@role!=\"overaccent\"]", "name(children/*[1])=\"underscore\"", "children/*[1]/children/*[2][@role!=\"underaccent\"]"], ["Precondition", "underscript", "default", "self::underscore"], ["Precondition", "underscript-limits", "default", "self::underscore", "@role=\"underover\"", "children/*[2][@role!=\"underaccent\"]"], ["Precondition", "number", "default", "self::number"], ["Precondition", "mixed-number", "default", "self::number", "@role=\"mixed\""], ["Precondition", "number-with-chars", "default", "self::number", "@role=\"othernumber\"", "\"\" != translate(text(), \"0123456789.,\", \"\")", "not(contains(@grammar, \"protected\"))"], ["Precondition", "decimal-period", "default", "self::punctuated", "@role=\"sequence\"", "count(./content/*)=1", "./content/*[1][@role=\"fullstop\"]", "name(children/*[1])=\"number\"", "children/*[1][@role=\"integer\"]", "name(children/*[3])=\"overscore\"", "children/*[3][@role=\"integer\"]", "children/*[3]/children/*[2][@role=\"overaccent\"]", "children/*[3]/children/*[2][contains(@annotation, \"accent:bar\")]"], ["Precondition", "decimal-period-float", "default", "self::infixop", "@role=\"implicit\"", "count(./children/*)=2", "name(children/*[1])=\"number\"", "children/*[1][@role=\"float\"]", "name(children/*[2])=\"overscore\"", "children/*[2][@role=\"integer\"]", "children/*[2]/children/*[2][@role=\"overaccent\"]", "children/*[2]/children/*[2][contains(@annotation, \"accent:bar\")]"], ["Precondition", "decimal-period-singular", "default", "self::punctuated", "@role=\"sequence\"", "count(./content/*)=1", "./content/*[1][@role=\"fullstop\"]", "name(children/*[1])=\"number\"", "children/*[1][@role=\"integer\"]", "name(children/*[3])=\"overscore\"", "children/*[3][@role=\"integer\"]", "children/*[3]/children/*[2][@role=\"overaccent\"]", "children/*[3]/children/*[2][contains(@annotation, \"accent:bar\")]", "string-length(./children/*[3]/children/*[1]/text())=1"], ["Precondition", "decimal-period-singular-float", "default", "self::infixop", "@role=\"implicit\"", "count(./children/*)=2", "name(children/*[1])=\"number\"", "children/*[1][@role=\"float\"]", "name(children/*[2])=\"overscore\"", "children/*[2][@role=\"integer\"]", "children/*[2]/children/*[2][@role=\"overaccent\"]", "children/*[2]/children/*[2][contains(@annotation, \"accent:bar\")]", "string-length(./children/*[2]/children/*[1]/text())=1"], ["Precondition", "number-with-spaces", "default", "self::number", "contains(@grammar, \"spaceout\")"], ["Precondition", "decimal-point", "default", "self::punctuation", "@role=\"fullstop\"", "contains(@grammar,\"number\")"], ["Precondition", "line-segment", "default", "self::overscore", "@role=\"implicit\"", "children/*[2][@role=\"overaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]", "name(children/*[1])=\"infixop\"", "count(./children/*[1]/children/*)=2"], ["Precondition", "conjugate", "Bar_Conjugate", "self::overscore", "children/*[2][@role=\"overaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]"], ["Precondition", "defined-by", "default", "self::overscore", "@role=\"equality\"", "@embellished=\"relation\"", "name(children/*[2])=\"text\"", "children/*[2][text()]=\"def\""], ["Precondition", "adorned-sign", "default", "self::overscore", "@embellished", "name(children/*[1])=\"operator\" or name(children/*[1])=\"relation\""], ["Precondition", "factorial", "default", "self::punctuation", "text()=\"!\"", "name(preceding-sibling::*[1])!=\"text\""], ["Precondition", "tensor-base", "default", "self::tensor"], ["Precondition", "left-super", "default", "self::*[@role=\"leftsuper\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "left-super-list", "default", "self::punctuated[@role=\"leftsuper\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "left-sub", "default", "self::*[@role=\"leftsub\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "left-sub-list", "default", "self::punctuated[@role=\"leftsub\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "right-super", "default", "self::*[@role=\"rightsuper\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "right-super-list", "default", "self::punctuated[@role=\"rightsuper\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "right-sub", "default", "self::*[@role=\"rightsub\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "right-sub-list", "default", "self::punctuated[@role=\"rightsub\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "empty-index", "default", "self::empty[@role=\"rightsub\" or @role=\"rightsuper\" or @role=\"leftsub\" or @role=\"leftsuper\"]"], ["Precondition", "combinatorics", "default", "self::tensor", "name(children/*[3])=\"empty\"", "name(children/*[5])=\"empty\"", "children/*[1][text()=\"P\" or text()=\"C\"]"], ["Precondition", "choose", "CombinationPermutation_ChoosePermute", "self::tensor", "name(children/*[3])=\"empty\"", "name(children/*[5])=\"empty\"", "children/*[1][text()=\"C\"]"], ["Precondition", "permute", "CombinationPermutation_ChoosePermute", "self::tensor", "name(children/*[3])=\"empty\"", "name(children/*[5])=\"empty\"", "children/*[1][text()=\"P\"]"], ["Precondition", "unit-singular", "default", "self::identifier[@role=\"unit\"]"], ["Precondition", "unit-plural", "default", "self::identifier[@role=\"unit\"]", "not(contains(@grammar, \"singular\"))"], ["Precondition", "unit-square", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=2]", "name(children/*[1])=\"identifier\""], ["Precondition", "unit-cubic", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=3]", "name(children/*[1])=\"identifier\""], ["Precondition", "unit-reciprocal", "default", "self::superscript[@role=\"unit\"]", "name(children/*[1])=\"identifier\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=1]", "count(preceding-sibling::*)=0 or preceding-sibling::*[@role!=\"unit\"]"], ["Precondition", "unit-reciprocal-singular", "default", "self::superscript[@role=\"unit\"]", "name(children/*[1])=\"identifier\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=1]", "preceding-sibling::*[@role=\"unit\"]"], ["Precondition", "unit-combine", "default", "self::infixop[@role=\"unit\"]"], ["Precondition", "unit-combine-singular", "default", "self::infixop[@role=\"unit\"]", "name(children/*[1])=\"number\"", "children/*[1][text()=1]"], ["Precondition", "unit-divide", "default", "self::fraction[@role=\"unit\"]"], ["Precondition", "currency", "default", "self::infixop", "contains(@annotation, \"clearspeak:unit\")", "children/*[1][@role=\"unit\"]", "children/*[1][@category=\"unit:currency\"]"], ["Precondition", "currency-position", "Currency_Position", "self::infixop", "contains(@annotation, \"clearspeak:unit\")"], ["Specialized", "currency-position", "Currency_Position", "Currency_Prefix"], ["Precondition", "currency-prefix", "Currency_Prefix", "self::infixop", "contains(@annotation, \"clearspeak:unit\")", "children/*[last()][@role=\"unit\"]", "children/*[last()][@category=\"unit:currency\"]"], ["Precondition", "enclose", "default", "self::enclose"], ["Precondition", "enclose-end", "Enclosed_EndEnclose", "self::enclose"], ["Precondition", "overbar", "default", "self::enclose", "@role=\"top\""], ["Precondition", "underbar", "default", "self::enclose", "@role=\"bottom\""], ["Precondition", "leftbar", "default", "self::enclose", "@role=\"left\""], ["Precondition", "rightbar", "default", "self::enclose", "@role=\"right\""], ["Precondition", "crossout", "default", "self::enclose", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "crossout-end", "Enclosed_EndEnclose", "self::enclose", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel-over", "default", "self::overscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["<PERSON><PERSON>", "cancel-over", "self::underscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel-under", "default", "self::underscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["<PERSON><PERSON>", "cancel-under", "self::overscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["Precondition", "cancel-over-end", "Enclosed_EndEnclose", "self::overscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["<PERSON><PERSON>", "cancel-over-end", "self::underscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel-under-end", "Enclosed_EndEnclose", "self::underscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["<PERSON><PERSON>", "cancel-under-end", "self::overscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"]], "annotators": ["simple", "unit"]}, "base/rules/clearspeak_base_actions.min": {"domain": "clearspeak", "locale": "base", "modality": "speech", "kind": "actions", "rules": [["Action", "punctuation-lr", "[p] (pause:short); [n] text() (pause:short)"], ["Action", "punctuation", "[n] text()"], ["Action", "punctuation-l", "[p] (pause:short); [n] text()"], ["Action", "punctuation-r", "[n] text() (pause:short)"], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "degrees", "[m] children/* (grammar:degree)"], ["Action", "feet", "[n] children/*[1]; [t] \"ft\" (grammar:annotation=\"unit\":translate:plural)"], ["Action", "foot", "[n] children/*[1]; [t] \"ft\" (grammar:annotation=\"unit\":translate)"], ["Action", "inches", "[n] children/*[1]; [t] \"in\" (grammar:annotation=\"unit\":translate:plural)"], ["Action", "inch", "[n] children/*[1]; [t] \"in\" (grammar:annotation=\"unit\":translate)"], ["Action", "minutes", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "minute", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate)"], ["Action", "seconds", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "second", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate)"], ["Action", "degrees-angle", "[t] text() (grammar:annotation=\"unit\":translate:plural, pause:short)"], ["Action", "degree-angle", "[t] text() (grammar:annotation=\"unit\":translate, pause:short)"], ["Action", "minutes-angle", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "minute-angle", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate)"], ["Action", "seconds-angle", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "second-angle", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate)"], ["Action", "feet-length", "[n] children/*[1]; [t] \"ft\" (grammar:annotation=\"unit\":translate:plural, pause:short)"], ["Action", "foot-length", "[n] children/*[1]; [t] \"ft\" (grammar:annotation=\"unit\":translate, pause:short)"], ["Action", "inches-length", "[n] children/*[1]; ; [t] \"in\" (grammar:annotation=\"unit\":translate:plural, pause:short)"], ["Action", "inch-length", "[n] children/*[1]; ; [t] \"in\" (grammar:annotation=\"unit\":translate, pause:short)"], ["Action", "punctuated", "[m] children/*"], ["Action", "function", "[n] text()"], ["Action", "binary-operation", "[n] . (grammar:impliedTimes, pause:short)"], ["Action", "function-ln-natlog", "[n] . (grammar:NatLog)"], ["Action", "function-ln-natlog-pause", "[n] . (grammar:<PERSON><PERSON><PERSON>, pause:short)"], ["Action", "function-no-inverse", "[n] . (grammar:functions_none)"], ["Action", "paren-simple", "[n] children/*[1]"], ["Action", "paren-simple-exp", "[n] children/*[1]"], ["Action", "paren-simple-nested-func", "[n] children/*[1]"], ["Action", "paren-simple-nested-func-no-bracket", "[n] children/*[1]"], ["Action", "fences-open-close", "[p] (pause:short); [n] content/*[1] (grammar:spokenFence, pause:short); [n] children/*[1] (pause:short); [n] content/*[2] (grammar:spokenFence, pause:short)"], ["Action", "paren-simple-nested-func-default", "[p] (pause:short); [n] content/*[1] (pause:short); [n] children/*[1] (pause:short); [n] content/*[2] (pause:short)"], ["Action", "paren-simple-nested-func-none", "[p] (pause:short); [n] content/*[1] (grammar:spokenFence, pause:short); [n] children/*[1] (pause:short); [n] content/*[2] (grammar:spokenFence, pause:short)"], ["Action", "fence-silent", "[p] (pause:short); [n] children/*[1] (pause:short)"], ["Action", "fences-open-close-none", "[p] (pause:short); [n] content/*[1] (grammar:spokenFence, pause:short); [n] children/*[1] (pause:short); [n] content/*[2] (grammar:spokenFence, pause:short)"], ["Action", "fence-nesting", "[n] text() (grammar:insertNesting=CSFnestingDepth)"], ["Action", "fence-no-nesting", "[n] text()"], ["Action", "interval-open-inf-lr", ""], ["Action", "paren-nested-embellished-funcs", "[p] (pause:short); [n] content/*[1] (pause:short); [n] children/*[1] (pause:short); [n] content/*[2] (pause:short)"], ["Action", "set-collection-<PERSON><PERSON><PERSON>", "[n] children/*[1]"], ["Action", "prefix", "[m] content/* (grammar:prefix); [n] children/*[1]"], ["Action", "postfix", "[n] children/*[1]; [m] content/* (grammar:postfix)"], ["Action", "binary-operation-default", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "binary-operation-moreimpliedtimes", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "binary-operation-pause", "[p] (pause:short); [m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "binary-operation-pause-r", "[m] children/* (sepFunc:CTFcontentIterator, pause:short)"], ["Action", "binary-operation-pause-lr", "[p] (pause:short); [m] children/* (sepFunc:CTFcontentIterator, pause:short)"], ["Action", "implicit-times", "[p] (pause:short)"], ["Action", "implicit-times-default", ""], ["Action", "implicit-times-simple", "[n] text()"], ["Action", "implicit-times-moreimpliedtimes", "[n] text()"], ["Action", "implicit-times-none", ""], ["Action", "binary-operation-simple", "[m] children/* (rate:\"0.5\", pause:short)"], ["Action", "simple-in-fraction", "[n] . (rate:\"0.5\", grammar:inFrac)"], ["Action", "relseq", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "mult<PERSON>", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "natural-numbers-super", "[t] \"n\" (join:\"-\",grammar:translate); [n] children/*[2] (grammar:numbers2alpha)"], ["Action", "integers-super", "[t] \"z\" (join:\"-\",grammar:translate); [n] children/*[2] (grammar:numbers2alpha)"], ["Action", "rational-numbers-super", "[t] \"q\" (join:\"-\",grammar:translate); [n] children/*[2] (grammar:numbers2alpha)"], ["Action", "real-numbers-super", "[t] \"r\" (join:\"-\",grammar:translate); [n] children/*[2] (grammar:numbers2alpha)"], ["Action", "complex-numbers-super", "[t] \"c\" (join:\"-\",grammar:translate); [n] children/*[2] (grammar:numbers2alpha)"], ["Action", "matrix-row-simple", "[m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\")"], ["Action", "matrix-row-simple-silent<PERSON>lnum", "[m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\")"], ["Action", "line-simple", "[n] children/*[1]"], ["Action", "matrix-cell", "[n] children/*[1]"], ["Action", "lines-summary-none", "[n] . (grammar:layoutSummary)"], ["Action", "cases-summary-none", "[n] . (grammar:layoutSummary)"], ["Action", "line", "[n] children/*[1]"], ["Action", "row-medium", "[m] children/* (sepFunc:CTFpauseSeparator, separator:\"medium\")"], ["Action", "row-long", "[m] children/* (sepFunc:CTFpauseSeparator, separator:\"long\")"], ["Action", "row-short", "[m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\")"], ["Action", "lines-none", "[p] (pause:short); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "overscript-accent", "[n] children/*[1]; [n] children/*[2]"], ["Action", "number", "[n] text()"], ["Action", "number-with-spaces", "[m] CQFspaceoutNumber (grammar:!spaceout:number)"], ["Action", "tensor-base", "[n] children/*[2]; [n] children/*[3]; [n] children/*[1]; [n] children/*[4]; [n] children/*[5]"], ["Action", "empty-index", "[p] (pause:medium)"], ["Action", "combinatorics", "[n] children/*[2] (grammar:combinatorics); [n] children/*[1]; [n] children/*[4] (grammar:combinatorics)"], ["Action", "unit-singular", "[t] text() (grammar:annotation=\"unit\":translate)"], ["Action", "unit-plural", "[t] text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "unit-combine", "[m] children/*"], ["Action", "unit-combine-singular", "[n] children/*[1]; [n] children/*[2] (grammar:singular); [m] children/*[position()>2]"], ["Action", "currency", "[m] children/*[position()>1]; [n] children/*[1]"], ["Action", "currency-position", "[m] children/*"], ["Action", "currency-prefix", "[n] children/*[last()]; [m] children/*[position()<last()]"]]}, "base/rules/clearspeak_base_romance.min": {"locale": "romance", "domain": "clearspeak", "modality": "speech", "kind": "abstract", "inherits": "base", "rules": [["Precondition", "function-prefix-reciprocal", "Trig_Reciprocal", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "function-prefix-reciprocal-simple", "Trig_Reciprocal", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "contains(children/*[2]/@annotation, \"clearspeak:simple\")", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "function-reciprocal", "Functions_Reciprocal", "self::superscript", "@role=\"prefix function\" or @role=\"simple function\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"]]}, "base/rules/mathspeak_base.min": {"domain": "mathspeak", "locale": "base", "modality": "speech", "kind": "abstract", "rules": [["Rule", "direct-speech", "default", "[t] @ext-speech", "self::*[@ext-speech]", "priority=Infinity"], ["Rule", "stree", "default", "[n] ./*[1]", "self::stree", "CQFresetNesting"], ["Rule", "unknown", "default", "[n] text()", "self::unknown"], ["Rule", "protected", "default", "[n] text() (grammar:ignoreCaps)", "self::number", "contains(@grammar, \"protected\")"], ["Rule", "omit-empty", "default", "[p] (pause:100)", "self::empty"], ["Rule", "omit-font", "default", "[n] . (grammar:ignoreFont=@font)", "self::identifier", "string-length(text())=1", "@font", "not(contains(@grammar, \"ignoreFont\"))", "@font=\"italic\""], ["Precondition", "collapsed", "default", "self::*[@alternative]", "not(contains(@grammar, \"collapsed\"))"], ["Specialized", "collapsed", "default", "brief"], ["Specialized", "collapsed", "brief", "sbrief"], ["Precondition", "blank-cell-empty", "default", "self::empty", "count(../*)=1", "name(../..)=\"cell\""], ["Precondition", "blank-line-empty", "default", "self::empty", "count(../*)=1", "name(../..)=\"line\""], ["Precondition", "font", "default", "self::*", "@font", "not(contains(@grammar, \"ignoreFont\"))", "@font!=\"normal\""], ["<PERSON><PERSON>", "font", "self::identifier", "string-length(text())=1", "@font", "not(contains(@grammar, \"ignoreFont\"))", "@font=\"normal\"", "\"\"=translate(text(), \"abcdefghijklmnopqrstuvwxyzαβγδεζηθικλμνξοπρςστυφχψωABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\", \"\")", "@role!=\"unit\""], ["<PERSON><PERSON>", "font", "self::identifier", "string-length(text())=1", "@font", "@font=\"normal\"", "not(contains(@grammar, \"ignoreFont\"))", "@role!=\"unit\""], ["Precondition", "number", "default", "self::number"], ["Precondition", "mixed-number", "default", "self::number", "@role=\"mixed\""], ["Precondition", "number-with-chars", "default", "self::number[@role=\"othernumber\"]", "\"\" != translate(text(), \"0123456789.,\", \"\")", "not(contains(@grammar, \"protected\"))"], ["Precondition", "number-with-chars-brief", "brief", "self::number[@role=\"othernumber\"]", "\"\" != translate(text(), \"0123456789.,\", \"\")", "not(contains(@grammar, \"protected\"))"], ["Specialized", "number-with-chars-brief", "brief", "sbrief"], ["Precondition", "number-as-upper-word", "default", "self::number[@role=\"othernumber\"]", "string-length(text())>1", "text()=translate(text(), \"abcdefghijklmnopqrstuvwxyzαβγδεζηθικλμνξοπρςστυφχψω\", \"ABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\")", "\"\"=translate(text(), \"ABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\",\"\")"], ["Specialized", "number-as-upper-word", "default", "brief"], ["Specialized", "number-as-upper-word", "default", "sbrief"], ["Precondition", "number-baseline", "default", "self::number", "not(contains(@grammar, \"ignoreFont\"))", "preceding-sibling::identifier", "not(contains(@grammar, \"baseline\"))", "preceding-sibling::*[1][contains(@role,\"letter\")]", "parent::*/parent::infixop[@role=\"implicit\"]"], ["Precondition", "number-baseline-brief", "brief", "self::number", "not(contains(@grammar, \"ignoreFont\"))", "preceding-sibling::identifier", "not(contains(@grammar, \"baseline\"))", "preceding-sibling::*[1][contains(@role,\"letter\")]", "parent::*/parent::infixop[@role=\"implicit\"]"], ["Specialized", "number-baseline-brief", "brief", "sbrief"], ["Precondition", "number-baseline-font", "default", "self::number[@font!=\"normal\"]", "not(contains(@grammar, \"ignoreFont\"))", "preceding-sibling::identifier", "preceding-sibling::*[contains(@role,\"letter\")]", "parent::*/parent::infixop[@role=\"implicit\"]"], ["Precondition", "number-baseline-font-brief", "brief", "self::number[@font!=\"normal\"]", "not(contains(@grammar, \"ignoreFont\"))", "preceding-sibling::identifier", "preceding-sibling::*[contains(@role,\"letter\")]", "parent::*/parent::infixop[@role=\"implicit\"]"], ["Specialized", "number-baseline-font-brief", "brief", "sbrief"], ["Precondition", "identifier-spacing", "default", "self::identifier", "string-length(text())>1", "@role!=\"unit\"", "not(@font) or @font=\"normal\" or contains(@grammar, \"ignoreFont\")", "text()!=translate(text(), \"abcdefghijklmnopqrstuvwxyzαβγδεζηθικλμνξοπρςστυφχψωABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\", \"\")"], ["Precondition", "identifier", "default", "self::identifier"], ["Precondition", "negative-number", "default", "self::prefixop", "@role=\"negative\"", "children/identifier"], ["<PERSON><PERSON>", "negative-number", "self::prefixop", "@role=\"negative\"", "children/number"], ["<PERSON><PERSON>", "negative-number", "self::prefixop", "@role=\"negative\"", "children/fraction[@role=\"vulgar\"]"], ["Precondition", "negative", "default", "self::prefixop", "@role=\"negative\""], ["Precondition", "prefix", "default", "self::prefixop"], ["Precondition", "postfix", "default", "self::postfixop"], ["Precondition", "binary-operation", "default", "self::infixop"], ["Precondition", "division", "default", "self::infixop", "@role=\"division\"", "count(children/*)=2"], ["Precondition", "implicit", "default", "self::infixop", "@role=\"implicit\""], ["<PERSON><PERSON>", "implicit", "self::infixop", "@role=\"leftsuper\" or @role=\"leftsub\" or @role=\"rightsuper\" or @role=\"rightsub\""], ["Precondition", "subtraction", "default", "self::infixop", "@role=\"subtraction\""], ["Precondition", "function-unknown", "default", "self::appl"], ["Precondition", "function-prefix", "default", "self::appl", "children/*[1][@role=\"prefix function\"]"], ["Precondition", "fences-open-close", "default", "self::fenced", "@role=\"leftright\""], ["Precondition", "fences-neutral", "default", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-neutral-sbrief", "sbrief", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-metric", "default", "self::fenced", "@role=\"metric\""], ["Precondition", "fences-metric-sbrief", "sbrief", "self::fenced", "@role=\"metric\""], ["Precondition", "empty-set", "default", "self::fenced[@role=\"set empty\"]", "not(name(../..)=\"appl\")"], ["Specialized", "empty-set", "default", "sbrief"], ["Precondition", "fences-set", "default", "self::fenced", "contains(@role,\"set \")", "not(name(../..)=\"appl\")"], ["Precondition", "fences-set-sbrief", "sbrief", "self::fenced", "contains(@role,\"set \")", "not(name(../..)=\"appl\")"], ["Precondition", "text", "default", "self::text"], ["Precondition", "factorial", "default", "self::punctuation", "text()=\"!\"", "name(preceding-sibling::*[1])!=\"text\""], ["Precondition", "minus", "default", "self::operator", "text()=\"-\""], ["Precondition", "fraction", "default", "self::fraction"], ["Precondition", "fraction-brief", "brief", "self::fraction"], ["Precondition", "fraction-sbrief", "sbrief", "self::fraction"], ["Precondition", "vulgar-fraction", "default", "self::fraction", "@role=\"vulgar\"", "CQFvulgarFractionSmall"], ["Specialized", "vulgar-fraction", "default", "brief"], ["Specialized", "vulgar-fraction", "default", "sbrief"], ["Precondition", "continued-fraction-outer", "default", "self::fraction", "not(ancestor::fraction)", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\" and not(following-sibling::*)]"], ["Precondition", "continued-fraction-outer-brief", "brief", "self::fraction", "not(ancestor::fraction)", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\" and not(following-sibling::*)]"], ["Specialized", "continued-fraction-outer-brief", "brief", "sbrief"], ["Precondition", "continued-fraction-inner", "default", "self::fraction", "ancestor::fraction", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\" and not(following-sibling::*)]"], ["Precondition", "continued-fraction-inner-brief", "brief", "self::fraction", "ancestor::fraction", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\" and not(following-sibling::*)]"], ["Precondition", "continued-fraction-inner-sbrief", "sbrief", "self::fraction", "ancestor::fraction", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\" and not(following-sibling::*)]"], ["Precondition", "sqrt", "default", "self::sqrt"], ["Precondition", "sqrt-brief", "brief", "self::sqrt"], ["Precondition", "sqrt-sbrief", "sbrief", "self::sqrt"], ["Precondition", "root-small", "default", "self::root", "CQFisSmallRoot"], ["Precondition", "root-small-brief", "brief", "self::root", "CQFisSmallRoot"], ["Precondition", "root-small-sbrief", "sbrief", "self::root", "CQFisSmallRoot"], ["Precondition", "root", "default", "self::root"], ["Precondition", "root-brief", "brief", "self::root"], ["Precondition", "root-sbrief", "sbrief", "self::root"], ["Precondition", "limboth", "default", "self::limboth", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["Precondition", "limlower", "default", "self::lim<PERSON>er", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["Precondition", "limupper", "default", "self::limupper", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limlower", "self::underscore", "@role=\"limit function\"", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limlower", "self::underscore", "children/*[2][@role!=\"underaccent\"]", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limupper", "self::overscore", "children/*[2][@role!=\"overaccent\"]", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["Precondition", "limboth-end", "default", "self::limboth"], ["Precondition", "limlower-end", "default", "self::lim<PERSON>er"], ["Precondition", "limupper-end", "default", "self::limupper"], ["<PERSON><PERSON>", "limlower-end", "self::underscore", "@role=\"limit function\"", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limlower-end", "self::underscore"], ["<PERSON><PERSON>", "limupper-end", "self::overscore"], ["Precondition", "integral-index", "default", "self::integral"], ["Precondition", "integral", "default", "self::limboth", "@role=\"integral\""], ["Precondition", "integral-brief", "brief", "self::limboth", "@role=\"integral\""], ["Specialized", "integral-brief", "brief", "sbrief"], ["Precondition", "bigop", "default", "self::bigop"], ["Precondition", "relseq", "default", "self::relseq"], ["Precondition", "equality", "default", "self::relseq", "@role=\"equality\"", "count(./children/*)=2"], ["Precondition", "multi-equality", "default", "self::relseq", "@role=\"equality\"", "count(./children/*)>2"], ["Precondition", "mult<PERSON>", "default", "self::multirel"], ["Precondition", "subscript", "default", "self::subscript"], ["Precondition", "subscript-brief", "brief", "self::subscript"], ["Specialized", "subscript-brief", "brief", "sbrief"], ["Precondition", "subscript-simple", "default", "self::subscript", "name(./children/*[1])=\"identifier\"", "name(./children/*[2])=\"number\"", "./children/*[2][@role!=\"mixed\"]", "./children/*[2][@role!=\"othernumber\"]"], ["Specialized", "subscript-simple", "default", "brief"], ["Specialized", "subscript-simple", "default", "sbrief"], ["Precondition", "subscript-baseline", "default", "self::subscript", "following-sibling::*", "not(name(following-sibling::subscript/children/*[1])=\"empty\" or (name(following-sibling::infixop[@role=\"implicit\"]/children/*[1])=\"subscript\" and name(following-sibling::*/children/*[1]/children/*[1])=\"empty\")) and @role!=\"subsup\"", "not(following-sibling::*[@role=\"rightsuper\" or @role=\"rightsub\" or @role=\"leftsub\" or @role=\"leftsub\"])"], ["Precondition", "subscript-baseline-brief", "brief", "self::subscript", "following-sibling::*", "not(name(following-sibling::subscript/children/*[1])=\"empty\" or (name(following-sibling::infixop[@role=\"implicit\"]/children/*[1])=\"subscript\" and name(following-sibling::*/children/*[1]/children/*[1])=\"empty\")) and @role!=\"subsup\"", "not(following-sibling::*[@role=\"rightsuper\" or @role=\"rightsub\" or @role=\"leftsub\" or @role=\"leftsub\"])"], ["Specialized", "subscript-baseline-brief", "brief", "sbrief"], ["<PERSON><PERSON>", "subscript-baseline", "self::subscript", "not(following-sibling::*)", "ancestor::fenced|ancestor::root|ancestor::sqrt|ancestor::punctuated|ancestor::fraction", "not(ancestor::punctuated[@role=\"leftsuper\" or @role=\"rightsub\" or @role=\"rightsuper\" or @role=\"rightsub\"])"], ["<PERSON><PERSON>", "subscript-baseline", "self::subscript", "not(following-sibling::*)", "ancestor::relse<PERSON>|ancestor::multirel", "CGFbaselineConstraint"], ["<PERSON><PERSON>", "subscript-baseline", "self::subscript", "not(following-sibling::*)", "@embellished"], ["<PERSON><PERSON>", "subscript-baseline-brief", "self::subscript", "not(following-sibling::*)", "ancestor::fenced|ancestor::root|ancestor::sqrt|ancestor::punctuated|ancestor::fraction", "not(ancestor::punctuated[@role=\"leftsuper\" or @role=\"rightsub\" or @role=\"rightsuper\" or @role=\"rightsub\"])"], ["<PERSON><PERSON>", "subscript-baseline-brief", "self::subscript", "not(following-sibling::*)", "ancestor::relse<PERSON>|ancestor::multirel", "CGFbaselineConstraint"], ["<PERSON><PERSON>", "subscript-baseline-brief", "self::subscript", "not(following-sibling::*)", "@embellished"], ["Precondition", "subscript-empty-sup", "default", "self::subscript", "name(children/*[2])=\"infixop\"", "name(children/*[2][@role=\"implicit\"]/children/*[1])=\"superscript\"", "name(children/*[2]/children/*[1]/children/*[1])=\"empty\""], ["Specialized", "subscript-empty-sup", "default", "brief"], ["Specialized", "subscript-empty-sup", "brief", "sbrief"], ["<PERSON><PERSON>", "subscript-empty-sup", "self::subscript", "name(children/*[2])=\"superscript\"", "name(children/*[2]/children/*[1])=\"empty\""], ["Precondition", "superscript", "default", "self::superscript"], ["Precondition", "superscript-brief", "brief", "self::superscript"], ["Specialized", "superscript-brief", "brief", "sbrief"], ["Precondition", "superscript-baseline", "default", "self::superscript", "following-sibling::*", "not(name(following-sibling::superscript/children/*[1])=\"empty\" or (name(following-sibling::infixop[@role=\"implicit\"]/children/*[1])=\"superscript\" and name(following-sibling::*/children/*[1]/children/*[1])=\"empty\")) and not(following-sibling::*[@role=\"rightsuper\" or @role=\"rightsub\" or @role=\"leftsub\" or @role=\"leftsub\"])"], ["Precondition", "superscript-baseline-brief", "brief", "self::superscript", "following-sibling::*", "not(name(following-sibling::superscript/children/*[1])=\"empty\" or (name(following-sibling::infixop[@role=\"implicit\"]/children/*[1])=\"superscript\" and name(following-sibling::*/children/*[1]/children/*[1])=\"empty\")) and not(following-sibling::*[@role=\"rightsuper\" or @role=\"rightsub\" or @role=\"leftsub\" or @role=\"leftsub\"])"], ["Specialized", "superscript-baseline-brief", "brief", "sbrief"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "ancestor::punctuated", "ancestor::*/following-sibling::* and not(ancestor::punctuated[@role=\"leftsuper\" or @role=\"rightsub\" or @role=\"rightsuper\" or @role=\"rightsub\"])"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "ancestor::fraction|ancestor::fenced|ancestor::root|ancestor::sqrt"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "ancestor::relse<PERSON>|ancestor::multirel", "not(@embellished)", "CGFbaselineConstraint"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "@embellished", "not(children/*[2][@role=\"prime\"])"], ["<PERSON><PERSON>", "superscript-baseline-brief", "self::superscript", "not(following-sibling::*)", "ancestor::punctuated", "ancestor::*/following-sibling::* and not(ancestor::punctuated[@role=\"leftsuper\" or @role=\"rightsub\" or @role=\"rightsuper\" or @role=\"rightsub\"])"], ["<PERSON><PERSON>", "superscript-baseline-brief", "self::superscript", "not(following-sibling::*)", "ancestor::fraction|ancestor::fenced|ancestor::root|ancestor::sqrt"], ["<PERSON><PERSON>", "superscript-baseline-brief", "self::superscript", "not(following-sibling::*)", "ancestor::relse<PERSON>|ancestor::multirel", "not(@embellished)", "CGFbaselineConstraint"], ["<PERSON><PERSON>", "superscript-baseline-brief", "self::superscript", "not(following-sibling::*)", "@embellished", "not(children/*[2][@role=\"prime\"])"], ["Precondition", "superscript-empty-sub", "default", "self::superscript", "name(children/*[2])=\"infixop\"", "name(children/*[2][@role=\"implicit\"]/children/*[1])=\"subscript\"", "name(children/*[2]/children/*[1]/children/*[1])=\"empty\""], ["Specialized", "superscript-empty-sub", "default", "brief"], ["Specialized", "superscript-empty-sub", "brief", "sbrief"], ["<PERSON><PERSON>", "superscript-empty-sub", "self::superscript", "name(children/*[2])=\"subscript\"", "name(children/*[2]/children/*[1])=\"empty\""], ["Precondition", "square", "default", "self::superscript", "children/*[2]", "children/*[2][text()=2]", "name(children/*[1])!=\"text\" or not(name(children/*[1])=\"text\" and (name(../../../punctuated[@role=\"text\"]/..)=\"stree\" or name(..)=\"stree\"))", "name(children/*[1])!=\"subscript\" or (name(children/*[1])=\"subscript\" and name(children/*[1]/children/*[1])=\"identifier\" and name(children/*[1]/children/*[2])=\"number\" and children/*[1]/children/*[2][@role!=\"mixed\"] and children/*[1]/children/*[2][@role!=\"othernumber\"])", "not(@embellished)"], ["Specialized", "square", "default", "brief"], ["Specialized", "square", "default", "sbrief"], ["<PERSON><PERSON>", "square", "self::superscript", "children/*[2]", "children/*[2][text()=2]", "@embellished", "children/*[1][@role=\"prefix operator\"]"], ["Precondition", "cube", "default", "self::superscript", "children/*[2]", "children/*[2][text()=3]", "name(children/*[1])!=\"text\" or not(name(children/*[1])=\"text\" and (name(../../../punctuated[@role=\"text\"]/..)=\"stree\" or name(..)=\"stree\"))", "name(children/*[1])!=\"subscript\" or (name(children/*[1])=\"subscript\" and name(children/*[1]/children/*[1])=\"identifier\" and name(children/*[1]/children/*[2])=\"number\" and children/*[1]/children/*[2][@role!=\"mixed\"] and children/*[1]/children/*[2][@role!=\"othernumber\"])", "not(@embellished)"], ["Specialized", "cube", "default", "brief"], ["Specialized", "cube", "default", "sbrief"], ["<PERSON><PERSON>", "cube", "self::superscript", "children/*[2]", "children/*[2][text()=3]", "@embellished", "children/*[1][@role=\"prefix operator\"]"], ["Precondition", "prime", "default", "self::superscript", "children/*[2]", "children/*[2][@role=\"prime\"]"], ["Specialized", "prime", "default", "brief"], ["Specialized", "prime", "default", "sbrief"], ["Precondition", "double-prime", "default", "self::punctuated", "@role=\"prime\"", "count(children/*)=2"], ["<PERSON><PERSON>", "double-prime", "self::operator", "@role=\"prime\"", "string-length(text())=2"], ["Precondition", "triple-prime", "default", "self::punctuated", "@role=\"prime\"", "count(children/*)=3"], ["<PERSON><PERSON>", "triple-prime", "self::operator", "@role=\"prime\"", "string-length(text())=3"], ["Precondition", "quadruple-prime", "default", "self::punctuated", "@role=\"prime\"", "count(children/*)=4"], ["<PERSON><PERSON>", "quadruple-prime", "self::operator", "@role=\"prime\"", "string-length(text())=4"], ["Precondition", "counted-prime", "default", "self::punctuated", "@role=\"prime\""], ["Precondition", "counted-prime-multichar", "default", "self::operator", "@role=\"prime\"", "string-length(text())>4"], ["Precondition", "prime-subscript", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "not(following-sibling::*)"], ["Precondition", "prime-subscript-brief", "brief", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "not(following-sibling::*)"], ["Specialized", "prime-subscript-brief", "brief", "sbrief"], ["Precondition", "prime-subscript-baseline", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "following-sibling::*"], ["Precondition", "prime-subscript-baseline-brief", "brief", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "following-sibling::*"], ["Specialized", "prime-subscript-baseline-brief", "brief", "sbrief"], ["<PERSON><PERSON>", "prime-subscript-baseline", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "not(following-sibling::*)", "@embellished"], ["<PERSON><PERSON>", "prime-subscript-baseline-brief", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "not(following-sibling::*)", "@embellished"], ["Precondition", "prime-subscript-simple", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "name(children/*[1]/children/*[1])=\"identifier\"", "name(children/*[1]/children/*[2])=\"number\"", "children/*[1]/children/*[2][@role!=\"mixed\"]", "children/*[1]/children/*[2][@role!=\"othernumber\"]"], ["Specialized", "prime-subscript-simple", "default", "brief"], ["Specialized", "prime-subscript-simple", "default", "sbrief"], ["Precondition", "overscore", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]"], ["Precondition", "overscore-brief", "brief", "self::overscore", "children/*[2][@role=\"overaccent\"]"], ["Specialized", "overscore-brief", "brief", "sbrief"], ["Precondition", "double-overscore", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]", "name(children/*[1])=\"overscore\"", "children/*[1]/children/*[2][@role=\"overaccent\"]"], ["Precondition", "double-overscore-brief", "brief", "self::overscore", "children/*[2][@role=\"overaccent\"]", "name(children/*[1])=\"overscore\"", "children/*[1]/children/*[2][@role=\"overaccent\"]"], ["Specialized", "double-overscore-brief", "brief", "sbrief"], ["Precondition", "underscore", "default", "self::underscore", "children/*[2][@role=\"underaccent\"]"], ["Precondition", "underscore-brief", "brief", "self::underscore", "children/*[2][@role=\"underaccent\"]"], ["Specialized", "underscore-brief", "brief", "sbrief"], ["Precondition", "double-underscore", "default", "self::underscore", "children/*[2][@role=\"underaccent\"]", "name(children/*[1])=\"underscore\"", "children/*[1]/children/*[2][@role=\"underaccent\"]"], ["Precondition", "double-underscore-brief", "brief", "self::underscore", "children/*[2][@role=\"underaccent\"]", "name(children/*[1])=\"underscore\"", "children/*[1]/children/*[2][@role=\"underaccent\"]"], ["Specialized", "double-underscore-brief", "brief", "sbrief"], ["Precondition", "overbar", "default", "self::overscore", "contains(@role,\"letter\")", "children/*[2][@role=\"overaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]"], ["Specialized", "overbar", "default", "brief"], ["Specialized", "overbar", "default", "sbrief"], ["Precondition", "underbar", "default", "self::underscore", "contains(@role,\"letter\")", "children/*[2][@role=\"underaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]"], ["Specialized", "underbar", "default", "brief"], ["Specialized", "underbar", "default", "sbrief"], ["Precondition", "overtilde", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]", "contains(@role,\"letter\")", "children/*[2][contains(@annotation, \"accent:tilde\")]"], ["Specialized", "overtilde", "default", "brief"], ["Specialized", "overtilde", "default", "sbrief"], ["Precondition", "undertilde", "default", "self::underscore", "contains(@role,\"letter\")", "children/*[2][@role=\"underaccent\"]", "children/*[2][contains(@annotation, \"accent:tilde\")]"], ["Specialized", "undertilde", "default", "brief"], ["Specialized", "undertilde", "default", "sbrief"], ["Precondition", "matrix", "default", "self::matrix"], ["Precondition", "matrix-sbrief", "sbrief", "self::matrix"], ["<PERSON><PERSON>", "matrix", "self::vector"], ["<PERSON><PERSON>", "matrix-sbrief", "self::vector"], ["Precondition", "matrix-row", "default", "self::row"], ["Precondition", "row-with-label", "default", "self::row", "content"], ["Precondition", "row-with-label-brief", "brief", "self::row", "content"], ["Specialized", "row-with-label-brief", "brief", "sbrief"], ["Precondition", "row-with-text-label", "sbrief", "self::row", "content", "name(content/cell/children/*[1])=\"text\""], ["Precondition", "empty-row", "default", "self::row", "count(children/*)=0"], ["Precondition", "matrix-cell", "default", "self::cell"], ["Precondition", "empty-cell", "default", "self::cell", "count(children/*)=0"], ["Precondition", "determinant", "default", "self::matrix", "@role=\"determinant\""], ["Precondition", "determinant-sbrief", "sbrief", "self::matrix", "@role=\"determinant\""], ["Precondition", "determinant-simple", "default", "self::matrix", "@role=\"determinant\"", "CQFdetIsSimple"], ["Precondition", "determinant-simple-sbrief", "sbrief", "self::matrix", "@role=\"determinant\"", "CQFdetIsSimple"], ["Precondition", "row-simple", "default", "self::row", "@role=\"determinant\"", "contains(@grammar, \"simpleDet\")"], ["Precondition", "layout", "default", "self::table"], ["Precondition", "layout-sbrief", "sbrief", "self::table"], ["Precondition", "binomial", "default", "self::vector", "@role=\"binomial\""], ["Precondition", "binomial-sbrief", "sbrief", "self::vector", "@role=\"binomial\""], ["Precondition", "cases", "default", "self::cases"], ["Precondition", "cases-sbrief", "sbrief", "self::cases"], ["<PERSON><PERSON>", "layout", "self::multiline"], ["<PERSON><PERSON>", "layout-sbrief", "self::multiline"], ["Precondition", "line", "default", "self::line"], ["Precondition", "line-with-label", "default", "self::line", "content"], ["Precondition", "line-with-label-brief", "brief", "self::line", "content"], ["Specialized", "line-with-label-brief", "brief", "sbrief"], ["Precondition", "line-with-text-label", "sbrief", "self::line", "content", "name(content/cell/children/*[1])=\"text\""], ["Precondition", "empty-line", "default", "self::line", "count(children/*)=0", "not(content)"], ["Specialized", "empty-line", "default", "brief"], ["Specialized", "empty-line", "brief", "sbrief"], ["Precondition", "empty-line-with-label", "default", "self::line", "count(children/*)=0", "content"], ["Precondition", "empty-line-with-label-brief", "brief", "self::line", "count(children/*)=0", "content"], ["Specialized", "empty-line-with-label-brief", "brief", "sbrief"], ["Precondition", "enclose", "default", "self::enclose"], ["<PERSON><PERSON>", "overbar", "self::enclose", "@role=\"top\""], ["<PERSON><PERSON>", "underbar", "self::enclose", "@role=\"bottom\""], ["Precondition", "leftbar", "default", "self::enclose", "@role=\"left\""], ["Precondition", "rightbar", "default", "self::enclose", "@role=\"right\""], ["Precondition", "crossout", "default", "self::enclose", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel", "default", "self::overscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Specialized", "cancel", "default", "brief"], ["Specialized", "cancel", "default", "sbrief"], ["<PERSON><PERSON>", "cancel", "self::underscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel-reverse", "default", "self::overscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["Specialized", "cancel-reverse", "default", "brief"], ["Specialized", "cancel-reverse", "default", "sbrief"], ["<PERSON><PERSON>", "cancel-reverse", "self::underscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["Precondition", "end-punct", "default", "self::punctuated", "@role=\"endpunct\""], ["Precondition", "start-punct", "default", "self::punctuated", "@role=\"startpunct\""], ["Precondition", "punctuated", "default", "self::punctuated"], ["Precondition", "unit", "default", "self::identifier[@role=\"unit\"]"], ["Precondition", "unit-square", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=2]", "name(children/*[1])=\"identifier\""], ["Specialized", "unit-square", "default", "brief"], ["Specialized", "unit-square", "brief", "sbrief"], ["Precondition", "unit-cubic", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=3]", "name(children/*[1])=\"identifier\""], ["Specialized", "unit-cubic", "default", "brief"], ["Specialized", "unit-cubic", "brief", "sbrief"], ["Precondition", "unit-combine", "default", "self::infixop[@role=\"unit\"]"], ["Precondition", "multi-inference", "default", "self::inference"], ["Precondition", "inference", "default", "self::inference", "count(children/*[2]/children/*)<2"], ["Precondition", "premise", "default", "self::premises"], ["Precondition", "conclusion", "default", "self::conclusion"], ["Precondition", "label", "default", "self::rulelabel"], ["Precondition", "axiom", "default", "self::inference[@role=\"axiom\"]"], ["Precondition", "empty-axiom", "default", "self::inference[@role=\"axiom\"]", "name(children/*[1])=\"empty\""], ["Generator", "CGFtensorRules"]]}, "base/rules/mathspeak_base_actions.min": {"domain": "mathspeak", "locale": "base", "modality": "speech", "kind": "actions", "rules": [["Action", "number", "[n] text()"], ["Action", "identifier-spacing", "[m] CQFspaceoutIdentifier"], ["Action", "identifier", "[n] text()"], ["Action", "prefix", "[m] content/*; [n] children/*[1]"], ["Action", "postfix", "[n] children/*[1]; [m] content/*"], ["Action", "binary-operation", "[m] children/* (sepFunc:CTFcontentIterator);"], ["Action", "implicit", "[m] children/*"], ["Action", "function-unknown", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix", "[n] children/*[1]; [n] children/*[2]"], ["Action", "fences-open-close", "[n] content/*[1]; [n] children/*[1]; [n] content/*[2]"], ["Action", "text", "[n] text()"], ["Action", "matrix-cell", "[n] children/*[1] (pause: 300)"], ["Action", "row-simple", "[m] children/*;"], ["Action", "line", "[m] children/*"], ["Action", "end-punct", "[m] children/*"], ["Action", "start-punct", "[n] content/*[1]; [m] children/*[position()>1]"], ["Action", "punctuated", "[m] children/*"], ["Action", "fraction", "[t] CSFopenFracVerbose; [n] children/*[1]; [t] CSFoverFracVerbose; [n] children/*[2]; [t] CSFcloseFracVerbose"], ["Action", "fraction-brief", "[t] CSFopenFracBrief; [n] children/*[1]; [t] CSFoverFracVerbose; [n] children/*[2]; [t] CSFcloseFracBrief"], ["Action", "fraction-sbrief", "[t] CSFopenFracSbrief; [n] children/*[1]; [t] CSFoverFracSbrief; [n] children/*[2]; [t] CSFcloseFracSbrief"], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction"], ["Action", "sqrt", "[t] CSFopenRadicalVerbose; [n] children/*[1]; [t] CSFcloseRadicalVerbose"], ["Action", "sqrt-brief", "[t] CSFopenRadicalBrief; [n] children/*[1]; [t] CSFcloseRadicalBrief"], ["Action", "sqrt-sbrief", "[t] CSFopenRadicalSbrief; [n] children/*[1]; [t] CSFcloseRadicalBrief"], ["Action", "root-small", "[t] CSFopenRadicalVerbose; [n] children/*[2]; [t] CSFcloseRadicalVerbose"], ["Action", "root-small-brief", "[t] CSFopenRadicalBrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "root-small-sbrief", "[t] CSFopenRadicalSbrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "root", "[t] CSFindexRadicalVerbose; [n] children/*[1];[t] CSFopenRadicalVerbose; [n] children/*[2]; [t] CSFcloseRadicalVerbose"], ["Action", "root-brief", "[t] CSFindexRadicalBrief; [n] children/*[1];[t] CSFopenRadicalBrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "root-sbrief", "[t] CSFindexRadicalSbrief; [n] children/*[1];[t] CSFopenRadicalSbrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "integral-index", "[n] children/*[1]; [n] children/*[2]; [n] children/*[3];"], ["Action", "bigop", "[n] children/*[1]; [n] children/*[2];"], ["Action", "relseq", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "equality", "[n] children/*[1]; [n] content/*[1]; [n] children/*[2]"], ["Action", "multi-equality", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "mult<PERSON>", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "subscript", "[n] children/*[1]; [t] CSFsubscriptVerbose; [n] children/*[2]"], ["Action", "subscript-brief", "[n] children/*[1]; [t] CSFsubscriptBrief; [n] children/*[2]"], ["Action", "subscript-simple", "[n] children/*[1]; [n] children/*[2]"], ["Action", "subscript-baseline", "[n] children/*[1]; [t] CSFsubscriptVerbose; [n] children/*[2]; [t] CSFbaselineVerbose"], ["Action", "subscript-baseline-brief", "[n] children/*[1]; [t] CSFsubscriptBrief; [n] children/*[2]; [t] CSFbaselineBrief"], ["Action", "subscript-empty-sup", "[n] children/*[1]; [n] children/*[2]"], ["Action", "superscript", "[n] children/*[1]; [t] CSFsuperscriptVerbose; [n] children/*[2]"], ["Action", "superscript-brief", "[n] children/*[1]; [t] CSFsuperscriptBrief; [n] children/*[2]"], ["Action", "superscript-baseline", "[n] children/*[1]; [t] CSFsuperscriptVerbose; [n] children/*[2];[t] CSFbaselineVerbose"], ["Action", "superscript-baseline-brief", "[n] children/*[1]; [t] CSFsuperscriptBrief; [n] children/*[2];[t] CSFbaselineBrief"], ["Action", "superscript-empty-sub", "[n] children/*[1]; [n] children/*[2]"], ["Action", "double-prime", "[t] \"″\" (grammar:translate)"], ["Action", "triple-prime", "[t] \"‴\" (grammar:translate)"], ["Action", "quadruple-prime", "[t] \"⁗\" (grammar:translate)"], ["Action", "prime-subscript", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [t] CSFsubscriptVerbose; [n] children/*[1]/children/*[2]"], ["Action", "prime-subscript-brief", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [t] CSFsubscriptBrief; [n] children/*[1]/children/*[2]"], ["Action", "prime-subscript-baseline", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [t] CSFsubscriptVerbose; [n] children/*[1]/children/*[2]; [t] CSFbaselineVerbose"], ["Action", "prime-subscript-baseline-brief", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [t] CSFsubscriptBrief; [n] children/*[1]/children/*[2]; [t] CSFbaselineBrief"], ["Action", "prime-subscript-simple", "[n] children/*[1]/children/*[1]; [n] children/*[2];[n] children/*[1]/children/*[2]"], ["Action", "unit", "[t] text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "unit-combine", "[m] children/*"], ["Action", "limboth", "[n] children/*[1]; [t] CSFunderscript; [n] children/*[2]; [t] CSFoverscript; [n] children/*[3]"], ["Action", "limlower", "[n] children/*[1]; [t] CSFunderscript; [n] children/*[2]"], ["Action", "limupper", "[n] children/*[1]; [t] CSFoverscript; [n] children/*[2]"], ["Action", "limboth-end", "[n] children/*[1]; [t] CSFunderscript; [n] children/*[2]; [t] CSFoverscript; [n] children/*[3]; [t] CSFendscripts"], ["Action", "limlower-end", "[n] children/*[1]; [t] CSFunderscript; [n] children/*[2]; [t] CSFendscripts"], ["Action", "limupper-end", "[n] children/*[1]; [t] CSFoverscript; [n] children/*[2]; [t] CSFendscripts"]]}, "base/rules/mathspeak_base_romance.min": {"domain": "mathspeak", "locale": "romance", "modality": "speech", "kind": "abstract", "inherits": "base", "rules": [["Precondition", "logarithm-base", "default", "self::subscript", "children/*[1][@category=\"Logarithm\"]", "name(./children/*[2])=\"identifier\" or name(./children/*[2])=\"number\"", "./children/*[2][@role!=\"mixed\"]", "./children/*[2][@role!=\"othernumber\"]"], ["Specialized", "logarithm-base", "default", "brief"], ["Specialized", "logarithm-base", "default", "sbrief"]]}, "base/rules/prefix_base.min": {"modality": "prefix", "domain": "default", "locale": "base", "kind": "abstract", "rules": [["Precondition", "numerator", "default", "self::*", "name(../..)=\"fraction\"", "count(preceding-sibling::*)=0"], ["Precondition", "denominator", "default", "self::*", "name(../..)=\"fraction\"", "count(preceding-sibling::*)=1"], ["Precondition", "base", "default", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"superscript\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"subscript\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"overscore\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"underscore\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"tensor\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"limlower\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"limupper\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"limboth\""], ["Precondition", "exponent", "default", "self::*", "name(../..)=\"superscript\"", "count(preceding-sibling::*)=1"], ["Precondition", "subscript", "default", "self::*", "name(../..)=\"subscript\"", "count(preceding-sibling::*)=1"], ["Precondition", "overscript", "default", "self::*", "count(preceding-sibling::*)=1", "name(../..)=\"overscore\""], ["<PERSON><PERSON>", "overscript", "self::*", "count(preceding-sibling::*)=1", "name(../..)=\"limupper\""], ["<PERSON><PERSON>", "overscript", "self::*", "count(preceding-sibling::*)=2", "name(../..)=\"limboth\""], ["Precondition", "underscript", "default", "self::*", "count(preceding-sibling::*)=1", "name(../..)=\"underscore\""], ["<PERSON><PERSON>", "underscript", "self::*", "count(preceding-sibling::*)=1", "name(../..)=\"limlower\""], ["<PERSON><PERSON>", "underscript", "self::*", "count(preceding-sibling::*)=1", "name(../..)=\"limboth\""], ["Precondition", "radicand", "default", "self::*", "name(../..)=\"sqrt\""], ["<PERSON><PERSON>", "radicand", "self::*", "name(../..)=\"root\"", "count(preceding-sibling::*)=1"], ["Precondition", "index", "default", "self::*", "name(../..)=\"root\"", "count(preceding-sibling::*)=0"], ["Precondition", "leftsub", "default", "self::*", "name(../..)=\"tensor\"", "@role=\"leftsub\""], ["Precondition", "leftsub-counted", "default", "self::*", "name(../..)=\"punctuated\"", "name(../../../..)=\"tensor\"", "../../@role=\"leftsub\""], ["Precondition", "leftsuper", "default", "self::*", "name(../..)=\"tensor\"", "@role=\"leftsuper\""], ["Precondition", "leftsuper-counted", "default", "self::*", "name(../..)=\"punctuated\"", "name(../../../..)=\"tensor\"", "../../@role=\"leftsuper\""], ["Precondition", "rightsub", "default", "self::*", "name(../..)=\"tensor\"", "@role=\"rightsub\""], ["Precondition", "rightsub-counted", "default", "self::*", "name(../..)=\"punctuated\"", "name(../../../..)=\"tensor\"", "../../@role=\"rightsub\""], ["Precondition", "<PERSON><PERSON>r", "default", "self::*", "name(../..)=\"tensor\"", "@role=\"rightsuper\""], ["Precondition", "rightsuper-counted", "default", "self::*", "name(../..)=\"punctuated\"", "name(../../../..)=\"tensor\"", "../../@role=\"rightsuper\""], ["Precondition", "choice", "default", "self::line", "@role=\"binomial\"", "parent::*/parent::vector", "count(preceding-sibling::*)=0"], ["Precondition", "select", "default", "self::line", "@role=\"binomial\"", "parent::*/parent::vector", "count(preceding-sibling::*)=1"], ["Precondition", "row", "default", "self::row"], ["<PERSON><PERSON>", "row", "self::line"], ["Precondition", "cell", "default", "self::cell", "contains(@grammar,\"depth\")"], ["Precondition", "cell-simple", "default", "self::cell"]]}, "base/rules/summary_base.min": {"modality": "summary", "locale": "base", "kind": "abstract", "rules": [["Rule", "stree", "default.default", "[n] ./*[1]", "self::stree"], ["Precondition", "abstr-identifier-long", "default.default", "self::identifier", "contains(@grammar, \"collapsed\")"], ["Precondition", "abstr-identifier", "default.default", "self::identifier"], ["Precondition", "abstr-number-long", "default.default", "self::number", "contains(@grammar, \"collapsed\")"], ["Precondition", "abstr-number", "default.default", "self::number"], ["Precondition", "abstr-mixed-number-long", "default.default", "self::number", "@role=\"mixed\"", "contains(@grammar, \"collapsed\")"], ["Precondition", "abstr-mixed-number", "default.default", "self::number", "@role=\"mixed\""], ["Precondition", "abstr-text", "default.default", "self::text"], ["Precondition", "abstr-function", "default.default", "self::function"], ["Precondition", "abstr-function-brief", "mathspeak.brief", "self::function"], ["Specialized", "abstr-function-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-lim", "default.default", "self::function", "@role=\"limit function\""], ["Precondition", "abstr-lim-brief", "mathspeak.brief", "self::function", "@role=\"limit function\""], ["Specialized", "abstr-lim-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-fraction", "default.default", "self::fraction"], ["Precondition", "abstr-fraction-brief", "mathspeak.brief", "self::fraction"], ["Specialized", "abstr-fraction-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-continued-fraction", "default.default", "self::fraction", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\"]"], ["Precondition", "abstr-continued-fraction-brief", "mathspeak.brief", "self::fraction", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\"]"], ["Specialized", "abstr-continued-fraction-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-sqrt", "default.default", "self::sqrt"], ["Precondition", "abstr-sqrt-nested", "default.default", "self::sqrt", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root"], ["Precondition", "abstr-root-end", "default.default", "self::root", "contains(@grammar, \"collapsed\")", "following-sibling::* or ancestor::*/following-sibling::*"], ["Precondition", "abstr-root", "default.default", "self::root"], ["Precondition", "abstr-root-brief", "mathspeak.brief", "self::root"], ["Specialized", "abstr-root-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-root-nested-end", "default.default", "self::root", "contains(@grammar, \"collapsed\")", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root", "following-sibling::* or ancestor::*/following-sibling::*"], ["Precondition", "abstr-root-nested", "default.default", "self::root", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root"], ["Precondition", "abstr-root-nested-brief", "mathspeak.brief", "self::root", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root"], ["Specialized", "abstr-root-nested-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-superscript", "default.default", "self::superscript"], ["Precondition", "abstr-subscript", "default.default", "self::subscript"], ["Precondition", "abstr-subsup", "default.default", "self::superscript", "name(children/*[1])=\"subscript\""], ["Precondition", "abstr-infixop", "default.default", "self::infixop"], ["Precondition", "abstr-infixop-var", "default.default", "self::infixop", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-infixop-brief", "mathspeak.brief", "self::infixop"], ["Specialized", "abstr-infixop-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-addition", "default.default", "self::infixop", "@role=\"addition\""], ["Precondition", "abstr-addition-brief", "mathspeak.brief", "self::infixop", "@role=\"addition\""], ["Specialized", "abstr-addition-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-addition-var", "default.default", "self::infixop", "@role=\"addition\"", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-multiplication", "default.default", "self::infixop", "@role=\"multiplication\""], ["Precondition", "abstr-multiplication-brief", "mathspeak.brief", "self::infixop", "@role=\"multiplication\""], ["Specialized", "abstr-multiplication-brief", "mathspeak.brief", "mathspeak.sbrief"], ["<PERSON><PERSON>", "abstr-multiplication", "self::infixop", "@role=\"implicit\""], ["<PERSON><PERSON>", "abstr-multiplication-brief", "self::infixop", "@role=\"implicit\""], ["Precondition", "abstr-multiplication-var", "default.default", "self::infixop", "@role=\"multiplication\"", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["<PERSON><PERSON>", "abstr-multiplication-var", "self::infixop", "@role=\"implicit\"", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-vector", "default.default", "self::vector"], ["Precondition", "abstr-vector-brief", "mathspeak.brief", "self::vector"], ["Specialized", "abstr-vector-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-vector-var", "default.default", "self::vector", "./children/*/children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-binomial", "default.default", "self::vector", "@role=\"binomial\""], ["Specialized", "abstr-binomial", "default.default", "mathspeak.brief"], ["Specialized", "abstr-binomial", "default.default", "mathspeak.sbrief"], ["Precondition", "abstr-determinant", "default.default", "self::matrix", "@role=\"determinant\""], ["Precondition", "abstr-determinant-brief", "mathspeak.brief", "self::matrix", "@role=\"determinant\""], ["Specialized", "abstr-determinant-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-determinant-var", "default.default", "self::matrix", "@role=\"determinant\"", "./children/*/children/*/children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-squarematrix", "default.default", "self::matrix", "@role=\"squarematrix\""], ["Precondition", "abstr-squarematrix-brief", "mathspeak.brief", "self::matrix", "@role=\"squarematrix\""], ["Specialized", "abstr-squarematrix-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-rowvector", "default.default", "self::matrix", "@role=\"rowvector\""], ["Precondition", "abstr-rowvector-brief", "mathspeak.brief", "self::matrix", "@role=\"rowvector\""], ["Specialized", "abstr-rowvector-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-rowvector-var", "default.default", "self::matrix", "@role=\"rowvector\"", "./children/*/children/*/children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-matrix", "default.default", "self::matrix"], ["Precondition", "abstr-matrix-brief", "mathspeak.brief", "self::matrix"], ["Specialized", "abstr-matrix-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-matrix-var", "default.default", "self::matrix", "./children/*/children/*/children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-cases", "default.default", "self::cases"], ["Precondition", "abstr-cases-brief", "mathspeak.brief", "self::cases"], ["Specialized", "abstr-cases-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-cases-var", "default.default", "self::cases", "./children/row/children/cell/children/punctuation[@role=\"ellipsis\"]or ./children/line/children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-punctuated", "default.default", "self::punctuated"], ["Precondition", "abstr-punctuated-brief", "mathspeak.brief", "self::punctuated"], ["Specialized", "abstr-punctuated-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-punctuated-var", "default.default", "self::punctuated", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-bigop", "default.default", "self::bigop"], ["Precondition", "abstr-integral", "default.default", "self::*", "@role=\"integral\""], ["Precondition", "abstr-relation", "default.default", "self::relseq", "count(./children/*)=2"], ["Precondition", "abstr-relation-seq", "default.default", "self::relseq", "count(./children/*)>2"], ["Precondition", "abstr-relation-seq-brief", "mathspeak.brief", "self::relseq", "count(./children/*)>2"], ["Specialized", "abstr-relation-seq-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-relation-var", "default.default", "self::relseq", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["<PERSON><PERSON>", "abstr-relation", "self::multirel", "@role!=\"unknown\"", "count(./children/*)>2"], ["<PERSON><PERSON>", "abstr-relation-var", "self::multirel", "@role!=\"unknown\"", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-multirel", "default.default", "self::multirel", "count(./children/*)>2"], ["Precondition", "abstr-multirel-brief", "mathspeak.brief", "self::multirel", "count(./children/*)>2"], ["Specialized", "abstr-multirel-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-multirel-var", "default.default", "self::multirel", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-table", "default.default", "self::table"], ["Precondition", "abstr-line", "default.default", "self::line"], ["Precondition", "abstr-row", "default.default", "self::row"], ["Precondition", "abstr-cell", "default.default", "self::cell"]]}, "base/rules/summary_base_romance.min": {"modality": "summary", "locale": "romance", "kind": "abstract", "inherits": "base", "rules": [["Precondition", "collapsed-masculine", "default.default", "self::*[@grammar]", "contains(@grammar, \"gender:m\")", "contains(@grammar, \"collapsed\")"], ["Specialized", "collapsed-masculine", "default.default", "mathspeak.brief"], ["Specialized", "collapsed-masculine", "default.default", "mathspeak.sbrief"], ["Precondition", "collapsed-feminine", "default.default", "self::*[@grammar]", "contains(@grammar, \"gender:f\")", "contains(@grammar, \"collapsed\")"], ["Specialized", "collapsed-feminine", "default.default", "mathspeak.brief"], ["Specialized", "collapsed-feminine", "default.default", "mathspeak.sbrief"], ["Rule", "no-collapsed", "default.default", "[t] \"\"", "self::*[@grammar]", "contains(@grammar, \"gender\")", "not(contains(@grammar, \"collapsed\"))"], ["SpecializedRule", "no-collapsed", "default.default", "mathspeak.brief"], ["SpecializedRule", "no-collapsed", "default.default", "mathspeak.sbrief"]]}}