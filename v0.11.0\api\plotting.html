


<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
      
      
      
        <link rel="prev" href="search.html">
      
      
        <link rel="next" href="stop.html">
      
      
      <link rel="icon" href="">
    
    
      
        <title>Plotting tools - PyVRP 0.11.0 documentation</title>
      
    
    
      
        
      
      


    
    
      
    
    
      
        
        
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
        <link rel="stylesheet" type="text/css" href="../_static/sphinx_immaterial_theme.96fe8683ff2bd71e9.min.css?v=13adf062" />
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="orange" data-md-color-accent="yellow">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#pyvrp.plotting.plot_coordinates.plot_coordinates" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
      <div data-md-color-scheme="default" data-md-component="outdated" hidden>
        
      </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../index.html" title="PyVRP 0.11.0 documentation" class="md-header__button md-logo" aria-label="PyVRP 0.11.0 documentation" data-md-component="logo">
      <img src="../_static/icon.svg" alt="logo">
    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            PyVRP 0.11.0 documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Plotting tools
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme="default" data-md-color-primary="orange" data-md-color-accent="yellow"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a7 7 0 0 1 7 7c0 2.38-1.19 4.47-3 5.74V17a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1v-2.26C6.19 13.47 5 11.38 5 9a7 7 0 0 1 7-7M9 21v-1h6v1a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1m3-17a5 5 0 0 0-5 5c0 2.05 1.23 3.81 3 4.58V16h4v-2.42c1.77-.77 3-2.53 3-4.58a5 5 0 0 0-5-5"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme="slate" data-md-color-primary="orange" data-md-color-accent="yellow"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a7 7 0 0 0-7 7c0 2.38 1.19 4.47 3 5.74V17a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-2.26c1.81-1.27 3-3.36 3-5.74a7 7 0 0 0-7-7M9 21a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-1H9z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
      <div class="md-header__source">
        <a href="https://github.com/PyVRP/PyVRP/" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
  </div>
  <div class="md-source__repository">
    PyVRP
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../index.html" title="PyVRP 0.11.0 documentation" class="md-nav__button md-logo" aria-label="PyVRP 0.11.0 documentation" data-md-component="logo">
      <img src="../_static/icon.svg" alt="logo">
    </a>
    PyVRP 0.11.0 documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/PyVRP/PyVRP/" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
  </div>
  <div class="md-source__repository">
    PyVRP
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_1" >
        
          
          <label class="md-nav__link" for="__nav_1" id="__nav_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_1">
            <span class="md-nav__icon md-icon"></span>
            Getting started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/introduction_to_vrp.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/introduction_to_vrp.rst (reference label)">A brief introduction to VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/introduction_to_hgs.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/introduction_to_hgs.rst (reference label)">A brief introduction to HGS</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/installation.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/installation.rst (reference label)">Installation instructions</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/getting_help.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/getting_help.rst (reference label)">Getting help</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/faq.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/faq.rst (reference label)">FAQ</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/concepts.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/concepts.rst (reference label)">Concepts</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/benchmarks.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/benchmarks.rst (reference label)">Benchmarks</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/citing.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/citing.rst (reference label)">Citing Py<wbr>VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Examples
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Examples
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../examples/quick_tutorial.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/quick_tutorial.ipynb (reference label)">A quick tutorial</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../examples/basic_vrps.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/basic_vrps.ipynb (reference label)">Classic VRPs</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../examples/using_pyvrp_components.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/using_pyvrp_components.ipynb (reference label)">Using Py<wbr>VRP’s components</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
    
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    API reference
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            API reference
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="pyvrp.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/pyvrp.rst (reference label)">Py<wbr>VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="crossover.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/crossover.rst (reference label)">Crossover operators</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="diversity.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/diversity.rst (reference label)">Diversity measures</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="repair.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/repair.rst (reference label)">Repair operators</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="search.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/search.rst (reference label)">Search methods</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    <span title="/api/plotting.rst (reference label)">Plotting tools</span>
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="#" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/plotting.rst (reference label)">Plotting tools</span>
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_coordinates.plot_coordinates" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_coordinates.plot_coordinates (Python function) — Plots coordinates for clients and depot.">plot_<wbr>coordinates</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_demands.plot_demands" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_demands.plot_demands (Python function) — Plots demands for clients, as vertical bars sorted by demand.">plot_<wbr>demands</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_diversity.plot_diversity" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_diversity.plot_diversity (Python function) — Plots population diversity statistics.">plot_<wbr>diversity</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_instance.plot_instance" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_instance.plot_instance (Python function) — Plots client coordinate, time window and demand data of the given instance.">plot_<wbr>instance</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_objectives.plot_objectives" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_objectives.plot_objectives (Python function) — Plots each subpopulation&#39;s objective values.">plot_<wbr>objectives</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_result.plot_result" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_result.plot_result (Python function) — Plots the results of a run, including the best solution and detailed statistics about the algorithm&#39;s performance.">plot_<wbr>result</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_route_schedule.plot_route_schedule (Python function) — Plots a route schedule. This function plots multiple time statistics as a function of distance travelled:">plot_<wbr>route_<wbr>schedule</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_runtimes.plot_runtimes" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_runtimes.plot_runtimes (Python function) — Plots iteration runtimes.">plot_<wbr>runtimes</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_solution.plot_solution" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_solution.plot_solution (Python function) — Plots the given solution.">plot_<wbr>solution</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_time_windows.plot_time_windows" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_time_windows.plot_time_windows (Python function) — Plots client time windows, as vertical bars sorted by time window.">plot_<wbr>time_<wbr>windows</span>
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="stop.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/stop.rst (reference label)">Stopping criteria</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Developing Py<wbr>VRP
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Developing Py<wbr>VRP
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/benchmarking.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/benchmarking.rst (reference label)">Benchmarking</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/contributing.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/contributing.rst (reference label)">Contributing</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/supported_vrplib_fields.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/supported_vrplib_fields.rst (reference label)">The VRPLIB format</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/new_vrp_variants.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/new_vrp_variants.rst (reference label)">Supporting new VRP variants</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/glossary.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/glossary.rst (reference label)">Glossary</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary">
  
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_coordinates.plot_coordinates" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_coordinates.plot_coordinates (Python function) — Plots coordinates for clients and depot.">plot_<wbr>coordinates</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_demands.plot_demands" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_demands.plot_demands (Python function) — Plots demands for clients, as vertical bars sorted by demand.">plot_<wbr>demands</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_diversity.plot_diversity" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_diversity.plot_diversity (Python function) — Plots population diversity statistics.">plot_<wbr>diversity</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_instance.plot_instance" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_instance.plot_instance (Python function) — Plots client coordinate, time window and demand data of the given instance.">plot_<wbr>instance</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_objectives.plot_objectives" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_objectives.plot_objectives (Python function) — Plots each subpopulation&#39;s objective values.">plot_<wbr>objectives</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_result.plot_result" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_result.plot_result (Python function) — Plots the results of a run, including the best solution and detailed statistics about the algorithm&#39;s performance.">plot_<wbr>result</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_route_schedule.plot_route_schedule (Python function) — Plots a route schedule. This function plots multiple time statistics as a function of distance travelled:">plot_<wbr>route_<wbr>schedule</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_runtimes.plot_runtimes" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_runtimes.plot_runtimes (Python function) — Plots iteration runtimes.">plot_<wbr>runtimes</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_solution.plot_solution" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_solution.plot_solution (Python function) — Plots the given solution.">plot_<wbr>solution</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pyvrp.plotting.plot_time_windows.plot_time_windows" class="md-nav__link">
    
      <span aria-label="Python function" class="objinfo-icon objinfo-icon__procedure" title="Python function">F</span>
    
    <span class="md-ellipsis">
      <span title="pyvrp.plotting.plot_time_windows.plot_time_windows (Python function) — Plots client time windows, as vertical bars sorted by time window.">plot_<wbr>time_<wbr>windows</span>
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset" role="main">
                
                
                  


<h1 id="plotting-tools"><span id="module-pyvrp.plotting"></span>Plotting tools<a class="headerlink" href="#plotting-tools" title="Link to this heading">¶</a></h1>
<p>The <a class="reference internal" href="#module-pyvrp.plotting" title="pyvrp.plotting: Plotting tools"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pyvrp.plotting</span></code></a> module contains various functions for plotting problem instances and solutions to those problem instances.
These can be used to better understand your problem, and to help investigate the solutions returned by the genetic algorithm.</p>
<span class="target" id="module-pyvrp.plotting.plot_coordinates"></span><dl class="py function objdesc">
<dt class="sig sig-object highlight sig-wrap py" id="pyvrp.plotting.plot_coordinates.plot_coordinates">
<span class="sig-name descname"><span class="pre">plot_coordinates</span></span><span class="sig-paren">(</span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_coordinates.plot_coordinates.data" title="pyvrp.plotting.plot_coordinates.plot_coordinates.data (Python parameter) — Data instance."><span class="n"><span class="pre">data</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem."><span class="pre">ProblemData</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_coordinates.plot_coordinates.title" title="pyvrp.plotting.plot_coordinates.plot_coordinates.title (Python parameter) — Title to add to the plot."><span class="n"><span class="pre">title</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="s1">&#39;Coordinates&#39;</span></code></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_coordinates.plot_coordinates.ax" title="pyvrp.plotting.plot_coordinates.plot_coordinates.ax (Python parameter) — Axes object to draw the plot on."><span class="n"><span class="pre">ax</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)"><span class="pre">Axes</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em></span><span class="sig-paren">)</span><a class="reference external" href="https://github.com/PyVRP/PyVRP//blob/b973389/pyvrp/plotting/plot_coordinates.py#L7"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pyvrp.plotting.plot_coordinates.plot_coordinates" title="Link to this definition">¶</a></dt>
<dd><p>Plots coordinates for clients and depot.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><dl class="simple api-field highlight">
<dt id="pyvrp.plotting.plot_coordinates.plot_coordinates.data"><span class="n sig-name">data</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem.">ProblemData</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_coordinates.plot_coordinates.data" title="Permalink to this definition">¶</a></dt><dd><p>Data instance.</p>
</dd>
<dt id="pyvrp.plotting.plot_coordinates.plot_coordinates.title"><span class="n sig-name">title</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="s1">&#39;Coordinates&#39;</span></code><a class="headerlink" href="#pyvrp.plotting.plot_coordinates.plot_coordinates.title" title="Permalink to this definition">¶</a></dt><dd><p>Title to add to the plot.</p>
</dd>
<dt id="pyvrp.plotting.plot_coordinates.plot_coordinates.ax"><span class="n sig-name">ax</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)">Axes</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_coordinates.plot_coordinates.ax" title="Permalink to this definition">¶</a></dt><dd><p>Axes object to draw the plot on. One will be created if not provided.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<span class="target" id="module-pyvrp.plotting.plot_demands"></span><dl class="py function objdesc">
<dt class="sig sig-object highlight sig-wrap py" id="pyvrp.plotting.plot_demands.plot_demands">
<span class="sig-name descname"><span class="pre">plot_demands</span></span><span class="sig-paren">(</span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_demands.plot_demands.data" title="pyvrp.plotting.plot_demands.plot_demands.data (Python parameter) — Data instance."><span class="n"><span class="pre">data</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem."><span class="pre">ProblemData</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_demands.plot_demands.dimension" title="pyvrp.plotting.plot_demands.plot_demands.dimension (Python parameter) — Load dimension to plot."><span class="n"><span class="pre">dimension</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="mi">0</span></code></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_demands.plot_demands.title" title="pyvrp.plotting.plot_demands.plot_demands.title (Python parameter) — Title to add to the plot."><span class="n"><span class="pre">title</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_demands.plot_demands.ax" title="pyvrp.plotting.plot_demands.plot_demands.ax (Python parameter) — Axes object to draw the plot on."><span class="n"><span class="pre">ax</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)"><span class="pre">Axes</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em></span><span class="sig-paren">)</span><a class="reference external" href="https://github.com/PyVRP/PyVRP//blob/b973389/pyvrp/plotting/plot_demands.py#L7"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pyvrp.plotting.plot_demands.plot_demands" title="Link to this definition">¶</a></dt>
<dd><p>Plots demands for clients, as vertical bars sorted by demand.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><dl class="simple api-field highlight">
<dt id="pyvrp.plotting.plot_demands.plot_demands.data"><span class="n sig-name">data</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem.">ProblemData</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_demands.plot_demands.data" title="Permalink to this definition">¶</a></dt><dd><p>Data instance.</p>
</dd>
<dt id="pyvrp.plotting.plot_demands.plot_demands.dimension"><span class="n sig-name">dimension</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="mi">0</span></code><a class="headerlink" href="#pyvrp.plotting.plot_demands.plot_demands.dimension" title="Permalink to this definition">¶</a></dt><dd><p>Load dimension to plot. Defaults to the first dimension.</p>
</dd>
<dt id="pyvrp.plotting.plot_demands.plot_demands.title"><span class="n sig-name">title</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_demands.plot_demands.title" title="Permalink to this definition">¶</a></dt><dd><p>Title to add to the plot.</p>
</dd>
<dt id="pyvrp.plotting.plot_demands.plot_demands.ax"><span class="n sig-name">ax</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)">Axes</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_demands.plot_demands.ax" title="Permalink to this definition">¶</a></dt><dd><p>Axes object to draw the plot on. One will be created if not provided.</p>
</dd>
</dl>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference external" href="https://docs.python.org/3/library/exceptions.html#ValueError" title="(in Python v3.13)"><strong>ValueError</strong></a> – When the load dimension is out of bounds for the given data instance.</p>
</dd>
</dl>
</dd></dl>

<span class="target" id="module-pyvrp.plotting.plot_diversity"></span><dl class="py function objdesc">
<dt class="sig sig-object highlight py" id="pyvrp.plotting.plot_diversity.plot_diversity">
<span class="sig-name descname"><span class="pre">plot_diversity</span></span><span class="sig-paren">(</span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_diversity.plot_diversity.result" title="pyvrp.plotting.plot_diversity.plot_diversity.result (Python parameter) — Result for which to plot diversity."><span class="n"><span class="pre">result</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp.Result.Result" title="pyvrp.Result.Result (Python class) — Stores the outcomes of a single run. An instance of this class is returned once the GeneticAlgorithm completes."><span class="pre">Result</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_diversity.plot_diversity.ax" title="pyvrp.plotting.plot_diversity.plot_diversity.ax (Python parameter) — Axes object to draw the plot on."><span class="n"><span class="pre">ax</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)"><span class="pre">Axes</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em></span><span class="sig-paren">)</span><a class="reference external" href="https://github.com/PyVRP/PyVRP//blob/b973389/pyvrp/plotting/plot_diversity.py#L7"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pyvrp.plotting.plot_diversity.plot_diversity" title="Link to this definition">¶</a></dt>
<dd><p>Plots population diversity statistics.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><dl class="simple api-field highlight">
<dt id="pyvrp.plotting.plot_diversity.plot_diversity.result"><span class="n sig-name">result</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp.Result.Result" title="pyvrp.Result.Result (Python class) — Stores the outcomes of a single run. An instance of this class is returned once the GeneticAlgorithm completes.">Result</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_diversity.plot_diversity.result" title="Permalink to this definition">¶</a></dt><dd><p>Result for which to plot diversity.</p>
</dd>
<dt id="pyvrp.plotting.plot_diversity.plot_diversity.ax"><span class="n sig-name">ax</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)">Axes</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_diversity.plot_diversity.ax" title="Permalink to this definition">¶</a></dt><dd><p>Axes object to draw the plot on. One will be created if not
provided.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<span class="target" id="module-pyvrp.plotting.plot_instance"></span><dl class="py function objdesc">
<dt class="sig sig-object highlight sig-wrap py" id="pyvrp.plotting.plot_instance.plot_instance">
<span class="sig-name descname"><span class="pre">plot_instance</span></span><span class="sig-paren">(</span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_instance.plot_instance.data" title="pyvrp.plotting.plot_instance.plot_instance.data (Python parameter) — Data instance."><span class="n"><span class="pre">data</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem."><span class="pre">ProblemData</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_instance.plot_instance.fig" title="pyvrp.plotting.plot_instance.plot_instance.fig (Python parameter) — Optional Figure to draw on."><span class="n"><span class="pre">fig</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.figure.Figure.html#matplotlib.figure.Figure" title="(in Matplotlib v3.10.3)"><span class="pre">Figure</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em></span><span class="sig-paren">)</span><a class="reference external" href="https://github.com/PyVRP/PyVRP//blob/b973389/pyvrp/plotting/plot_instance.py#L9"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pyvrp.plotting.plot_instance.plot_instance" title="Link to this definition">¶</a></dt>
<dd><p>Plots client coordinate, time window and demand data of the given instance.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><dl class="simple api-field highlight">
<dt id="pyvrp.plotting.plot_instance.plot_instance.data"><span class="n sig-name">data</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem.">ProblemData</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_instance.plot_instance.data" title="Permalink to this definition">¶</a></dt><dd><p>Data instance.</p>
</dd>
<dt id="pyvrp.plotting.plot_instance.plot_instance.fig"><span class="n sig-name">fig</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.figure.Figure.html#matplotlib.figure.Figure" title="(in Matplotlib v3.10.3)">Figure</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_instance.plot_instance.fig" title="Permalink to this definition">¶</a></dt><dd><p>Optional Figure to draw on. One will be created if not provided.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<span class="target" id="module-pyvrp.plotting.plot_objectives"></span><dl class="py function objdesc">
<dt class="sig sig-object highlight sig-wrap py" id="pyvrp.plotting.plot_objectives.plot_objectives">
<span class="sig-name descname"><span class="pre">plot_objectives</span></span><span class="sig-paren">(</span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_objectives.plot_objectives.result" title="pyvrp.plotting.plot_objectives.plot_objectives.result (Python parameter) — Result for which to plot objectives."><span class="n"><span class="pre">result</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp.Result.Result" title="pyvrp.Result.Result (Python class) — Stores the outcomes of a single run. An instance of this class is returned once the GeneticAlgorithm completes."><span class="pre">Result</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_objectives.plot_objectives.num_to_skip" title="pyvrp.plotting.plot_objectives.plot_objectives.num_to_skip (Python parameter) — Number of initial iterations to skip when plotting."><span class="n"><span class="pre">num_to_skip</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_objectives.plot_objectives.ax" title="pyvrp.plotting.plot_objectives.plot_objectives.ax (Python parameter) — Axes object to draw the plot on."><span class="n"><span class="pre">ax</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)"><span class="pre">Axes</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_objectives.plot_objectives.ylim_adjust" title="pyvrp.plotting.plot_objectives.plot_objectives.ylim_adjust (Python parameter) — Bounds the y-axis to (best * ylim_adjust[0], best * ylim_adjust[1]) where best denotes the best found feasible objective value."><span class="n"><span class="pre">ylim_adjust</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#tuple" title="(in Python v3.13)"><span class="pre">tuple</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="p"><span class="pre">]</span></span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="p">(</span><span class="mf">0.95</span><span class="p">,</span> <span class="mf">1.15</span><span class="p">)</span></code></em></span><span class="sig-paren">)</span><a class="reference external" href="https://github.com/PyVRP/PyVRP//blob/b973389/pyvrp/plotting/plot_objectives.py#L7"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pyvrp.plotting.plot_objectives.plot_objectives" title="Link to this definition">¶</a></dt>
<dd><p>Plots each subpopulation’s objective values.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><dl class="simple api-field highlight">
<dt id="pyvrp.plotting.plot_objectives.plot_objectives.result"><span class="n sig-name">result</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp.Result.Result" title="pyvrp.Result.Result (Python class) — Stores the outcomes of a single run. An instance of this class is returned once the GeneticAlgorithm completes.">Result</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_objectives.plot_objectives.result" title="Permalink to this definition">¶</a></dt><dd><p>Result for which to plot objectives.</p>
</dd>
<dt id="pyvrp.plotting.plot_objectives.plot_objectives.num_to_skip"><span class="n sig-name">num_to_skip</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_objectives.plot_objectives.num_to_skip" title="Permalink to this definition">¶</a></dt><dd><p>Number of initial iterations to skip when plotting. Early iterations
often have very high objective values, and obscure what’s going on
later in the search. The default skips the first 5% of iterations.</p>
</dd>
<dt id="pyvrp.plotting.plot_objectives.plot_objectives.ax"><span class="n sig-name">ax</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)">Axes</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_objectives.plot_objectives.ax" title="Permalink to this definition">¶</a></dt><dd><p>Axes object to draw the plot on. One will be created if not provided.</p>
</dd>
<dt id="pyvrp.plotting.plot_objectives.plot_objectives.ylim_adjust"><span class="n sig-name">ylim_adjust</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#tuple" title="(in Python v3.13)">tuple</a><span class="p">[</span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)">float</a><span class="p">,</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)">float</a><span class="p">]</span></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="p">(</span><span class="mf">0.95</span><span class="p">,</span> <span class="mf">1.15</span><span class="p">)</span></code><a class="headerlink" href="#pyvrp.plotting.plot_objectives.plot_objectives.ylim_adjust" title="Permalink to this definition">¶</a></dt><dd><p>Bounds the y-axis to <code class="docutils literal notranslate"><span class="pre">(best</span> <span class="pre">*</span> <span class="pre">ylim_adjust[0],</span> <span class="pre">best</span> <span class="pre">*</span> <span class="pre">ylim_adjust[1])</span></code>
where <code class="docutils literal notranslate"><span class="pre">best</span></code> denotes the best found feasible objective value. Default
(0.95, 1.15).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<span class="target" id="module-pyvrp.plotting.plot_result"></span><dl class="py function objdesc">
<dt class="sig sig-object highlight sig-wrap py" id="pyvrp.plotting.plot_result.plot_result">
<span class="sig-name descname"><span class="pre">plot_result</span></span><span class="sig-paren">(</span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_result.plot_result.result" title="pyvrp.plotting.plot_result.plot_result.result (Python parameter) — Result to be plotted."><span class="n"><span class="pre">result</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp.Result.Result" title="pyvrp.Result.Result (Python class) — Stores the outcomes of a single run. An instance of this class is returned once the GeneticAlgorithm completes."><span class="pre">Result</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_result.plot_result.data" title="pyvrp.plotting.plot_result.plot_result.data (Python parameter) — Data instance underlying the result's solution."><span class="n"><span class="pre">data</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem."><span class="pre">ProblemData</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_result.plot_result.fig" title="pyvrp.plotting.plot_result.plot_result.fig (Python parameter) — Optional Figure to draw on."><span class="n"><span class="pre">fig</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.figure.Figure.html#matplotlib.figure.Figure" title="(in Matplotlib v3.10.3)"><span class="pre">Figure</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em></span><span class="sig-paren">)</span><a class="reference external" href="https://github.com/PyVRP/PyVRP//blob/b973389/pyvrp/plotting/plot_result.py#L11"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pyvrp.plotting.plot_result.plot_result" title="Link to this definition">¶</a></dt>
<dd><p>Plots the results of a run, including the best solution and detailed
statistics about the algorithm’s performance.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><dl class="simple api-field highlight">
<dt id="pyvrp.plotting.plot_result.plot_result.result"><span class="n sig-name">result</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp.Result.Result" title="pyvrp.Result.Result (Python class) — Stores the outcomes of a single run. An instance of this class is returned once the GeneticAlgorithm completes.">Result</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_result.plot_result.result" title="Permalink to this definition">¶</a></dt><dd><p>Result to be plotted.</p>
</dd>
<dt id="pyvrp.plotting.plot_result.plot_result.data"><span class="n sig-name">data</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem.">ProblemData</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_result.plot_result.data" title="Permalink to this definition">¶</a></dt><dd><p>Data instance underlying the result’s solution.</p>
</dd>
<dt id="pyvrp.plotting.plot_result.plot_result.fig"><span class="n sig-name">fig</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.figure.Figure.html#matplotlib.figure.Figure" title="(in Matplotlib v3.10.3)">Figure</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_result.plot_result.fig" title="Permalink to this definition">¶</a></dt><dd><p>Optional Figure to draw on. One will be created if not provided.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<span class="target" id="module-pyvrp.plotting.plot_route_schedule"></span><dl class="py function objdesc">
<dt class="sig sig-object highlight sig-wrap py" id="pyvrp.plotting.plot_route_schedule.plot_route_schedule">
<span class="sig-name descname"><span class="pre">plot_route_schedule</span></span><span class="sig-paren">(</span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.data" title="pyvrp.plotting.plot_route_schedule.plot_route_schedule.data (Python parameter) — Data instance for which to plot the route schedule."><span class="n"><span class="pre">data</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem."><span class="pre">ProblemData</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.route" title="pyvrp.plotting.plot_route_schedule.plot_route_schedule.route (Python parameter) — Route (list of clients) whose schedule to plot."><span class="n"><span class="pre">route</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.Route" title="pyvrp._pyvrp.Route (Python class) — A simple class that stores the route plan and some statistics. Internally, a route consists of one or more Trip objects."><span class="pre">Route</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.load_dimension" title="pyvrp.plotting.plot_route_schedule.plot_route_schedule.load_dimension (Python parameter) — Load dimension to plot."><span class="n"><span class="pre">load_dimension</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="mi">0</span></code></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.legend" title="pyvrp.plotting.plot_route_schedule.plot_route_schedule.legend (Python parameter) — Whether or not to show the legends."><span class="n"><span class="pre">legend</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">True</span></code></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.title" title="pyvrp.plotting.plot_route_schedule.plot_route_schedule.title (Python parameter) — Title to add to the plot."><span class="n"><span class="pre">title</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.ax" title="pyvrp.plotting.plot_route_schedule.plot_route_schedule.ax (Python parameter) — Axes object to draw the plot on."><span class="n"><span class="pre">ax</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)"><span class="pre">Axes</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em></span><span class="sig-paren">)</span><a class="reference external" href="https://github.com/PyVRP/PyVRP//blob/b973389/pyvrp/plotting/plot_route_schedule.py#L8"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule" title="Link to this definition">¶</a></dt>
<dd><p>Plots a route schedule. This function plots multiple time statistics
as a function of distance travelled:</p>
<ul class="simple">
<li><p>Solid: earliest possible trajectory / time, using time warp if the route
is infeasible.</p></li>
<li><p>Shaded: slack up to latest possible trajectory / time, only if no time
warp on the route.</p></li>
<li><p>Dash-dotted: driving and service time, excluding wait time and time warp.</p></li>
<li><p>Dotted: pure driving time.</p></li>
<li><p>Grey shaded background: remaining load in the vehicle for the provided
load dimension.</p></li>
</ul>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><dl class="simple api-field highlight">
<dt id="pyvrp.plotting.plot_route_schedule.plot_route_schedule.data"><span class="n sig-name">data</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem.">ProblemData</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.data" title="Permalink to this definition">¶</a></dt><dd><p>Data instance for which to plot the route schedule.</p>
</dd>
<dt id="pyvrp.plotting.plot_route_schedule.plot_route_schedule.route"><span class="n sig-name">route</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.Route" title="pyvrp._pyvrp.Route (Python class) — A simple class that stores the route plan and some statistics. Internally, a route consists of one or more Trip objects.">Route</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.route" title="Permalink to this definition">¶</a></dt><dd><p>Route (list of clients) whose schedule to plot.</p>
</dd>
<dt id="pyvrp.plotting.plot_route_schedule.plot_route_schedule.load_dimension"><span class="n sig-name">load_dimension</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="mi">0</span></code><a class="headerlink" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.load_dimension" title="Permalink to this definition">¶</a></dt><dd><p>Load dimension to plot. Defaults to the first dimension, if it exists.</p>
</dd>
<dt id="pyvrp.plotting.plot_route_schedule.plot_route_schedule.legend"><span class="n sig-name">legend</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">True</span></code><a class="headerlink" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.legend" title="Permalink to this definition">¶</a></dt><dd><p>Whether or not to show the legends. Default True.</p>
</dd>
<dt id="pyvrp.plotting.plot_route_schedule.plot_route_schedule.title"><span class="n sig-name">title</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.title" title="Permalink to this definition">¶</a></dt><dd><p>Title to add to the plot.</p>
</dd>
<dt id="pyvrp.plotting.plot_route_schedule.plot_route_schedule.ax"><span class="n sig-name">ax</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)">Axes</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_route_schedule.plot_route_schedule.ax" title="Permalink to this definition">¶</a></dt><dd><p>Axes object to draw the plot on. One will be created if not provided.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<span class="target" id="module-pyvrp.plotting.plot_runtimes"></span><dl class="py function objdesc">
<dt class="sig sig-object highlight py" id="pyvrp.plotting.plot_runtimes.plot_runtimes">
<span class="sig-name descname"><span class="pre">plot_runtimes</span></span><span class="sig-paren">(</span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_runtimes.plot_runtimes.result" title="pyvrp.plotting.plot_runtimes.plot_runtimes.result (Python parameter) — Result for which to plot runtimes."><span class="n"><span class="pre">result</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp.Result.Result" title="pyvrp.Result.Result (Python class) — Stores the outcomes of a single run. An instance of this class is returned once the GeneticAlgorithm completes."><span class="pre">Result</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_runtimes.plot_runtimes.ax" title="pyvrp.plotting.plot_runtimes.plot_runtimes.ax (Python parameter) — Axes object to draw the plot on."><span class="n"><span class="pre">ax</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)"><span class="pre">Axes</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em></span><span class="sig-paren">)</span><a class="reference external" href="https://github.com/PyVRP/PyVRP//blob/b973389/pyvrp/plotting/plot_runtimes.py#L7"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pyvrp.plotting.plot_runtimes.plot_runtimes" title="Link to this definition">¶</a></dt>
<dd><p>Plots iteration runtimes.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><dl class="simple api-field highlight">
<dt id="pyvrp.plotting.plot_runtimes.plot_runtimes.result"><span class="n sig-name">result</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp.Result.Result" title="pyvrp.Result.Result (Python class) — Stores the outcomes of a single run. An instance of this class is returned once the GeneticAlgorithm completes.">Result</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_runtimes.plot_runtimes.result" title="Permalink to this definition">¶</a></dt><dd><p>Result for which to plot runtimes.</p>
</dd>
<dt id="pyvrp.plotting.plot_runtimes.plot_runtimes.ax"><span class="n sig-name">ax</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)">Axes</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_runtimes.plot_runtimes.ax" title="Permalink to this definition">¶</a></dt><dd><p>Axes object to draw the plot on. One will be created if not provided.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<span class="target" id="module-pyvrp.plotting.plot_solution"></span><dl class="py function objdesc">
<dt class="sig sig-object highlight sig-wrap py" id="pyvrp.plotting.plot_solution.plot_solution">
<span class="sig-name descname"><span class="pre">plot_solution</span></span><span class="sig-paren">(</span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_solution.plot_solution.solution" title="pyvrp.plotting.plot_solution.plot_solution.solution (Python parameter) — Solution to plot."><span class="n"><span class="pre">solution</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.Solution" title="pyvrp._pyvrp.Solution (Python class) — Encodes VRP solutions."><span class="pre">Solution</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_solution.plot_solution.data" title="pyvrp.plotting.plot_solution.plot_solution.data (Python parameter) — Data instance underlying the solution."><span class="n"><span class="pre">data</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem."><span class="pre">ProblemData</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_solution.plot_solution.plot_clients" title="pyvrp.plotting.plot_solution.plot_solution.plot_clients (Python parameter) — Whether to plot all clients as dots."><span class="n"><span class="pre">plot_clients</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">False</span></code></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_solution.plot_solution.ax" title="pyvrp.plotting.plot_solution.plot_solution.ax (Python parameter) — Axes object to draw the plot on."><span class="n"><span class="pre">ax</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)"><span class="pre">Axes</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em></span><span class="sig-paren">)</span><a class="reference external" href="https://github.com/PyVRP/PyVRP//blob/b973389/pyvrp/plotting/plot_solution.py#L7"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pyvrp.plotting.plot_solution.plot_solution" title="Link to this definition">¶</a></dt>
<dd><p>Plots the given solution.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><dl class="simple api-field highlight">
<dt id="pyvrp.plotting.plot_solution.plot_solution.solution"><span class="n sig-name">solution</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.Solution" title="pyvrp._pyvrp.Solution (Python class) — Encodes VRP solutions.">Solution</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_solution.plot_solution.solution" title="Permalink to this definition">¶</a></dt><dd><p>Solution to plot.</p>
</dd>
<dt id="pyvrp.plotting.plot_solution.plot_solution.data"><span class="n sig-name">data</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem.">ProblemData</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_solution.plot_solution.data" title="Permalink to this definition">¶</a></dt><dd><p>Data instance underlying the solution.</p>
</dd>
<dt id="pyvrp.plotting.plot_solution.plot_solution.plot_clients"><span class="n sig-name">plot_clients</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">False</span></code><a class="headerlink" href="#pyvrp.plotting.plot_solution.plot_solution.plot_clients" title="Permalink to this definition">¶</a></dt><dd><p>Whether to plot all clients as dots. Default False, which plots only
the solution’s routes.</p>
</dd>
<dt id="pyvrp.plotting.plot_solution.plot_solution.ax"><span class="n sig-name">ax</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)">Axes</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_solution.plot_solution.ax" title="Permalink to this definition">¶</a></dt><dd><p>Axes object to draw the plot on. One will be created if not provided.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<span class="target" id="module-pyvrp.plotting.plot_time_windows"></span><dl class="py function objdesc">
<dt class="sig sig-object highlight sig-wrap py" id="pyvrp.plotting.plot_time_windows.plot_time_windows">
<span class="sig-name descname"><span class="pre">plot_time_windows</span></span><span class="sig-paren">(</span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_time_windows.plot_time_windows.data" title="pyvrp.plotting.plot_time_windows.plot_time_windows.data (Python parameter) — Data instance."><span class="n"><span class="pre">data</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem."><span class="pre">ProblemData</span></a></span></span></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_time_windows.plot_time_windows.title" title="pyvrp.plotting.plot_time_windows.plot_time_windows.title (Python parameter) — Title to add to the plot."><span class="n"><span class="pre">title</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="s1">&#39;Time windows&#39;</span></code></em>, </span><span class="sig-param-decl"><em class="sig-param"><a class="n reference internal" href="#pyvrp.plotting.plot_time_windows.plot_time_windows.ax" title="pyvrp.plotting.plot_time_windows.plot_time_windows.ax (Python parameter) — Axes object to draw the plot on."><span class="n"><span class="pre">ax</span></span></a><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)"><span class="pre">Axes</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code></em></span><span class="sig-paren">)</span><a class="reference external" href="https://github.com/PyVRP/PyVRP//blob/b973389/pyvrp/plotting/plot_time_windows.py#L8"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pyvrp.plotting.plot_time_windows.plot_time_windows" title="Link to this definition">¶</a></dt>
<dd><p>Plots client time windows, as vertical bars sorted by time window.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><dl class="simple api-field highlight">
<dt id="pyvrp.plotting.plot_time_windows.plot_time_windows.data"><span class="n sig-name">data</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference internal" href="pyvrp.html#pyvrp._pyvrp.ProblemData" title="pyvrp._pyvrp.ProblemData (Python class) — Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem.">ProblemData</a></span></span><a class="headerlink" href="#pyvrp.plotting.plot_time_windows.plot_time_windows.data" title="Permalink to this definition">¶</a></dt><dd><p>Data instance.</p>
</dd>
<dt id="pyvrp.plotting.plot_time_windows.plot_time_windows.title"><span class="n sig-name">title</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="s1">&#39;Time windows&#39;</span></code><a class="headerlink" href="#pyvrp.plotting.plot_time_windows.plot_time_windows.title" title="Permalink to this definition">¶</a></dt><dd><p>Title to add to the plot.</p>
</dd>
<dt id="pyvrp.plotting.plot_time_windows.plot_time_windows.ax"><span class="n sig-name">ax</span><span class="p">:</span><span class="w"> </span><span class="n"><span class="desctype"><a class="reference external" href="https://matplotlib.org/stable/api/_as_gen/matplotlib.axes.Axes.html#matplotlib.axes.Axes" title="(in Matplotlib v3.10.3)">Axes</a><span class="w"> </span><span class="p">|</span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)">None</a></span></span><span class="w"> </span><span class="o">=</span><span class="w"> </span><code class="code python default_value docutils literal highlight highlight-python"><span class="kc">None</span></code><a class="headerlink" href="#pyvrp.plotting.plot_time_windows.plot_time_windows.ax" title="Permalink to this definition">¶</a></dt><dd><p>Axes object to draw the plot on. One will be created if not provided.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>







  
  






                
              </article>
            </div>
          
          
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  
  
  <div class="md-footer-meta md-typeset">
    
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-footer-copyright__highlight">
        &#169; Copyright 2022 - 2025, PyVRP contributors.
        
    </div>
  
    Created using
    <a href="https://www.sphinx-doc.org/" target="_blank" rel="noopener">Sphinx</a>
    8.1.3.
     and
    <a href="https://github.com/jbms/sphinx-immaterial/" target="_blank" rel="noopener">Sphinx-Immaterial</a>
  
</div>
      
    </div>
    
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    <script id="__config" type="application/json">{"base": "..", "features": ["navigation.expand", "navigation.top"], "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike", "staticVersions": [{"aliases": [], "title": "Development", "version": ""}, {"aliases": [], "title": "v0.10.0", "version": "https://pyvrp.github.io/v0.10.0"}, {"aliases": [], "title": "v0.9.0", "version": "https://pyvrp.github.io/v0.9.0"}, {"aliases": [], "title": "v0.8.1", "version": "https://pyvrp.github.io/v0.8.1"}, {"aliases": [], "title": "v0.7.0", "version": "https://pyvrp.github.io/v0.7.0"}, {"aliases": [], "title": "v0.6.0", "version": "https://pyvrp.github.io/v0.6.0"}, {"aliases": [], "title": "v0.5.0", "version": "https://pyvrp.github.io/v0.5.0"}, {"aliases": [], "title": "v0.4.4", "version": "https://pyvrp.github.io/v0.4.4"}, {"aliases": [], "title": "v0.3.0", "version": "https://pyvrp.github.io/v0.3.0"}, {"aliases": [], "title": "v0.2.1", "version": "https://pyvrp.github.io/v0.2.1"}, {"aliases": [], "title": "v0.1.0", "version": "https://pyvrp.github.io/v0.1.0"}], "versionPath": null}}</script>
    
      
        <script src="../_static/sphinx_immaterial_theme.32136f45f91ae6956.min.js?v=a7a9472a"></script>
        <script crossorigin="anonymous" integrity="sha256-Ae2Vz/4ePdIu6ZyI/5ZGsYnb+m0JlOmKPjt6XZ9JJkA=" src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.4/require.min.js"></script>
    
  </body>
</html>