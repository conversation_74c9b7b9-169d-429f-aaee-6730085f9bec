import pandas as pd
import numpy as np
from pyvrp import Model
from pyvrp.stop import MaxIterations

# Excel dosyasını oku
excel_file = r"C:\\Users\\<USER>\\OneDrive - Horoz Lojistik\\_slms_\\PyVRP\\vrp_data.xlsx"

# Clients sayfası
clients_df = pd.read_excel(excel_file, sheet_name="Clients")

# Mesafe matrisi (km sayfası)
km_df = pd.read_excel(excel_file, sheet_name="km", index_col=0)
distance_matrix = km_df.values

# Talep, zaman penceresi ve servis süresi
demands = clients_df["Demand"].to_numpy()
tw_early = clients_df["Earliest"].to_numpy()
tw_late = clients_df["Latest"].to_numpy()
service_durations = clients_df["ServiceDuration"].to_numpy()

# Depo bilgisi: ClientID == 0 olan satırın index'i
depot_index = clients_df[clients_df["ClientID"] == 0].index[0]

# Model oluştur
model = Model.from_data(
    distance_matrix=distance_matrix,
    demands=demands,
    vehicle_capacity=150,
    num_vehicles=10,
    depot=depot_index,
    tw_early=tw_early,
    tw_late=tw_late,
    service_durations=service_durations,
)

# Optimizasyonu çöz
result = model.solve(stop=MaxIterations(1000))
solution = result.best

# Rotaları yazdır
for i, route in enumerate(solution.routes):
    route_ids = [clients_df.iloc[idx]["ClientID"] for idx in route]
    print(f"Araç {i+1} rotası: {route_ids}")
