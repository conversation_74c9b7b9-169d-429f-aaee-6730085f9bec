{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# A quick tutorial\n", "\n", "This notebook provides a brief tutorial to modelling vehicle routing problems with PyVRP, introducing some of its most important modelling features:\n", "\n", "- We first solve a capacitated VRP, introducing the modelling interface and the most basic components.\n", "- We then solve a VRP with time windows, where we introduce the support PyVRP has for problems with duration constraints.\n", "- We then solve a multi-depot VRP with time windows and maximum route duration constraints.\n", "- We also solve a prize-collecting VRP with optional clients to showcase the modelling optional client visits.\n", "- We then solve a VRP with simultaneous pickup and delivery to show problems with deliveries from the depot to clients, and return shipments from clients to depots.\n", "- We briefly show how to use routing profiles to model zone restrictions.\n", "- Finally, we show how to model a VRP with reloading at various reload depots along the route."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Capacitated VRP\n", "\n", "We will first model and solve the small capacitated VRP instance with 16 clients defined [in the OR-Tools documentation](https://developers.google.com/optimization/routing/cvrp).\n", "This instance has an optimal solution of cost 6208.\n", "The data are as follows:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:10.257064Z", "iopub.status.busy": "2025-05-15T14:42:10.256877Z", "iopub.status.idle": "2025-05-15T14:42:10.261846Z", "shell.execute_reply": "2025-05-15T14:42:10.261412Z"}}, "outputs": [], "source": ["# fmt: off\n", "COORDS = [\n", "    (456, 320),  # location 0 - the depot\n", "    (228, 0),    # location 1\n", "    (912, 0),    # location 2\n", "    (0, 80),     # location 3\n", "    (114, 80),   # location 4\n", "    (570, 160),  # location 5\n", "    (798, 160),  # location 6\n", "    (342, 240),  # location 7\n", "    (684, 240),  # location 8\n", "    (570, 400),  # location 9\n", "    (912, 400),  # location 10\n", "    (114, 480),  # location 11\n", "    (228, 480),  # location 12\n", "    (342, 560),  # location 13\n", "    (684, 560),  # location 14\n", "    (0, 640),    # location 15\n", "    (798, 640),  # location 16\n", "]\n", "DEMANDS = [0, 1, 1, 2, 4, 2, 4, 8, 8, 1, 2, 1, 2, 4, 4, 8, 8]\n", "# fmt: on"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We can use the `pyvrp.Model` interface to conveniently specify our vehicle routing problem using this data.\n", "A full description of the `Model` interface is given in our [API documentation](https://pyvrp.org/api/pyvrp.html#pyvrp.Model.Model)."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:10.263643Z", "iopub.status.busy": "2025-05-15T14:42:10.263289Z", "iopub.status.idle": "2025-05-15T14:42:10.351595Z", "shell.execute_reply": "2025-05-15T14:42:10.351007Z"}}, "outputs": [], "source": ["from pyvrp import Model\n", "\n", "m = Model()\n", "m.add_vehicle_type(4, capacity=15)\n", "depot = m.add_depot(x=COORDS[0][0], y=COORDS[0][1])\n", "clients = [\n", "    m.add_client(x=COORDS[idx][0], y=COORDS[idx][1], delivery=DEMANDS[idx])\n", "    for idx in range(1, len(COORDS))\n", "]\n", "\n", "locations = [depot] + clients\n", "for frm in locations:\n", "    for to in locations:\n", "        distance = abs(frm.x - to.x) + abs(frm.y - to.y)  # Manhattan\n", "        m.add_edge(frm, to, distance=distance)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Let's inspect the resulting data instance."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:10.354344Z", "iopub.status.busy": "2025-05-15T14:42:10.354065Z", "iopub.status.idle": "2025-05-15T14:42:10.890835Z", "shell.execute_reply": "2025-05-15T14:42:10.890193Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "from pyvrp.plotting import plot_coordinates\n", "\n", "_, ax = plt.subplots(figsize=(8, 8))\n", "plot_coordinates(m.data(), ax=ax)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The instance looks good, so we are ready to solve it.\n", "Let's do so with a second of runtime, and display the search progress using the `display` argument on `Model.solve`."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:10.892896Z", "iopub.status.busy": "2025-05-15T14:42:10.892466Z", "iopub.status.idle": "2025-05-15T14:42:11.898849Z", "shell.execute_reply": "2025-05-15T14:42:11.898261Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyVRP v0.11.0\n", "\n", "Solving an instance with:\n", "    1 depot\n", "    16 clients\n", "    4 vehicles (1 vehicle type)\n", "\n", "                  |       Feasible        |      Infeasible\n", "    Iters    Time |   #      Avg     Best |   #      Avg     Best\n"]}, {"name": "stdout", "output_type": "stream", "text": ["H     500      0s |  45     6230     6208 |  39     6995     5905\n"]}, {"name": "stdout", "output_type": "stream", "text": ["     1000      1s |  46     6219     6208 |  65     6198     5235\n", "\n", "Search terminated in 1.00s after 1183 iterations.\n", "Best-found solution has cost 6208.\n", "\n", "Solution results\n", "================\n", "    # routes: 4\n", "     # trips: 4\n", "   # clients: 16\n", "   objective: 6208\n", "    distance: 6208\n", "    duration: 0\n", "# iterations: 1183\n", "    run-time: 1.00 seconds\n", "\n"]}], "source": ["from pyvrp.stop import MaxRuntime\n", "\n", "res = m.solve(stop=MaxRuntime(1), display=True)  # one second"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By passing the `display` argument, PyVRP displays statistics about the solver progress and the instance being solved.\n", "In particular, it outputs the sizes of the feasible and infeasible solution pools, their average objective values, and the objective of the best solutions in either pool.\n", "A heuristic improvement is indicated by a `H` at the start of a line.\n", "\n", "Let's print the solution we have found to see the routes."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:11.901036Z", "iopub.status.busy": "2025-05-15T14:42:11.900644Z", "iopub.status.idle": "2025-05-15T14:42:11.903862Z", "shell.execute_reply": "2025-05-15T14:42:11.903398Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solution results\n", "================\n", "    # routes: 4\n", "     # trips: 4\n", "   # clients: 16\n", "   objective: 6208\n", "    distance: 6208\n", "    duration: 0\n", "# iterations: 1183\n", "    run-time: 1.00 seconds\n", "\n", "Routes\n", "------\n", "Route #1: 7 1 4 3\n", "Route #2: 14 16 10 9\n", "Route #3: 5 6 2 8\n", "Route #4: 13 15 11 12\n", "\n"]}], "source": ["print(res)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Good! Our solution attains the same objective value as the optimal solution OR<PERSON><PERSON><PERSON> finds.\n", "Let's inspect our solution more closely."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:11.905510Z", "iopub.status.busy": "2025-05-15T14:42:11.905329Z", "iopub.status.idle": "2025-05-15T14:42:12.111942Z", "shell.execute_reply": "2025-05-15T14:42:12.111300Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pyvrp.plotting import plot_solution\n", "\n", "_, ax = plt.subplots(figsize=(8, 8))\n", "plot_solution(res.best, m.data(), ax=ax)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We have just solved our first vehicle routing problem using PyVRP!"]}, {"cell_type": "markdown", "metadata": {}, "source": [".. warning::\n", "   PyVRP automatically converts all numeric input values to integers.\n", "   If your data has decimal values, you must scale and convert them to integers first to avoid unexpected behaviour.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### VRP with time windows\n", "\n", "\n", "Besides the capacitated VRP, PyVRP also supports the VRP with time windows.\n", "Let's see if we can also solve such an instance, again following the [OR-Tools documentation](https://developers.google.com/optimization/routing/vrptw).\n", "Like in the OR-Tools example, we will ignore capacity restrictions, and give each vehicle a maximum route duration of 30.\n", "Unlike the OR-Tools example, we still aim to minimise the total travel _distance_, not _duration_."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:12.114292Z", "iopub.status.busy": "2025-05-15T14:42:12.113868Z", "iopub.status.idle": "2025-05-15T14:42:12.122363Z", "shell.execute_reply": "2025-05-15T14:42:12.121880Z"}}, "outputs": [], "source": ["# fmt: off\n", "DURATION_MATRIX = [\n", "        [0, 6, 9, 8, 7, 3, 6, 2, 3, 2, 6, 6, 4, 4, 5, 9, 7],\n", "        [6, 0, 8, 3, 2, 6, 8, 4, 8, 8, 13, 7, 5, 8, 12, 10, 14],\n", "        [9, 8, 0, 11, 10, 6, 3, 9, 5, 8, 4, 15, 14, 13, 9, 18, 9],\n", "        [8, 3, 11, 0, 1, 7, 10, 6, 10, 10, 14, 6, 7, 9, 14, 6, 16],\n", "        [7, 2, 10, 1, 0, 6, 9, 4, 8, 9, 13, 4, 6, 8, 12, 8, 14],\n", "        [3, 6, 6, 7, 6, 0, 2, 3, 2, 2, 7, 9, 7, 7, 6, 12, 8],\n", "        [6, 8, 3, 10, 9, 2, 0, 6, 2, 5, 4, 12, 10, 10, 6, 15, 5],\n", "        [2, 4, 9, 6, 4, 3, 6, 0, 4, 4, 8, 5, 4, 3, 7, 8, 10],\n", "        [3, 8, 5, 10, 8, 2, 2, 4, 0, 3, 4, 9, 8, 7, 3, 13, 6],\n", "        [2, 8, 8, 10, 9, 2, 5, 4, 3, 0, 4, 6, 5, 4, 3, 9, 5],\n", "        [6, 13, 4, 14, 13, 7, 4, 8, 4, 4, 0, 10, 9, 8, 4, 13, 4],\n", "        [6, 7, 15, 6, 4, 9, 12, 5, 9, 6, 10, 0, 1, 3, 7, 3, 10],\n", "        [4, 5, 14, 7, 6, 7, 10, 4, 8, 5, 9, 1, 0, 2, 6, 4, 8],\n", "        [4, 8, 13, 9, 8, 7, 10, 3, 7, 4, 8, 3, 2, 0, 4, 5, 6],\n", "        [5, 12, 9, 14, 12, 6, 6, 7, 3, 3, 4, 7, 6, 4, 0, 9, 2],\n", "        [9, 10, 18, 6, 8, 12, 15, 8, 13, 9, 13, 3, 4, 5, 9, 0, 9],\n", "        [7, 14, 9, 16, 14, 8, 5, 10, 6, 5, 4, 10, 8, 6, 2, 9, 0],\n", "]\n", "TIME_WINDOWS = [\n", "        (0, 999),  # location 0 - the depot (modified to be unrestricted)\n", "        (7, 12),   # location 1\n", "        (10, 15),  # location 2\n", "        (16, 18),  # location 3\n", "        (10, 13),  # location 4\n", "        (0, 5),    # location 5\n", "        (5, 10),   # location 6\n", "        (0, 4),    # location 7\n", "        (5, 10),   # location 8\n", "        (0, 3),    # location 9\n", "        (10, 16),  # location 10\n", "        (10, 15),  # location 11\n", "        (0, 5),    # location 12\n", "        (5, 10),   # location 13\n", "        (7, 8),    # location 14\n", "        (10, 15),  # location 15\n", "        (11, 15),  # location 16\n", "]\n", "# fmt: on"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We now need to specify the time windows for all locations, and the duration of travelling along each edge.\n", "The depot's time window is also applied to the vehicle type, to indicate shift time windows."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:12.124294Z", "iopub.status.busy": "2025-05-15T14:42:12.123904Z", "iopub.status.idle": "2025-05-15T14:42:12.129732Z", "shell.execute_reply": "2025-05-15T14:42:12.129273Z"}}, "outputs": [], "source": ["m = Model()\n", "m.add_vehicle_type(\n", "    4,\n", "    max_duration=30,\n", "    tw_early=TIME_WINDOWS[0][0],\n", "    tw_late=TIME_WINDOWS[0][1],\n", ")\n", "\n", "depot = m.add_depot(\n", "    x=COORDS[0][0],\n", "    y=COORDS[0][1],\n", "    tw_early=TIME_WINDOWS[0][0],\n", "    tw_late=TIME_WINDOWS[0][1],\n", ")\n", "clients = [\n", "    m.add_client(\n", "        x=COORDS[idx][0],\n", "        y=COORDS[idx][1],\n", "        tw_early=TIME_WINDOWS[idx][0],\n", "        tw_late=TIME_WINDOWS[idx][1],\n", "    )\n", "    for idx in range(1, len(COORDS))\n", "]\n", "\n", "for frm_idx, frm in enumerate(m.locations):\n", "    for to_idx, to in enumerate(m.locations):\n", "        distance = abs(frm.x - to.x) + abs(frm.y - to.y)  # Manhattan\n", "        duration = DURATION_MATRIX[frm_idx][to_idx]\n", "        m.add_edge(frm, to, distance=distance, duration=duration)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:12.131400Z", "iopub.status.busy": "2025-05-15T14:42:12.131190Z", "iopub.status.idle": "2025-05-15T14:42:13.136884Z", "shell.execute_reply": "2025-05-15T14:42:13.136260Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solution results\n", "================\n", "    # routes: 4\n", "     # trips: 4\n", "   # clients: 16\n", "   objective: 6528\n", "    distance: 6528\n", "    duration: 79\n", "# iterations: 882\n", "    run-time: 1.00 seconds\n", "\n", "Routes\n", "------\n", "Route #1: 7 1 4 3\n", "Route #2: 12 13 15 11\n", "Route #3: 5 8 6 2 10\n", "Route #4: 9 14 16\n", "\n"]}], "source": ["res = m.solve(stop=MaxRuntime(1), display=False)  # one second\n", "print(res)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Due to the hard time windows requirements, the total travel distance has increased slightly compared to our solution for the capacitated VRP.\n", "Let's have a look at the new solution."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:13.138783Z", "iopub.status.busy": "2025-05-15T14:42:13.138575Z", "iopub.status.idle": "2025-05-15T14:42:13.341687Z", "shell.execute_reply": "2025-05-15T14:42:13.341168Z"}}, "outputs": [{"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["_, ax = plt.subplots(figsize=(8, 8))\n", "plot_solution(res.best, m.data(), ax=ax)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Multi-depot VRP with time windows\n", "\n", "Let's now solve a VRP with multiple depots and time windows.\n", "We consider two depots, and two vehicles per depot that have to start and end their routes at their respective depot.\n", "PyVRP additionally supports vehicles ending their routes at a different depot from where they start, by passing different depots to the `start_depot` and `end_depot` arguments of the `VehicleType`.\n", "\n", "We will re-use some of the data from the VRPTW case, but change the time window data slightly: the first client now becomes the second depot.\n", "Note that in the case of multiple depots, the distinction between vehicle shifts and depot opening times becomes particularly important."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:13.343665Z", "iopub.status.busy": "2025-05-15T14:42:13.343274Z", "iopub.status.idle": "2025-05-15T14:42:13.347162Z", "shell.execute_reply": "2025-05-15T14:42:13.346676Z"}}, "outputs": [], "source": ["# fmt: off\n", "TIME_WINDOWS = [\n", "    (0, 999),  # location 0 - a depot (modified to be unrestricted)\n", "    (0, 999),  # location 1 - a depot (modified to be unrestricted)\n", "    (10, 15),  # location 2\n", "    (16, 18),  # location 3\n", "    (10, 13),  # location 4\n", "    (0, 5),    # location 5\n", "    (5, 10),   # location 6\n", "    (0, 4),    # location 7\n", "    (5, 10),   # location 8\n", "    (0, 3),    # location 9\n", "    (10, 16),  # location 10\n", "    (10, 15),  # location 11\n", "    (0, 5),    # location 12\n", "    (5, 10),   # location 13\n", "    (7, 8),    # location 14\n", "    (10, 15),  # location 15\n", "    (11, 15),  # location 16\n", "]\n", "# fmt: on"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:13.348742Z", "iopub.status.busy": "2025-05-15T14:42:13.348553Z", "iopub.status.idle": "2025-05-15T14:42:13.354660Z", "shell.execute_reply": "2025-05-15T14:42:13.354130Z"}}, "outputs": [], "source": ["m = Model()\n", "\n", "for idx in range(2):\n", "    depot = m.add_depot(x=COORDS[idx][0], y=COORDS[idx][1])\n", "\n", "    # Two vehicles at each depot, with 30 maximum route duration.\n", "    m.add_vehicle_type(\n", "        2,\n", "        start_depot=depot,\n", "        end_depot=depot,\n", "        max_duration=30,\n", "        tw_early=TIME_WINDOWS[idx][0],\n", "        tw_late=TIME_WINDOWS[idx][1],\n", "    )\n", "\n", "for idx in range(2, len(COORDS)):\n", "    m.add_client(\n", "        x=COORDS[idx][0],\n", "        y=COORDS[idx][1],\n", "        tw_early=TIME_WINDOWS[idx][0],\n", "        tw_late=TIME_WINDOWS[idx][1],\n", "    )\n", "\n", "for frm_idx, frm in enumerate(m.locations):\n", "    for to_idx, to in enumerate(m.locations):\n", "        distance = abs(frm.x - to.x) + abs(frm.y - to.y)  # Manhattan\n", "        duration = DURATION_MATRIX[frm_idx][to_idx]\n", "        m.add_edge(frm, to, distance=distance, duration=duration)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's have a look at the modified data instance to familiarise ourself with the changes."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:13.356493Z", "iopub.status.busy": "2025-05-15T14:42:13.356054Z", "iopub.status.idle": "2025-05-15T14:42:13.503413Z", "shell.execute_reply": "2025-05-15T14:42:13.502770Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["_, ax = plt.subplots(figsize=(8, 8))\n", "plot_coordinates(m.data(), ax=ax)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's solve the instance."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:13.505494Z", "iopub.status.busy": "2025-05-15T14:42:13.505035Z", "iopub.status.idle": "2025-05-15T14:42:14.510740Z", "shell.execute_reply": "2025-05-15T14:42:14.510150Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solution results\n", "================\n", "    # routes: 4\n", "     # trips: 4\n", "   # clients: 15\n", "   objective: 6004\n", "    distance: 6004\n", "    duration: 69\n", "# iterations: 1002\n", "    run-time: 1.00 seconds\n", "\n", "Routes\n", "------\n", "Route #1: 9 14 16\n", "Route #2: 7 5 8 6 2 10\n", "Route #3: 12 13 15 11\n", "Route #4: 4 3\n", "\n"]}], "source": ["res = m.solve(stop=MaxRuntime(1), display=False)  # one second\n", "print(res)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:14.512845Z", "iopub.status.busy": "2025-05-15T14:42:14.512449Z", "iopub.status.idle": "2025-05-15T14:42:14.715811Z", "shell.execute_reply": "2025-05-15T14:42:14.715248Z"}}, "outputs": [{"data": {"image/png": "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*************************************************/5X1QuNzcetyUAqlIFxiGYXYJIiLiqlLOhvEzfmkFdSG01JtdkdtRIBXZi+joaMrKyswuQ0REXJXFAic+CqEJULUZFt9gdkVuR4FUZC8USEVEZK/8w+C055ytoLJfhVXvml2RW1EgFdkLq9WqXfYiIrJ3Qw+GQ+Y4v1/4d6jRan9dpUAq0gWGYSiUiojI3h1xIwxKh2a1guoOBVKRLggLC6OmpsbsMkRExNXZvOH0/4B3IBR8B0ufMLsit+BldgGuzjAMjKYmAByNjSZX47os/v79ukVSXFwca9euJSwszOxSRETE1Q0YDic8CO9fAV/8CxIPh7h0s6tyaQqke2E0NVF21NEUxscTUlNDSF2d2SW5pNFZy7EEBJhdRq/x8vLCbtduFxER6aLUWbD+E8h/H965CP72LfgEml2Vy9Iu+y6KKyykzcebLUOHUjBkCI3+/maXJCIiIq7KYoETH4OQOKjcCEtuNLsil6YZ0r2w+PsT/flnAIwJDwfA4XBQsG0btXV1eHl5MWzIEPw9PKBa/P159dVX8ff3JywsjKioKOLj4wkLC8Nq7R9/9/j7+9PQ0EBgoP7CFRGRLgiIgNOehXknQ9Z/YcQxMO5ks6tySQqke2GxWLD8Ejatv+yStgLDx44FoL29nU2bNtHU1ISPjw/Dhw/Hx8fHrHJNNWHCBD744IOOs9FtNhtBQUGkpqZyxBFHmFtcD0hISKCgoIDRo0ebXYqIiLiLxMPg4L/D94/BwqshfhKEDDK7KpfTP6auTOTl5cWoUaNISUkhMTGRDRs2kJOTw4YNGzzumMOUlBSGDh3a8bPdbicwMJApU6aYV1QP8vPzo6WlxewyRETE3Uy9GQamQlMVvHspOBxmV+RyFEh7kJ+fH+PGjSMlJYXY2Fjy8/PJycmhoKDAI3pYWiwWDj74YLy9vQHw8fEhIyPDY2eMRUREAPDy+aUVVABs/gZ++LfZFbkcBdJeEhQURFJSEikpKYSEhJCbm0t2djYlJSX9OpwOHz6cYcOGAXDAAQcQFhZGXl4ejn7y16C3tzetra1mlyEiIu4mciQcd7/z+8/vguJsU8txNQqkfSA8PJyUlBRSUlKwWCzk5uaSk5NDZWWl2aX1iiOOOIIhQ4YwdepUhg4dyvDhw1m2bFm/aCwfHx9PYaGWghMRkX2Q/hcYcyI42pytoFrV33wnBdI+ZLFYiImJISUlheTkZOrr68nJySEvL4+6ftTfNDY2lvPOO6/j7PqAgAAyMjIoKSlh3bp1Jle3f4KDg6mvrze7DBERcUcWC5z8JAQPhIr18PE/za7IZSiQmsRisTB48GBSUlIYN24cZWVlZGdns2rVKpqbm80ub7/9ftUmi8XC6NGjiYqKIjMzk6ZfVr8SERHxKDtbQQEsfxnWfGhuPS5CbZ9cgM1mY/jw4QC0tbWxceNGWlpa8PPzY9iwYR0nCfUH4eHhTJw4kZUrVxIWFsbgwYPNLqnbrFYrdrsdm81mdikiIuKOhh0BU66CpU/C+1dC3EQIjjW7KlNphtTFeHt7M2bMGFJSUhg8eDDr1q0jOzubjRs39psTg6xWK8nJyXh7e5OVlUV7e7vZJXXLwIED2b59u9lliIiIOzvyVohNhqZKePdvHt8KSoHUhfn7+zN+/HhSU1OJiopi5cqV5OTksG3btn5xpv7AgQNJSkoiJyeHsrIys8vpsoiIiH57QpqIiPQRL19nKygvf9j0Jfz0jNkVmUqB1E2EhISQnJxMSkoKAQEBHW2k3CnI7Y63tzcTJ06koaGBvLw8twjavz8+VkREZJ9EjYZp9zi//+wO2J5rajlmUiB1QwMGDOhoI9Xe3k52djY5OTlUV1ebXdo+S0xMZNiwYWRmZlJbW2t2OV3iDuFZRERc3KS/wugTwN7q0a2gFEjdmMViYdCgQaSmppKUlER1dTXZ2dnk5eXR0NBgdnndFhgYSEZGBkVFRaxfv97scv5QZGQk5eXlZpchIiLubmcrqKAYKF8Ln95mdkWmUCDtJ6xWK0OHDiU1NZWxY8dSXFxMTk4O+fn5brX+usViYezYsQwYMIDMzEyXbYEVGxtLSUmJ2WWIiEh/EBgJp/5yDGnmC7B2ibn1mEBtn/ohLy8vRo4cCUBra2tHG6mAgACGDRuGl5frD3tERETHsqMREREkJCSYXVInVqtVu+xFRKTnjDgKDroCfnwK3r8CLlsKwTFmV9VnNEPaz/n4+DB27FhSU1OJj49nzZo15OTksHnzZpdvI2W1WklJScFms7FixQrsdrvZJYmIiPSeo26DmAnQWA7vX+5RraAUSD1IQEAAEyZMICUlhYiICFauXEl2djZFRUUuPds3aNAgxo8fz4oVK9ixY4fZ5XQICQmhpqbG7DJERKS/8Pb7pRWUH2z4DH5+3uyK+owCqYcKDQ0lOTmZ1NRUfHx8yM3NJScnx2VP1PHx8WHSpEnU1dWxatUqlwjQcXFxFBUVmV2GiIj0J9Fj4dh/Ob//9DYoXWVuPX1EgVSIiooiJSWF5ORkmpubyc7OJjc31yXbLw0bNowhQ4aQmZlJXV2dqbV4e3u73SpTIiLiBjIugpHTwN7ibAXV1mR2Rb1OgVQ6WCwW4uPjSU1NZcKECZSXl5OTk8PKlStpbHSdvmhBQUFkZGSwbds2NmzYYHY5IiIiPctigVOegsAoKMt3Ns3v5xRIZbesVivDhg0jJSWFMWPGsG3bNrKzs1m9ejWtra1ml4fFYmHcuHGEhYWRmZlpWmsrPz8/mpr6/1+uIiLSx4Kifm0F9dOzsP5Tc+vpZQqksldeXl6MHj2a1NRUEhMT2bBhAzk5OWzYsMH0M98jIyNJT09n9erVFBYW9vnjJyQksG3btj5/XBER8QAjj4ED/+b8/r3LoN51TuztaQqk0i1+fn6MGzeOlJQUYmNjyc/PJzs7m4KCAtNONLLZbKSmpgL0eXsof39/l23eLyIi/cDRd0L0OGjY4exP6gIn9fYGBVLZZ0FBQSQlJZGamkpISAi5ublkZ2dTUlJiSjiNj49n3LhxrFixgoqKij5/fBERkR63sxWUzRfWfwyZ/zG7ol6hQCo9Ijw8nJSUFFJSUrBYLOTk5JCTk0NlZWWf1uHr68ukSZOoqqoiPz+/T4Kxl5cXbW1tvf44IiLioWLGwzF3Ob//5BYoW/3/7d15cJR1nsfxT3cn3UkgnZibI5FA0HAkHYTIoNRO7ZoSHWpFnZ2acdFBxkFF2AGxnJGx1K21EKpmtEpdRnfcFd0SZcZz1OUYNuDBypGE6YZwn0IFkggYOwlnun/7R6QlEiD3L8f7VZWy85zfx28CH57j99itpxMQSNGhHA6H0tPTVVBQoPz8fNXV1SkQCGjr1q1dOkxTTk6OMjMzVVJSorq6uk7dF+ORAgA63fgHpJwiqeH0t0NB9a7bxQik6DQOh0NZWVny+XwaOXKkqqur5ff7tW3bti657zI+Pl6FhYX68ssvtW/fvk7bT0JCQrccsxUA0Is4HNKUP0hxKVJVuVT8b7Yr6lAEUnQJl8ulYcOGqaCgQNdcc40OHjyoQCCgXbt2derlbofDoVGjRsnr9aq0tLRbDFkFAECbxKc3jk8qSRsWS3uL7dbTgQik6HLR0dHKzc2Vz+dTVlaWdu/eLb/fr3379ikcDnfKPlNTUzVmzBht27ZNR44c6fDtOxyOTqsdAICIa2+RCmc0fv5gplTfPV/53VoEUlgVGxurUaNGqaCgQKmpqSovL1cgENDhw4c7/IEkl8ulMWPGKBQKye/3d2iAzMjIUGVlZYdtDwCAS7r5aSnlWqmuSvrwX3rFUFAEUnQbXq9X+fn58vl8iouLiwwjVV1d3aH7yczMVG5ursrKyjpsFICUlBQdO9Y7/pUKAOjmomOlf/ovyeWWdi2XSl+1XVG7EUjRLSUnJ0eGkQqFQpFhpGpqajpk+zExMSosLNTx48e1Y8eOdp+NdTgcTb4PffONnH94SaFvvmnXdgEAaFZGnlT0r42fVz0ufbXLajntRSBFt+ZwODRgwAD5fD7l5eWppqZGfr9fW7duVX19fbu3P3z4cA0aNEglJSUdsr3zwTa4YoWca9cquGJlu7cJAECzxs+Uhv2D1HBKevc+qeGM7YrajECKHsPpdGrIkCEqKCjQyJEjdeTIEQUCAW3fvl1nzrT9l9Dr9aqwsFD79+/XgQMH2ryd5OTkyBuizgfR4EoCKQCgkzid0u0vSbFJUuVWac3TtitqMwIpeiSXy6Xhw4fL5/MpJydH+/fvl9/v1+7du9XQ0NDq7TkcDuXl5alfv34qLy9v01BU5x9savj6a50sKZEkndy0SaEOus0AAICLxGdIU/698fMXL0r71tqtp40IpOjx3G63RowYoYKCAg0ePFg7d+5UIBDQgQMHWv0kfVpamnJzc7V7924dPXq0Veu6XC6Fw2HVrVkrnd9vOKzaNT3zDwcAQA+RO1kaO73x8wczpZNd+9rujkAgRa8SFxen0aNHy+fzKSkpSeXl5fL7/aqoqGjxg0tRUVEaNWqUzp07p0Ag0OpQG1y5svEyiiS5XAquXNHawwAAoHUmPSMlD5dqj/bIoaAIpOi1EhISlJ+fr4KCArndbm3ZskWBQKDFwzNlZWXpmmuuUWlpqb7++usWrdPP4VD9F198d4Y0FFL9F+sVqqtr62EAAHBl7rjGoaCc0dLOj6XNr9uuqFUIpOgTUlNT5fP5lJ+fr9OnT8vv92vLli1XfAd9bGysCgsLVV1drZ07d17xLGvi3r1SKNR0YkOD6tZ+0s4jAADgCgb4pJuebPy8cr50bI/delqBQIo+xeFwaPDgwSooKNDo0aN1/PhxBQIBlZeX6+TJk5dc59prr1VGRoZKSkouuZwknfzfYhnn936tXC4FV63qyMMAAKB5E2ZL2X8nnTspvftLqeGs7YpahECKPsvpdCo7O1s+n0+5ubk6fPiw/H6/9u3b1+xT9omJiRo3bpz27t2rgwcPXjQ/XF+v+s8+k+P795yGQqr/7DOFLxNkAQDoEE6ndMd/SLFXSUf90toFtitqEQIpoMYHma699trIk/pffvmlAoGA9u7dq9AFl+CdTqfy8/MVExOjsrKyJsG17vPPZS4xXJQ5e1Z1n33e6ccBAIC8A6XbXmz8/H/PSwc+s1tPC0TZLgDobjwej3JycpSUlKS9d9yhXTt2Srr49aBxkvYY8910YySX6+J7SCXJ5VLF3Lmq+N42LiVm5Ehlv/tOew4DANCXjfhH6bqfS5v/W3rvAemut2xXdFnWzpAuXrxYQ4YMUUxMjMaPH69NmzbZKgW4pNR775XT7ZZDagyc3/tqMl1qPoxeOL2ZbVy0zehoJf38ns4/OABA73bLIik5R6o9Iv3PI7aruSwrgfRPf/qT5s2bp6eeekqbN2+Wz+fTpEmTVF1dbaMc4JISpkxR9vvvyZ2TI7Xw7GabORxyD89R9vvvKWHKlM7dFwCg93P3k+58RXJGSRWltqu5LCuB9LnnntOMGTM0ffp0jRw5Ui+//LLi4uL06quv2igHuCzPsGHKfvcdXTX1nxsndHQw/XZ7V909VdnvvCPPsGEdu30AQN816Drp7x+3XcUVdfk9pGfPnlVZWZnmz58fmeZ0OlVUVKT169c3u86ZM2d05syZyPfnx448ceJEm95b3lo1vIu8T7lUv90PPaTE/Hx9829Py5w6denL863hcskRG6uEJ5+Qe+JE1dTXS/X17d8uWoTf7b6Ffvct9PsCI+5R/M5Viq7YIGOMvj7RNa8WvdJY3xfq8jOkx44dUygUUnp6epPp6enpqqysbHadhQsXKiEhIfKVmZnZFaUCF/FMnKjkpW8oOi+vQ7YXnZen5KVvyDNxYodsDwCAizhdqr/5WYW8g3Vu0Hjb1TSrRzxlP3/+fM2bNy/yfTAYVGZmppKSkuT1erusjqSkpC7bF+y7ZL+TkpSy9A0df+U/9dULLzROa8377r8dOD91zhwl//I+OVyudlaK9uJ3u2+h330L/f5WUpI0JyCX06Wkzn4m4ltRUS2PmV0eSFNSUuRyuVRVVdVkelVVlTIyMppdx+PxyOPxdEV5QIs4XC6lPPiA4grH6cupd7du5XBYV7+5VHHXXdc5xQEA0BxX9z0P2eWX7N1ut8aOHavi4uLItHA4rOLiYk2YMKGrywHaxZxr2z3MbV0PAIDeyMpT9vPmzdMrr7yi119/XTt27NDMmTNVX1+v6dOn2ygHaLPav/61cTD81nC5GtcDAACSLN1D+tOf/lRfffWVnnzySVVWVqqgoEArV6686EEnoDsz4bCCK1e0/mn7UEjBFSuU/vhv5XDy9l4AAKzdTDB79mzNnj3b1u6Bdjvl9yt04uvmZzocjW9eOv/f7wmdOKFT/oDirhvTyVUCAND9cXoGaKPaVZe4XO9yyRkfr9SHH5YzPv6Sy3DZHgCARgRSoA2MMQquaP5yfdzYsRr68UdKeeB+Df3oI8WNbeZp+lBIwRXLZZo5ewoAQF9DIAXa4HT5NjVUV383weWSnE6lzpunrNeWKDotTZIUnZ6mrCVLlDpvXuP4oxfcM9pQVa3T27Z3dekAAHQ7BFKgDWr/uuq7S/FOp6LSUjXkrTeVcv+Mix5UcrhcSrl/hoa8uVRRaWnfhVKXS7WrVnVx5QAAdD8EUqCVjDEKLv/ucr33lls09MMPFevzXXa92IICDf3oQ8VPmtQ4IRRScDmX7QEAIJACrXRm9x6dq6iQw+PRgGee0cBnfy9XfHyL1nXFx2vQc89qwDPPyOF261xFhc7s2dPJFQMA0L1133dIAd2Us18/eSdPVsqsWfIMzW71+g6HQ4l33qHYggIdW7xYzrh+nVAlAAA9B4EUaCX34EEa9Ozv270dz9DsDtkOAAA9HZfsAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYEUAAAAVhFIAQAAYBWBFAAAAFYRSAEAAGAVgRQAAABWRdkuoC2MMZKkYDDYJfs7v5+oqB75vwutRL/7Dnrdt9DvvoV+23e+B+dz2+X0yC7V1tZKkjIzMy1XAgAAgMupra1VQkLCZZdxmJbE1m4mHA7ryJEjio+Pl8Ph6PT9BYNBZWZm6vDhw/J6vZ2+P9hFv/sOet230O++hX7bZ4xRbW2tBg4cKKfz8neJ9sgzpE6nU4MHD+7y/Xq9Xn6o+xD63XfQ676Ffvct9NuuK50ZPY+HmgAAAGAVgRQAAABWEUhbwOPx6KmnnpLH47FdCroA/e476HXfQr/7Fvrds/TIh5oAAADQe3CGFAAAAFYRSAEAAGAVgRQAAABWEUgBAABgFYG0BRYvXqwhQ4YoJiZG48eP16ZNm2yXhFZauHChCgsLFR8fr7S0NN1+++3atWtXk2VOnz6tWbNmKTk5Wf3799ePf/xjVVVVNVnm0KFDmjx5suLi4pSWlqZHH31UDQ0NXXkoaKVFixbJ4XBo7ty5kWn0unepqKjQ3XffreTkZMXGxiovL0+lpaWR+cYYPfnkkxowYIBiY2NVVFSkPXv2NNnGiRMnNHXqVHm9XiUmJuq+++5TXV1dVx8KriAUCumJJ55Qdna2YmNjNWzYMD399NNN3pVOv3sog8tatmyZcbvd5tVXXzXbtm0zM2bMMImJiaaqqsp2aWiFSZMmmSVLlpjy8nLj9/vNj370I5OVlWXq6uoiyzz44IMmMzPTFBcXm9LSUvODH/zA3HDDDZH5DQ0NZvTo0aaoqMj87W9/M8uXLzcpKSlm/vz5Ng4JLbBp0yYzZMgQk5+fb+bMmROZTq97jxMnTpirr77a3HvvvWbjxo1m//79ZtWqVWbv3r2RZRYtWmQSEhLMBx98YAKBgLnttttMdna2OXXqVGSZW265xfh8PrNhwwbz+eefm5ycHHPXXXfZOCRcxoIFC0xycrL5+OOPzYEDB8zbb79t+vfvb55//vnIMvS7ZyKQXsH1119vZs2aFfk+FAqZgQMHmoULF1qsCu1VXV1tJJlPP/3UGGNMTU2NiY6ONm+//XZkmR07dhhJZv369cYYY5YvX26cTqeprKyMLPPSSy8Zr9drzpw507UHgCuqra01w4cPN6tXrzY//OEPI4GUXvcuv/nNb8zEiRMvOT8cDpuMjAzzu9/9LjKtpqbGeDwe89ZbbxljjNm+fbuRZEpKSiLLrFixwjgcDlNRUdF5xaPVJk+ebH7xi180mXbnnXeaqVOnGmPod0/GJfvLOHv2rMrKylRUVBSZ5nQ6VVRUpPXr11usDO31zTffSJKSkpIkSWVlZTp37lyTXufm5iorKyvS6/Xr1ysvL0/p6emRZSZNmqRgMKht27Z1YfVoiVmzZmny5MlNeirR697mww8/1Lhx4/STn/xEaWlpGjNmjF555ZXI/AMHDqiysrJJvxMSEjR+/Pgm/U5MTNS4ceMiyxQVFcnpdGrjxo1ddzC4ohtuuEHFxcXavXu3JCkQCGjdunW69dZbJdHvnizKdgHd2bFjxxQKhZr8pSRJ6enp2rlzp6Wq0F7hcFhz587VjTfeqNGjR0uSKisr5Xa7lZiY2GTZ9PR0VVZWRpZp7mfh/Dx0H8uWLdPmzZtVUlJy0Tx63bvs379fL730kubNm6ff/va3Kikp0a9+9Su53W5NmzYt0q/m+nlhv9PS0prMj4qKUlJSEv3uZh577DEFg0Hl5ubK5XIpFAppwYIFmjp1qiTR7x6MQIo+Z9asWSovL9e6detsl4JOcPjwYc2ZM0erV69WTEyM7XLQycLhsMaNG6dnnnlGkjRmzBiVl5fr5Zdf1rRp0yxXh4725z//WUuXLtWbb76pUaNGye/3a+7cuRo4cCD97uG4ZH8ZKSkpcrlcFz19W1VVpYyMDEtVoT1mz56tjz/+WGvXrtXgwYMj0zMyMnT27FnV1NQ0Wf7CXmdkZDT7s3B+HrqHsrIyVVdX67rrrlNUVJSioqL06aef6oUXXlBUVJTS09PpdS8yYMAAjRw5ssm0ESNG6NChQ5K+69fl/hzPyMhQdXV1k/kNDQ06ceIE/e5mHn30UT322GP62c9+pry8PN1zzz16+OGHtXDhQkn0uycjkF6G2+3W2LFjVVxcHJkWDodVXFysCRMmWKwMrWWM0ezZs/X+++9rzZo1ys7ObjJ/7Nixio6ObtLrXbt26dChQ5FeT5gwQVu3bm3yB9nq1avl9Xov+gsR9tx0003aunWr/H5/5GvcuHGaOnVq5DO97j1uvPHGi4Zw2717t66++mpJUnZ2tjIyMpr0OxgMauPGjU36XVNTo7Kyssgya9asUTgc1vjx47vgKNBSJ0+elNPZNLq4XC6Fw2FJ9LtHs/1UVXe3bNky4/F4zGuvvWa2b99u7r//fpOYmNjk6Vt0fzNnzjQJCQnmk08+MUePHo18nTx5MrLMgw8+aLKyssyaNWtMaWmpmTBhgpkwYUJk/vmhgG6++Wbj9/vNypUrTWpqKkMB9QAXPmVvDL3uTTZt2mSioqLMggULzJ49e8zSpUtNXFyceeONNyLLLFq0yCQmJpq//OUvZsuWLWbKlCnNDgM0ZswYs3HjRrNu3TozfPhwhgHqhqZNm2YGDRoUGfbpvffeMykpKebXv/51ZBn63TMRSFvgxRdfNFlZWcbtdpvrr7/ebNiwwXZJaCVJzX4tWbIkssypU6fMQw89ZK666ioTFxdn7rjjDnP06NEm2zl48KC59dZbTWxsrElJSTGPPPKIOXfuXBcfDVrr+4GUXvcuH330kRk9erTxeDwmNzfX/PGPf2wyPxwOmyeeeMKkp6cbj8djbrrpJrNr164myxw/ftzcddddpn///sbr9Zrp06eb2trarjwMtEAwGDRz5swxWVlZJiYmxgwdOtQ8/vjjTYZjo989k8OYC15vAAAAAHQx7iEFAACAVQRSAAAAWEUgBQAAgFUEUgAAAFhFIAUAAIBVBFIAAABYRSAFAACAVQRSAAAAWEUgBQAAgFUEUgAAAFhFIAUAAIBVBFIAAABY9f83T43E2JiZ6wAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["_, ax = plt.subplots(figsize=(8, 8))\n", "plot_solution(res.best, m.data(), ax=ax)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Prize-collecting VRP\n", "\n", "We now have a basic familiarity with PyVRP's `Model` interface, but have not seen some of its additional features yet.\n", "In this short section we will discuss _optional_ clients, which offer a reward (a prize) when they are visited, but are not required for feasibility.\n", "This VRP variant is often called a prize-collecting VRP, and PyVRP supports this out-of-the-box.\n", "\n", "Let's stick to the multiple depot setting, and also define a `PRIZES` list that provides the prizes of visiting each client."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:14.717607Z", "iopub.status.busy": "2025-05-15T14:42:14.717423Z", "iopub.status.idle": "2025-05-15T14:42:14.720837Z", "shell.execute_reply": "2025-05-15T14:42:14.720354Z"}}, "outputs": [], "source": ["# fmt: off\n", "PRIZES = [\n", "    0,    # location 0 - a depot\n", "    0,    # location 1 - a depot\n", "    334,  # location 2\n", "    413,  # location 3\n", "    295,  # location 4\n", "    471,  # location 5\n", "    399,  # location 6\n", "    484,  # location 7\n", "    369,  # location 8\n", "    410,  # location 9\n", "    471,  # location 10\n", "    382,  # location 11\n", "    347,  # location 12\n", "    380,  # location 13\n", "    409,  # location 14\n", "    302,  # location 15\n", "    411,  # location 16\n", "]\n", "# fmt: on"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When modelling optional clients, it is important to provide both a reward (the `prize` argument to `add_client`), and to mark the client as optional by passing `required=False`:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:14.722816Z", "iopub.status.busy": "2025-05-15T14:42:14.722432Z", "iopub.status.idle": "2025-05-15T14:42:14.728361Z", "shell.execute_reply": "2025-05-15T14:42:14.727881Z"}}, "outputs": [], "source": ["m = Model()\n", "\n", "for idx in range(2):\n", "    depot = m.add_depot(x=COORDS[idx][0], y=COORDS[idx][1])\n", "    m.add_vehicle_type(\n", "        2,\n", "        start_depot=depot,\n", "        end_depot=depot,\n", "        tw_early=TIME_WINDOWS[idx][0],\n", "        tw_late=TIME_WINDOWS[idx][1],\n", "    )\n", "\n", "\n", "for idx in range(2, len(COORDS)):\n", "    m.add_client(\n", "        x=COORDS[idx][0],\n", "        y=COORDS[idx][1],\n", "        tw_early=TIME_WINDOWS[idx][0],\n", "        tw_late=TIME_WINDOWS[idx][1],\n", "        prize=PRIZES[idx],\n", "        required=False,\n", "    )\n", "\n", "for frm_idx, frm in enumerate(m.locations):\n", "    for to_idx, to in enumerate(m.locations):\n", "        distance = abs(frm.x - to.x) + abs(frm.y - to.y)  # Manhattan\n", "        duration = DURATION_MATRIX[frm_idx][to_idx]\n", "        m.add_edge(frm, to, distance=distance, duration=duration)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:14.730055Z", "iopub.status.busy": "2025-05-15T14:42:14.729713Z", "iopub.status.idle": "2025-05-15T14:42:15.735854Z", "shell.execute_reply": "2025-05-15T14:42:15.735155Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solution results\n", "================\n", "    # routes: 3\n", "     # trips: 3\n", "   # clients: 10\n", "   objective: 5145\n", "    distance: 3400\n", "    duration: 40\n", "# iterations: 1289\n", "    run-time: 1.00 seconds\n", "\n", "Routes\n", "------\n", "Route #1: 9 14 16 10\n", "Route #2: 7 5 6 8\n", "Route #3: 4 3\n", "\n"]}], "source": ["res = m.solve(stop=MaxRuntime(1), display=False)  # one second\n", "print(res)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:15.738035Z", "iopub.status.busy": "2025-05-15T14:42:15.737459Z", "iopub.status.idle": "2025-05-15T14:42:16.009857Z", "shell.execute_reply": "2025-05-15T14:42:16.009338Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["_, ax = plt.subplots(figsize=(8, 8))\n", "plot_solution(res.best, m.data(), plot_clients=True, ax=ax)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Some clients are not visited in the figure above.\n", "These clients are too far from other locations for their prizes to be worth the additional travel cost of visiting.\n", "Thus, PyVRP's solver opts not to visit such optional clients."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### VRP with simultaneous pickup and delivery\n", "\n", "We will now consider the VRP with simultaneous pickup and delivery.\n", "In this problem variant, clients request items from the depot, and also produce return shipments that needs to be delivered back to the depot after visiting the client.\n", "Thus, there are both deliveries from the depot to the clients, and pickups from the clients to the depot.\n", "\n", "Let's remain in the multi-depot, prize-collecting world we entered through the last example.\n", "We first define a `LOADS` list that tracks the delivery and pickup amount for each location:"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:16.011938Z", "iopub.status.busy": "2025-05-15T14:42:16.011555Z", "iopub.status.idle": "2025-05-15T14:42:16.015638Z", "shell.execute_reply": "2025-05-15T14:42:16.015136Z"}}, "outputs": [], "source": ["# fmt: off\n", "LOADS = [\n", "    (0, 0),   # location 0 - a depot\n", "    (0, 0),   # location 1 - a depot\n", "    (1, 4),   # location 2 - simultaneous pickup and delivery\n", "    (2, 0),   # location 3 - pure delivery\n", "    (0, 5),   # location 4 - pure pickup\n", "    (6, 3),   # location 5 - simultaneous pickup and delivery\n", "    (4, 7),   # location 6 - simultaneous pickup and delivery\n", "    (11, 0),  # location 7 - pure delivery\n", "    (3, 0),   # location 8 - pure delivery\n", "    (0, 5),   # location 9 - pure pickup\n", "    (6, 4),   # location 10 - simultaneous pickup and delivery\n", "    (1, 4),   # location 11 - simultaneous pickup and delivery\n", "    (0, 3),   # location 12 - pure pickup\n", "    (6, 0),   # location 13 - pure delivery\n", "    (3, 2),   # location 14 - simultaneous pickup and delivery\n", "    (4, 3),   # location 15 - simultaneous pickup and delivery\n", "    (0, 6),   # location 16 - pure pickup\n", "]\n", "# fmt: on"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:16.017323Z", "iopub.status.busy": "2025-05-15T14:42:16.017099Z", "iopub.status.idle": "2025-05-15T14:42:16.023087Z", "shell.execute_reply": "2025-05-15T14:42:16.022614Z"}}, "outputs": [], "source": ["m = Model()\n", "\n", "for idx in range(2):\n", "    depot = m.add_depot(x=COORDS[idx][0], y=COORDS[idx][1])\n", "    m.add_vehicle_type(\n", "        2,\n", "        start_depot=depot,\n", "        end_depot=depot,\n", "        capacity=15,\n", "        tw_early=TIME_WINDOWS[idx][0],\n", "        tw_late=TIME_WINDOWS[idx][1],\n", "    )\n", "\n", "\n", "for idx in range(2, len(COORDS)):\n", "    m.add_client(\n", "        x=COORDS[idx][0],\n", "        y=COORDS[idx][1],\n", "        tw_early=TIME_WINDOWS[idx][0],\n", "        tw_late=TIME_WINDOWS[idx][1],\n", "        delivery=LOADS[idx][0],\n", "        pickup=LOADS[idx][1],\n", "        prize=PRIZES[idx],\n", "        required=False,\n", "    )\n", "\n", "for frm_idx, frm in enumerate(m.locations):\n", "    for to_idx, to in enumerate(m.locations):\n", "        distance = abs(frm.x - to.x) + abs(frm.y - to.y)  # Manhattan\n", "        duration = DURATION_MATRIX[frm_idx][to_idx]\n", "        m.add_edge(frm, to, distance=distance, duration=duration)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:16.024947Z", "iopub.status.busy": "2025-05-15T14:42:16.024589Z", "iopub.status.idle": "2025-05-15T14:42:17.030530Z", "shell.execute_reply": "2025-05-15T14:42:17.029870Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solution results\n", "================\n", "    # routes: 3\n", "     # trips: 3\n", "   # clients: 6\n", "   objective: 5375\n", "    distance: 1940\n", "    duration: 21\n", "# iterations: 1271\n", "    run-time: 1.00 seconds\n", "\n", "Routes\n", "------\n", "Route #1: 7\n", "Route #2: 9 5 8\n", "Route #3: 4 3\n", "\n"]}], "source": ["res = m.solve(stop=MaxRuntime(1), display=False)  # one second\n", "print(res)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:17.032494Z", "iopub.status.busy": "2025-05-15T14:42:17.032125Z", "iopub.status.idle": "2025-05-15T14:42:17.308594Z", "shell.execute_reply": "2025-05-15T14:42:17.308040Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["_, ax = plt.subplots(figsize=(8, 8))\n", "plot_solution(res.best, m.data(), plot_clients=True, ax=ax)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### VRP with zone restrictions\n", "\n", "We have seen several VRP variants in this notebook already.\n", "Let us conclude with a variant showing how to model zone restrictions, where some vehicles are not allowed to visit clients located inside a particular area.\n", "Such restrictions commonly apply in urban environments with emission zones, where several types of (heavy) trucks may not enter.\n", "We will add one regular vehicle type to the model that can enter the restricted zone.\n", "Additionally, we will consider a vehicle type that cannot enter the restricted zone, and has to travel from the first to the second depot."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Suppose we have a rectangular zone defined by the following `(x, y)` coordinates."]}, {"cell_type": "code", "execution_count": 24, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:17.310540Z", "iopub.status.busy": "2025-05-15T14:42:17.310345Z", "iopub.status.idle": "2025-05-15T14:42:17.314040Z", "shell.execute_reply": "2025-05-15T14:42:17.313425Z"}}, "outputs": [], "source": ["ZONE = ((500, 125), (850, 275))\n", "\n", "\n", "def in_zone(client) -> bool:\n", "    return (\n", "        ZONE[0][0] <= client.x <= ZONE[1][0]\n", "        and ZONE[0][1] <= client.y <= ZONE[1][1]\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can now set up a `Model` as follows, using routing profiles to restrict which vehicle types can enter the zone to visit clients there."]}, {"cell_type": "code", "execution_count": 25, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:17.316288Z", "iopub.status.busy": "2025-05-15T14:42:17.315787Z", "iopub.status.idle": "2025-05-15T14:42:17.323034Z", "shell.execute_reply": "2025-05-15T14:42:17.322543Z"}}, "outputs": [], "source": ["m = Model()\n", "\n", "depot1 = m.add_depot(x=COORDS[0][0], y=COORDS[0][1])\n", "depot2 = m.add_depot(x=COORDS[1][0], y=COORDS[1][1])\n", "\n", "regular = m.add_profile()\n", "m.add_vehicle_type(\n", "    2,\n", "    start_depot=depot1,\n", "    end_depot=depot1,\n", "    tw_early=TIME_WINDOWS[0][0],\n", "    tw_late=TIME_WINDOWS[0][1],\n", "    profile=regular,\n", ")\n", "\n", "restricted = m.add_profile()\n", "m.add_vehicle_type(\n", "    2,\n", "    start_depot=depot1,\n", "    end_depot=depot2,\n", "    tw_early=TIME_WINDOWS[1][0],\n", "    tw_late=TIME_WINDOWS[1][1],\n", "    profile=restricted,\n", ")\n", "\n", "for idx in range(2, len(COORDS)):\n", "    m.add_client(\n", "        x=COORDS[idx][0],\n", "        y=COORDS[idx][1],\n", "        tw_early=TIME_WINDOWS[idx][0],\n", "        tw_late=TIME_WINDOWS[idx][1],\n", "    )\n", "\n", "for frm_idx, frm in enumerate(m.locations):\n", "    for to_idx, to in enumerate(m.locations):\n", "        distance = abs(frm.x - to.x) + abs(frm.y - to.y)  # Manhattan\n", "        duration = DURATION_MATRIX[frm_idx][to_idx]\n", "\n", "        # Edges without a specific profile assignment are added to all\n", "        # profiles, unless a profile-specific edge overrides them.\n", "        m.add_edge(frm, to, distance=distance, duration=duration)\n", "\n", "        if frm_idx != to_idx and in_zone(to):\n", "            # Here we specify an edge with a high distance and duration\n", "            # for the restricted profile. This ensures vehicles with\n", "            # that profile do not travel over this edge.\n", "            m.add_edge(\n", "                frm,\n", "                to,\n", "                distance=1_000,\n", "                duration=1_000,\n", "                profile=restricted,\n", "            )"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:17.325029Z", "iopub.status.busy": "2025-05-15T14:42:17.324544Z", "iopub.status.idle": "2025-05-15T14:42:18.330876Z", "shell.execute_reply": "2025-05-15T14:42:18.330166Z"}}, "outputs": [], "source": ["res = m.solve(stop=MaxRuntime(1), display=False)  # one second"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:18.333124Z", "iopub.status.busy": "2025-05-15T14:42:18.332907Z", "iopub.status.idle": "2025-05-15T14:42:18.539676Z", "shell.execute_reply": "2025-05-15T14:42:18.539018Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["_, ax = plt.subplots(figsize=(8, 8))\n", "plot_solution(res.best, m.data(), ax=ax)\n", "\n", "# Highlight the restricted zone.\n", "ax.fill_between(\n", "    [ZONE[0][0], ZON<PERSON>[1][0]],\n", "    ZONE[0][1],\n", "    ZONE[1][1],\n", "    color=\"red\",\n", "    alpha=0.15,\n", ");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### VRP with reloading\n", "\n", "Sometimes vehicles can execute multiple trips over a time horizon, by reloading at the depot between trips.\n", "This effectively mitigates the capacity constraint we have so far seen, because vehicles can instead opt to return to the depot to reload if needed.\n", "PyVRP supports a very general form of reloading, with free depot selection.\n", "Optionally, the maximum number of reloads per vehicle type may also be restricted.\n", "See the FAQ for modelling service or loading durations at the depots.\n", "We will solve a small example problem here to showcase some of these features, focusing on reloading, rather than time window support."]}, {"cell_type": "code", "execution_count": 28, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:18.541681Z", "iopub.status.busy": "2025-05-15T14:42:18.541324Z", "iopub.status.idle": "2025-05-15T14:42:18.547076Z", "shell.execute_reply": "2025-05-15T14:42:18.546576Z"}}, "outputs": [], "source": ["m = Model()\n", "\n", "depot1 = m.add_depot(x=COORDS[0][0], y=COORDS[0][1])\n", "depot2 = m.add_depot(x=COORDS[1][0], y=COORDS[1][1])\n", "\n", "m.add_vehicle_type(\n", "    3,\n", "    capacity=15,\n", "    start_depot=depot1,\n", "    end_depot=depot1,\n", "    reload_depots=[depot1, depot2],  # where reloads may take place\n", "    max_reloads=2,  # maximum number of reload depot visits on a route\n", ")\n", "\n", "for idx in range(2, len(COORDS)):\n", "    m.add_client(\n", "        x=COORDS[idx][0],\n", "        y=COORDS[idx][1],\n", "        delivery=DEMANDS[idx],\n", "    )\n", "\n", "for frm in m.locations:\n", "    for to in m.locations:\n", "        distance = abs(frm.x - to.x) + abs(frm.y - to.y)  # Manhattan\n", "        m.add_edge(frm, to, distance=distance)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Returns to a reload depot are marked with a `|` in the result summary, as follows:"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:18.548680Z", "iopub.status.busy": "2025-05-15T14:42:18.548494Z", "iopub.status.idle": "2025-05-15T14:42:19.554460Z", "shell.execute_reply": "2025-05-15T14:42:19.553861Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solution results\n", "================\n", "    # routes: 2\n", "     # trips: 4\n", "   # clients: 15\n", "   objective: 5728\n", "    distance: 5728\n", "    duration: 0\n", "# iterations: 1030\n", "    run-time: 1.00 seconds\n", "\n", "Routes\n", "------\n", "Route #1: 5 8 6 2 | 4 3 7\n", "Route #2: 12 11 15 13 | 10 16 14 9\n", "\n"]}], "source": ["res = m.solve(stop=MaxRuntime(1), display=False)  # one second\n", "print(res)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Each route in the solution consists of two trips.\n", "Let's investigate the first route in more detail:"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:19.556598Z", "iopub.status.busy": "2025-05-15T14:42:19.556206Z", "iopub.status.idle": "2025-05-15T14:42:19.560847Z", "shell.execute_reply": "2025-05-15T14:42:19.560350Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0: <PERSON> visits clients [5, 8, 6, 2].\n", "   It starts at depot 0 and ends at 1.\n", "   Trip distance is 1620, total delivery 15.\n", "1: <PERSON> visits clients [4, 3, 7].\n", "   It starts at depot 1 and ends at 0.\n", "   Trip distance is 1004, total delivery 14.\n"]}], "source": ["route, _ = res.best.routes()\n", "\n", "for idx, trip in enumerate(route.trips()):\n", "    start_depot = trip.start_depot()\n", "    end_depot = trip.end_depot()\n", "    delivery = trip.delivery()[0]\n", "\n", "    print(f\"{idx}: Trip visits clients {trip.visits()}.\")\n", "    print(f\"   It starts at depot {start_depot} and ends at {end_depot}.\")\n", "    print(f\"   Trip distance is {trip.distance()}, total delivery {delivery}.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A plot reveals the routing and reloading decisions that PyVRP determined:"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"execution": {"iopub.execute_input": "2025-05-15T14:42:19.562559Z", "iopub.status.busy": "2025-05-15T14:42:19.562372Z", "iopub.status.idle": "2025-05-15T14:42:19.766940Z", "shell.execute_reply": "2025-05-15T14:42:19.766404Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["_, ax = plt.subplots(figsize=(8, 8))\n", "plot_solution(res.best, m.data(), ax=ax)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This concludes the brief tutorial: you now know how to model and solve vehicle routing problems using PyVRP's `Model` interface.\n", "PyVRP supports several additional VRP variants we have not covered here.\n", "Have a look at the VRP introduction and other documentation pages to see how those can be modelled and solved. "]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}