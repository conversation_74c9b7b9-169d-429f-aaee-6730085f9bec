"use strict";(()=>{var Ni=Object.create;var Er=Object.defineProperty;var Di=Object.getOwnPropertyDescriptor;var Wi=Object.getOwnPropertyNames,qt=Object.getOwnPropertySymbols,zi=Object.getPrototypeOf,wr=Object.prototype.hasOwnProperty,fo=Object.prototype.propertyIsEnumerable;var mo=(e,t,r)=>t in e?Er(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,I=(e,t)=>{for(var r in t||(t={}))wr.call(t,r)&&mo(e,r,t[r]);if(qt)for(var r of qt(t))fo.call(t,r)&&mo(e,r,t[r]);return e};var uo=(e,t)=>{var r={};for(var o in e)wr.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&qt)for(var o of qt(e))t.indexOf(o)<0&&fo.call(e,o)&&(r[o]=e[o]);return r};var Tr=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var qi=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Wi(t))!wr.call(e,n)&&n!==r&&Er(e,n,{get:()=>t[n],enumerable:!(o=Di(t,n))||o.enumerable});return e};var vt=(e,t,r)=>(r=e!=null?Ni(zi(e)):{},qi(t||!e||!e.__esModule?Er(r,"default",{value:e,enumerable:!0}):r,e));var Ie=(e,t,r)=>new Promise((o,n)=>{var i=l=>{try{s(r.next(l))}catch(c){n(c)}},a=l=>{try{s(r.throw(l))}catch(c){n(c)}},s=l=>l.done?o(l.value):Promise.resolve(l.value).then(i,a);s((r=r.apply(e,t)).next())});var bo=Tr((Sr,ho)=>{(function(e,t){typeof Sr=="object"&&typeof ho!="undefined"?t():typeof define=="function"&&define.amd?define(t):t()})(Sr,function(){"use strict";function e(r){var o=!0,n=!1,i=null,a={text:!0,search:!0,url:!0,tel:!0,email:!0,password:!0,number:!0,date:!0,month:!0,week:!0,time:!0,datetime:!0,"datetime-local":!0};function s(_){return!!(_&&_!==document&&_.nodeName!=="HTML"&&_.nodeName!=="BODY"&&"classList"in _&&"contains"in _.classList)}function l(_){var De=_.type,ke=_.tagName;return!!(ke==="INPUT"&&a[De]&&!_.readOnly||ke==="TEXTAREA"&&!_.readOnly||_.isContentEditable)}function c(_){_.classList.contains("focus-visible")||(_.classList.add("focus-visible"),_.setAttribute("data-focus-visible-added",""))}function p(_){_.hasAttribute("data-focus-visible-added")&&(_.classList.remove("focus-visible"),_.removeAttribute("data-focus-visible-added"))}function m(_){_.metaKey||_.altKey||_.ctrlKey||(s(r.activeElement)&&c(r.activeElement),o=!0)}function u(_){o=!1}function d(_){s(_.target)&&(o||l(_.target))&&c(_.target)}function h(_){s(_.target)&&(_.target.classList.contains("focus-visible")||_.target.hasAttribute("data-focus-visible-added"))&&(n=!0,window.clearTimeout(i),i=window.setTimeout(function(){n=!1},100),p(_.target))}function b(_){document.visibilityState==="hidden"&&(n&&(o=!0),O())}function O(){document.addEventListener("mousemove",B),document.addEventListener("mousedown",B),document.addEventListener("mouseup",B),document.addEventListener("pointermove",B),document.addEventListener("pointerdown",B),document.addEventListener("pointerup",B),document.addEventListener("touchmove",B),document.addEventListener("touchstart",B),document.addEventListener("touchend",B)}function K(){document.removeEventListener("mousemove",B),document.removeEventListener("mousedown",B),document.removeEventListener("mouseup",B),document.removeEventListener("pointermove",B),document.removeEventListener("pointerdown",B),document.removeEventListener("pointerup",B),document.removeEventListener("touchmove",B),document.removeEventListener("touchstart",B),document.removeEventListener("touchend",B)}function B(_){_.target.nodeName&&_.target.nodeName.toLowerCase()==="html"||(o=!1,K())}document.addEventListener("keydown",m,!0),document.addEventListener("mousedown",u,!0),document.addEventListener("pointerdown",u,!0),document.addEventListener("touchstart",u,!0),document.addEventListener("visibilitychange",b,!0),O(),r.addEventListener("focus",d,!0),r.addEventListener("blur",h,!0),r.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&r.host?r.host.setAttribute("data-js-focus-visible",""):r.nodeType===Node.DOCUMENT_NODE&&(document.documentElement.classList.add("js-focus-visible"),document.documentElement.setAttribute("data-js-focus-visible",""))}if(typeof window!="undefined"&&typeof document!="undefined"){window.applyFocusVisiblePolyfill=e;var t;try{t=new CustomEvent("focus-visible-polyfill-ready")}catch(r){t=document.createEvent("CustomEvent"),t.initCustomEvent("focus-visible-polyfill-ready",!1,!1,{})}window.dispatchEvent(t)}typeof document!="undefined"&&e(document)})});var dr=Tr((vy,_n)=>{"use strict";/*!
 * escape-html
 * Copyright(c) 2012-2013 TJ Holowaychuk
 * Copyright(c) 2015 Andreas Lubbe
 * Copyright(c) 2015 Tiancheng "Timothy" Gu
 * MIT Licensed
 */var ka=/["'&<>]/;_n.exports=$a;function $a(e){var t=""+e,r=ka.exec(t);if(!r)return t;var o,n="",i=0,a=0;for(i=r.index;i<t.length;i++){switch(t.charCodeAt(i)){case 34:o="&quot;";break;case 38:o="&amp;";break;case 39:o="&#39;";break;case 60:o="&lt;";break;case 62:o="&gt;";break;default:continue}a!==i&&(n+=t.substring(a,i)),a=i+1,n+=o}return a!==i?n+t.substring(a,i):n}});var Xr=Tr((Vt,Qr)=>{/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */(function(t,r){typeof Vt=="object"&&typeof Qr=="object"?Qr.exports=r():typeof define=="function"&&define.amd?define([],r):typeof Vt=="object"?Vt.ClipboardJS=r():t.ClipboardJS=r()})(Vt,function(){return function(){var e={686:function(o,n,i){"use strict";i.d(n,{default:function(){return Vi}});var a=i(279),s=i.n(a),l=i(370),c=i.n(l),p=i(817),m=i.n(p);function u(N){try{return document.execCommand(N)}catch(A){return!1}}var d=function(A){var L=m()(A);return u("cut"),L},h=d;function b(N){var A=document.documentElement.getAttribute("dir")==="rtl",L=document.createElement("textarea");L.style.fontSize="12pt",L.style.border="0",L.style.padding="0",L.style.margin="0",L.style.position="absolute",L.style[A?"right":"left"]="-9999px";var U=window.pageYOffset||document.documentElement.scrollTop;return L.style.top="".concat(U,"px"),L.setAttribute("readonly",""),L.value=N,L}var O=function(A,L){var U=b(A);L.container.appendChild(U);var V=m()(U);return u("copy"),U.remove(),V},K=function(A){var L=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body},U="";return typeof A=="string"?U=O(A,L):A instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(A==null?void 0:A.type)?U=O(A.value,L):(U=m()(A),u("copy")),U},B=K;function _(N){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_=function(L){return typeof L}:_=function(L){return L&&typeof Symbol=="function"&&L.constructor===Symbol&&L!==Symbol.prototype?"symbol":typeof L},_(N)}var De=function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},L=A.action,U=L===void 0?"copy":L,V=A.container,G=A.target,$e=A.text;if(U!=="copy"&&U!=="cut")throw new Error('Invalid "action" value, use either "copy" or "cut"');if(G!==void 0)if(G&&_(G)==="object"&&G.nodeType===1){if(U==="copy"&&G.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if(U==="cut"&&(G.hasAttribute("readonly")||G.hasAttribute("disabled")))throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`)}else throw new Error('Invalid "target" value, use a valid Element');if($e)return B($e,{container:V});if(G)return U==="cut"?h(G):B(G,{container:V})},ke=De;function Ce(N){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ce=function(L){return typeof L}:Ce=function(L){return L&&typeof Symbol=="function"&&L.constructor===Symbol&&L!==Symbol.prototype?"symbol":typeof L},Ce(N)}function Dt(N,A){if(!(N instanceof A))throw new TypeError("Cannot call a class as a function")}function bt(N,A){for(var L=0;L<A.length;L++){var U=A[L];U.enumerable=U.enumerable||!1,U.configurable=!0,"value"in U&&(U.writable=!0),Object.defineProperty(N,U.key,U)}}function $i(N,A,L){return A&&bt(N.prototype,A),L&&bt(N,L),N}function Pi(N,A){if(typeof A!="function"&&A!==null)throw new TypeError("Super expression must either be null or a function");N.prototype=Object.create(A&&A.prototype,{constructor:{value:N,writable:!0,configurable:!0}}),A&&yr(N,A)}function yr(N,A){return yr=Object.setPrototypeOf||function(U,V){return U.__proto__=V,U},yr(N,A)}function Ri(N){var A=Fi();return function(){var U=Wt(N),V;if(A){var G=Wt(this).constructor;V=Reflect.construct(U,arguments,G)}else V=U.apply(this,arguments);return Ii(this,V)}}function Ii(N,A){return A&&(Ce(A)==="object"||typeof A=="function")?A:ji(N)}function ji(N){if(N===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return N}function Fi(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(N){return!1}}function Wt(N){return Wt=Object.setPrototypeOf?Object.getPrototypeOf:function(L){return L.__proto__||Object.getPrototypeOf(L)},Wt(N)}function xr(N,A){var L="data-clipboard-".concat(N);if(A.hasAttribute(L))return A.getAttribute(L)}var Ui=function(N){Pi(L,N);var A=Ri(L);function L(U,V){var G;return Dt(this,L),G=A.call(this),G.resolveOptions(V),G.listenClick(U),G}return $i(L,[{key:"resolveOptions",value:function(){var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=typeof V.action=="function"?V.action:this.defaultAction,this.target=typeof V.target=="function"?V.target:this.defaultTarget,this.text=typeof V.text=="function"?V.text:this.defaultText,this.container=Ce(V.container)==="object"?V.container:document.body}},{key:"listenClick",value:function(V){var G=this;this.listener=c()(V,"click",function($e){return G.onClick($e)})}},{key:"onClick",value:function(V){var G=V.delegateTarget||V.currentTarget,$e=this.action(G)||"copy",zt=ke({action:$e,container:this.container,target:this.target(G),text:this.text(G)});this.emit(zt?"success":"error",{action:$e,text:zt,trigger:G,clearSelection:function(){G&&G.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(V){return xr("action",V)}},{key:"defaultTarget",value:function(V){var G=xr("target",V);if(G)return document.querySelector(G)}},{key:"defaultText",value:function(V){return xr("text",V)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(V){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body};return B(V,G)}},{key:"cut",value:function(V){return h(V)}},{key:"isSupported",value:function(){var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["copy","cut"],G=typeof V=="string"?[V]:V,$e=!!document.queryCommandSupported;return G.forEach(function(zt){$e=$e&&!!document.queryCommandSupported(zt)}),$e}}]),L}(s()),Vi=Ui},828:function(o){var n=9;if(typeof Element!="undefined"&&!Element.prototype.matches){var i=Element.prototype;i.matches=i.matchesSelector||i.mozMatchesSelector||i.msMatchesSelector||i.oMatchesSelector||i.webkitMatchesSelector}function a(s,l){for(;s&&s.nodeType!==n;){if(typeof s.matches=="function"&&s.matches(l))return s;s=s.parentNode}}o.exports=a},438:function(o,n,i){var a=i(828);function s(p,m,u,d,h){var b=c.apply(this,arguments);return p.addEventListener(u,b,h),{destroy:function(){p.removeEventListener(u,b,h)}}}function l(p,m,u,d,h){return typeof p.addEventListener=="function"?s.apply(null,arguments):typeof u=="function"?s.bind(null,document).apply(null,arguments):(typeof p=="string"&&(p=document.querySelectorAll(p)),Array.prototype.map.call(p,function(b){return s(b,m,u,d,h)}))}function c(p,m,u,d){return function(h){h.delegateTarget=a(h.target,m),h.delegateTarget&&d.call(p,h)}}o.exports=l},879:function(o,n){n.node=function(i){return i!==void 0&&i instanceof HTMLElement&&i.nodeType===1},n.nodeList=function(i){var a=Object.prototype.toString.call(i);return i!==void 0&&(a==="[object NodeList]"||a==="[object HTMLCollection]")&&"length"in i&&(i.length===0||n.node(i[0]))},n.string=function(i){return typeof i=="string"||i instanceof String},n.fn=function(i){var a=Object.prototype.toString.call(i);return a==="[object Function]"}},370:function(o,n,i){var a=i(879),s=i(438);function l(u,d,h){if(!u&&!d&&!h)throw new Error("Missing required arguments");if(!a.string(d))throw new TypeError("Second argument must be a String");if(!a.fn(h))throw new TypeError("Third argument must be a Function");if(a.node(u))return c(u,d,h);if(a.nodeList(u))return p(u,d,h);if(a.string(u))return m(u,d,h);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function c(u,d,h){return u.addEventListener(d,h),{destroy:function(){u.removeEventListener(d,h)}}}function p(u,d,h){return Array.prototype.forEach.call(u,function(b){b.addEventListener(d,h)}),{destroy:function(){Array.prototype.forEach.call(u,function(b){b.removeEventListener(d,h)})}}}function m(u,d,h){return s(document.body,u,d,h)}o.exports=l},817:function(o){function n(i){var a;if(i.nodeName==="SELECT")i.focus(),a=i.value;else if(i.nodeName==="INPUT"||i.nodeName==="TEXTAREA"){var s=i.hasAttribute("readonly");s||i.setAttribute("readonly",""),i.select(),i.setSelectionRange(0,i.value.length),s||i.removeAttribute("readonly"),a=i.value}else{i.hasAttribute("contenteditable")&&i.focus();var l=window.getSelection(),c=document.createRange();c.selectNodeContents(i),l.removeAllRanges(),l.addRange(c),a=l.toString()}return a}o.exports=n},279:function(o){function n(){}n.prototype={on:function(i,a,s){var l=this.e||(this.e={});return(l[i]||(l[i]=[])).push({fn:a,ctx:s}),this},once:function(i,a,s){var l=this;function c(){l.off(i,c),a.apply(s,arguments)}return c._=a,this.on(i,c,s)},emit:function(i){var a=[].slice.call(arguments,1),s=((this.e||(this.e={}))[i]||[]).slice(),l=0,c=s.length;for(l;l<c;l++)s[l].fn.apply(s[l].ctx,a);return this},off:function(i,a){var s=this.e||(this.e={}),l=s[i],c=[];if(l&&a)for(var p=0,m=l.length;p<m;p++)l[p].fn!==a&&l[p].fn._!==a&&c.push(l[p]);return c.length?s[i]=c:delete s[i],this}},o.exports=n,o.exports.TinyEmitter=n}},t={};function r(o){if(t[o])return t[o].exports;var n=t[o]={exports:{}};return e[o](n,n.exports,r),n.exports}return function(){r.n=function(o){var n=o&&o.__esModule?function(){return o.default}:function(){return o};return r.d(n,{a:n}),n}}(),function(){r.d=function(o,n){for(var i in n)r.o(n,i)&&!r.o(o,i)&&Object.defineProperty(o,i,{enumerable:!0,get:n[i]})}}(),function(){r.o=function(o,n){return Object.prototype.hasOwnProperty.call(o,n)}}(),r(686)}().default})});var oL=vt(bo());var Or=function(e,t){return Or=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,o){r.__proto__=o}||function(r,o){for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(r[n]=o[n])},Or(e,t)};function re(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Or(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}function vo(e,t,r,o){function n(i){return i instanceof r?i:new r(function(a){a(i)})}return new(r||(r=Promise))(function(i,a){function s(p){try{c(o.next(p))}catch(m){a(m)}}function l(p){try{c(o.throw(p))}catch(m){a(m)}}function c(p){p.done?i(p.value):n(p.value).then(s,l)}c((o=o.apply(e,t||[])).next())})}function Kt(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},o,n,i,a=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function s(c){return function(p){return l([c,p])}}function l(c){if(o)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(r=0)),r;)try{if(o=1,n&&(i=c[0]&2?n.return:c[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,c[1])).done)return i;switch(n=0,i&&(c=[c[0]&2,i.value]),c[0]){case 0:case 1:i=c;break;case 4:return r.label++,{value:c[1],done:!1};case 5:r.label++,n=c[1],c=[0];continue;case 7:c=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){r=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){r.label=c[1];break}if(c[0]===6&&r.label<i[1]){r.label=i[1],i=c;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(c);break}i[2]&&r.ops.pop(),r.trys.pop();continue}c=t.call(e,r)}catch(p){c=[6,p],n=0}finally{o=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function be(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],o=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&o>=e.length&&(e=void 0),{value:e&&e[o++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Y(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var o=r.call(e),n,i=[],a;try{for(;(t===void 0||t-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(s){a={error:s}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i}function Q(e,t,r){if(r||arguments.length===2)for(var o=0,n=t.length,i;o<n;o++)(i||!(o in t))&&(i||(i=Array.prototype.slice.call(t,0,o)),i[o]=t[o]);return e.concat(i||Array.prototype.slice.call(t))}function it(e){return this instanceof it?(this.v=e,this):new it(e)}function go(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o=r.apply(e,t||[]),n,i=[];return n=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),s("next"),s("throw"),s("return",a),n[Symbol.asyncIterator]=function(){return this},n;function a(d){return function(h){return Promise.resolve(h).then(d,m)}}function s(d,h){o[d]&&(n[d]=function(b){return new Promise(function(O,K){i.push([d,b,O,K])>1||l(d,b)})},h&&(n[d]=h(n[d])))}function l(d,h){try{c(o[d](h))}catch(b){u(i[0][3],b)}}function c(d){d.value instanceof it?Promise.resolve(d.value.v).then(p,m):u(i[0][2],d)}function p(d){l("next",d)}function m(d){l("throw",d)}function u(d,h){d(h),i.shift(),i.length&&l(i[0][0],i[0][1])}}function yo(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],r;return t?t.call(e):(e=typeof be=="function"?be(e):e[Symbol.iterator](),r={},o("next"),o("throw"),o("return"),r[Symbol.asyncIterator]=function(){return this},r);function o(i){r[i]=e[i]&&function(a){return new Promise(function(s,l){a=e[i](a),n(s,l,a.done,a.value)})}}function n(i,a,s,l){Promise.resolve(l).then(function(c){i({value:c,done:s})},a)}}function k(e){return typeof e=="function"}function gt(e){var t=function(o){Error.call(o),o.stack=new Error().stack},r=e(t);return r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r}var Bt=gt(function(e){return function(r){e(this),this.message=r?r.length+` errors occurred during unsubscription:
`+r.map(function(o,n){return n+1+") "+o.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=r}});function We(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var je=function(){function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){var t,r,o,n,i;if(!this.closed){this.closed=!0;var a=this._parentage;if(a)if(this._parentage=null,Array.isArray(a))try{for(var s=be(a),l=s.next();!l.done;l=s.next()){var c=l.value;c.remove(this)}}catch(b){t={error:b}}finally{try{l&&!l.done&&(r=s.return)&&r.call(s)}finally{if(t)throw t.error}}else a.remove(this);var p=this.initialTeardown;if(k(p))try{p()}catch(b){i=b instanceof Bt?b.errors:[b]}var m=this._finalizers;if(m){this._finalizers=null;try{for(var u=be(m),d=u.next();!d.done;d=u.next()){var h=d.value;try{xo(h)}catch(b){i=i!=null?i:[],b instanceof Bt?i=Q(Q([],Y(i)),Y(b.errors)):i.push(b)}}}catch(b){o={error:b}}finally{try{d&&!d.done&&(n=u.return)&&n.call(u)}finally{if(o)throw o.error}}}if(i)throw new Bt(i)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)xo(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(r=this._finalizers)!==null&&r!==void 0?r:[]).push(t)}},e.prototype._hasParent=function(t){var r=this._parentage;return r===t||Array.isArray(r)&&r.includes(t)},e.prototype._addParent=function(t){var r=this._parentage;this._parentage=Array.isArray(r)?(r.push(t),r):r?[r,t]:t},e.prototype._removeParent=function(t){var r=this._parentage;r===t?this._parentage=null:Array.isArray(r)&&We(r,t)},e.prototype.remove=function(t){var r=this._finalizers;r&&We(r,t),t instanceof e&&t._removeParent(this)},e.EMPTY=function(){var t=new e;return t.closed=!0,t}(),e}();var Lr=je.EMPTY;function Yt(e){return e instanceof je||e&&"closed"in e&&k(e.remove)&&k(e.add)&&k(e.unsubscribe)}function xo(e){k(e)?e():e.unsubscribe()}var Pe={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var yt={setTimeout:function(e,t){for(var r=[],o=2;o<arguments.length;o++)r[o-2]=arguments[o];var n=yt.delegate;return n!=null&&n.setTimeout?n.setTimeout.apply(n,Q([e,t],Y(r))):setTimeout.apply(void 0,Q([e,t],Y(r)))},clearTimeout:function(e){var t=yt.delegate;return((t==null?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function Jt(e){yt.setTimeout(function(){var t=Pe.onUnhandledError;if(t)t(e);else throw e})}function Oe(){}var Eo=function(){return Mr("C",void 0,void 0)}();function wo(e){return Mr("E",void 0,e)}function To(e){return Mr("N",e,void 0)}function Mr(e,t,r){return{kind:e,value:t,error:r}}var at=null;function xt(e){if(Pe.useDeprecatedSynchronousErrorHandling){var t=!at;if(t&&(at={errorThrown:!1,error:null}),e(),t){var r=at,o=r.errorThrown,n=r.error;if(at=null,o)throw n}}else e()}function So(e){Pe.useDeprecatedSynchronousErrorHandling&&at&&(at.errorThrown=!0,at.error=e)}var Ht=function(e){re(t,e);function t(r){var o=e.call(this)||this;return o.isStopped=!1,r?(o.destination=r,Yt(r)&&r.add(o)):o.destination=Ji,o}return t.create=function(r,o,n){return new ze(r,o,n)},t.prototype.next=function(r){this.isStopped?Ar(To(r),this):this._next(r)},t.prototype.error=function(r){this.isStopped?Ar(wo(r),this):(this.isStopped=!0,this._error(r))},t.prototype.complete=function(){this.isStopped?Ar(Eo,this):(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(r){this.destination.next(r)},t.prototype._error=function(r){try{this.destination.error(r)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(je);var Ki=Function.prototype.bind;function _r(e,t){return Ki.call(e,t)}var Bi=function(){function e(t){this.partialObserver=t}return e.prototype.next=function(t){var r=this.partialObserver;if(r.next)try{r.next(t)}catch(o){Gt(o)}},e.prototype.error=function(t){var r=this.partialObserver;if(r.error)try{r.error(t)}catch(o){Gt(o)}else Gt(t)},e.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(r){Gt(r)}},e}(),ze=function(e){re(t,e);function t(r,o,n){var i=e.call(this)||this,a;if(k(r)||!r)a={next:r!=null?r:void 0,error:o!=null?o:void 0,complete:n!=null?n:void 0};else{var s;i&&Pe.useDeprecatedNextContext?(s=Object.create(r),s.unsubscribe=function(){return i.unsubscribe()},a={next:r.next&&_r(r.next,s),error:r.error&&_r(r.error,s),complete:r.complete&&_r(r.complete,s)}):a=r}return i.destination=new Bi(a),i}return t}(Ht);function Gt(e){Pe.useDeprecatedSynchronousErrorHandling?So(e):Jt(e)}function Yi(e){throw e}function Ar(e,t){var r=Pe.onStoppedNotification;r&&yt.setTimeout(function(){return r(e,t)})}var Ji={closed:!0,next:Oe,error:Yi,complete:Oe};var Et=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function le(e){return e}function Oo(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Cr(e)}function Cr(e){return e.length===0?le:e.length===1?e[0]:function(r){return e.reduce(function(o,n){return n(o)},r)}}var F=function(){function e(t){t&&(this._subscribe=t)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(t,r,o){var n=this,i=Qi(t)?t:new ze(t,r,o);return xt(function(){var a=n,s=a.operator,l=a.source;i.add(s?s.call(i,l):l?n._subscribe(i):n._trySubscribe(i))}),i},e.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(r){t.error(r)}},e.prototype.forEach=function(t,r){var o=this;return r=Lo(r),new r(function(n,i){var a=new ze({next:function(s){try{t(s)}catch(l){i(l),a.unsubscribe()}},error:i,complete:n});o.subscribe(a)})},e.prototype._subscribe=function(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)},e.prototype[Et]=function(){return this},e.prototype.pipe=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return Cr(t)(this)},e.prototype.toPromise=function(t){var r=this;return t=Lo(t),new t(function(o,n){var i;r.subscribe(function(a){return i=a},function(a){return n(a)},function(){return o(i)})})},e.create=function(t){return new e(t)},e}();function Lo(e){var t;return(t=e!=null?e:Pe.Promise)!==null&&t!==void 0?t:Promise}function Gi(e){return e&&k(e.next)&&k(e.error)&&k(e.complete)}function Qi(e){return e&&e instanceof Ht||Gi(e)&&Yt(e)}function Xi(e){return k(e==null?void 0:e.lift)}function w(e){return function(t){if(Xi(t))return t.lift(function(r){try{return e(r,this)}catch(o){this.error(o)}});throw new TypeError("Unable to lift unknown Observable type")}}function T(e,t,r,o,n){return new Zi(e,t,r,o,n)}var Zi=function(e){re(t,e);function t(r,o,n,i,a,s){var l=e.call(this,r)||this;return l.onFinalize=a,l.shouldUnsubscribe=s,l._next=o?function(c){try{o(c)}catch(p){r.error(p)}}:e.prototype._next,l._error=i?function(c){try{i(c)}catch(p){r.error(p)}finally{this.unsubscribe()}}:e.prototype._error,l._complete=n?function(){try{n()}catch(c){r.error(c)}finally{this.unsubscribe()}}:e.prototype._complete,l}return t.prototype.unsubscribe=function(){var r;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var o=this.closed;e.prototype.unsubscribe.call(this),!o&&((r=this.onFinalize)===null||r===void 0||r.call(this))}},t}(Ht);var wt={schedule:function(e){var t=requestAnimationFrame,r=cancelAnimationFrame,o=wt.delegate;o&&(t=o.requestAnimationFrame,r=o.cancelAnimationFrame);var n=t(function(i){r=void 0,e(i)});return new je(function(){return r==null?void 0:r(n)})},requestAnimationFrame:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=wt.delegate;return((r==null?void 0:r.requestAnimationFrame)||requestAnimationFrame).apply(void 0,Q([],Y(e)))},cancelAnimationFrame:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=wt.delegate;return((r==null?void 0:r.cancelAnimationFrame)||cancelAnimationFrame).apply(void 0,Q([],Y(e)))},delegate:void 0};var Mo=gt(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}});var E=function(e){re(t,e);function t(){var r=e.call(this)||this;return r.closed=!1,r.currentObservers=null,r.observers=[],r.isStopped=!1,r.hasError=!1,r.thrownError=null,r}return t.prototype.lift=function(r){var o=new _o(this,this);return o.operator=r,o},t.prototype._throwIfClosed=function(){if(this.closed)throw new Mo},t.prototype.next=function(r){var o=this;xt(function(){var n,i;if(o._throwIfClosed(),!o.isStopped){o.currentObservers||(o.currentObservers=Array.from(o.observers));try{for(var a=be(o.currentObservers),s=a.next();!s.done;s=a.next()){var l=s.value;l.next(r)}}catch(c){n={error:c}}finally{try{s&&!s.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}}})},t.prototype.error=function(r){var o=this;xt(function(){if(o._throwIfClosed(),!o.isStopped){o.hasError=o.isStopped=!0,o.thrownError=r;for(var n=o.observers;n.length;)n.shift().error(r)}})},t.prototype.complete=function(){var r=this;xt(function(){if(r._throwIfClosed(),!r.isStopped){r.isStopped=!0;for(var o=r.observers;o.length;)o.shift().complete()}})},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var r;return((r=this.observers)===null||r===void 0?void 0:r.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(r){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,r)},t.prototype._subscribe=function(r){return this._throwIfClosed(),this._checkFinalizedStatuses(r),this._innerSubscribe(r)},t.prototype._innerSubscribe=function(r){var o=this,n=this,i=n.hasError,a=n.isStopped,s=n.observers;return i||a?Lr:(this.currentObservers=null,s.push(r),new je(function(){o.currentObservers=null,We(s,r)}))},t.prototype._checkFinalizedStatuses=function(r){var o=this,n=o.hasError,i=o.thrownError,a=o.isStopped;n?r.error(i):a&&r.complete()},t.prototype.asObservable=function(){var r=new F;return r.source=this,r},t.create=function(r,o){return new _o(r,o)},t}(F);var _o=function(e){re(t,e);function t(r,o){var n=e.call(this)||this;return n.destination=r,n.source=o,n}return t.prototype.next=function(r){var o,n;(n=(o=this.destination)===null||o===void 0?void 0:o.next)===null||n===void 0||n.call(o,r)},t.prototype.error=function(r){var o,n;(n=(o=this.destination)===null||o===void 0?void 0:o.error)===null||n===void 0||n.call(o,r)},t.prototype.complete=function(){var r,o;(o=(r=this.destination)===null||r===void 0?void 0:r.complete)===null||o===void 0||o.call(r)},t.prototype._subscribe=function(r){var o,n;return(n=(o=this.source)===null||o===void 0?void 0:o.subscribe(r))!==null&&n!==void 0?n:Lr},t}(E);var Hr=function(e){re(t,e);function t(r){var o=e.call(this)||this;return o._value=r,o}return Object.defineProperty(t.prototype,"value",{get:function(){return this.getValue()},enumerable:!1,configurable:!0}),t.prototype._subscribe=function(r){var o=e.prototype._subscribe.call(this,r);return!o.closed&&r.next(this._value),o},t.prototype.getValue=function(){var r=this,o=r.hasError,n=r.thrownError,i=r._value;if(o)throw n;return this._throwIfClosed(),i},t.prototype.next=function(r){e.prototype.next.call(this,this._value=r)},t}(E);var kt={now:function(){return(kt.delegate||Date).now()},delegate:void 0};var $t=function(e){re(t,e);function t(r,o,n){r===void 0&&(r=1/0),o===void 0&&(o=1/0),n===void 0&&(n=kt);var i=e.call(this)||this;return i._bufferSize=r,i._windowTime=o,i._timestampProvider=n,i._buffer=[],i._infiniteTimeWindow=!0,i._infiniteTimeWindow=o===1/0,i._bufferSize=Math.max(1,r),i._windowTime=Math.max(1,o),i}return t.prototype.next=function(r){var o=this,n=o.isStopped,i=o._buffer,a=o._infiniteTimeWindow,s=o._timestampProvider,l=o._windowTime;n||(i.push(r),!a&&i.push(s.now()+l)),this._trimBuffer(),e.prototype.next.call(this,r)},t.prototype._subscribe=function(r){this._throwIfClosed(),this._trimBuffer();for(var o=this._innerSubscribe(r),n=this,i=n._infiniteTimeWindow,a=n._buffer,s=a.slice(),l=0;l<s.length&&!r.closed;l+=i?1:2)r.next(s[l]);return this._checkFinalizedStatuses(r),o},t.prototype._trimBuffer=function(){var r=this,o=r._bufferSize,n=r._timestampProvider,i=r._buffer,a=r._infiniteTimeWindow,s=(a?1:2)*o;if(o<1/0&&s<i.length&&i.splice(0,i.length-s),!a){for(var l=n.now(),c=0,p=1;p<i.length&&i[p]<=l;p+=2)c=p;c&&i.splice(0,c+1)}},t}(E);var Ao=function(e){re(t,e);function t(r,o){return e.call(this)||this}return t.prototype.schedule=function(r,o){return o===void 0&&(o=0),this},t}(je);var Pt={setInterval:function(e,t){for(var r=[],o=2;o<arguments.length;o++)r[o-2]=arguments[o];var n=Pt.delegate;return n!=null&&n.setInterval?n.setInterval.apply(n,Q([e,t],Y(r))):setInterval.apply(void 0,Q([e,t],Y(r)))},clearInterval:function(e){var t=Pt.delegate;return((t==null?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0};var Tt=function(e){re(t,e);function t(r,o){var n=e.call(this,r,o)||this;return n.scheduler=r,n.work=o,n.pending=!1,n}return t.prototype.schedule=function(r,o){var n;if(o===void 0&&(o=0),this.closed)return this;this.state=r;var i=this.id,a=this.scheduler;return i!=null&&(this.id=this.recycleAsyncId(a,i,o)),this.pending=!0,this.delay=o,this.id=(n=this.id)!==null&&n!==void 0?n:this.requestAsyncId(a,this.id,o),this},t.prototype.requestAsyncId=function(r,o,n){return n===void 0&&(n=0),Pt.setInterval(r.flush.bind(r,this),n)},t.prototype.recycleAsyncId=function(r,o,n){if(n===void 0&&(n=0),n!=null&&this.delay===n&&this.pending===!1)return o;o!=null&&Pt.clearInterval(o)},t.prototype.execute=function(r,o){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var n=this._execute(r,o);if(n)return n;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(r,o){var n=!1,i;try{this.work(r)}catch(a){n=!0,i=a||new Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),i},t.prototype.unsubscribe=function(){if(!this.closed){var r=this,o=r.id,n=r.scheduler,i=n.actions;this.work=this.state=this.scheduler=null,this.pending=!1,We(i,this),o!=null&&(this.id=this.recycleAsyncId(n,o,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(Ao);var kr=function(){function e(t,r){r===void 0&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(t,r,o){return r===void 0&&(r=0),new this.schedulerActionCtor(this,t).schedule(o,r)},e.now=kt.now,e}();var St=function(e){re(t,e);function t(r,o){o===void 0&&(o=kr.now);var n=e.call(this,r,o)||this;return n.actions=[],n._active=!1,n}return t.prototype.flush=function(r){var o=this.actions;if(this._active){o.push(r);return}var n;this._active=!0;do if(n=r.execute(r.state,r.delay))break;while(r=o.shift());if(this._active=!1,n){for(;r=o.shift();)r.unsubscribe();throw n}},t}(kr);var se=new St(Tt),$r=se;var Co=function(e){re(t,e);function t(r,o){var n=e.call(this,r,o)||this;return n.scheduler=r,n.work=o,n}return t.prototype.schedule=function(r,o){return o===void 0&&(o=0),o>0?e.prototype.schedule.call(this,r,o):(this.delay=o,this.state=r,this.scheduler.flush(this),this)},t.prototype.execute=function(r,o){return o>0||this.closed?e.prototype.execute.call(this,r,o):this._execute(r,o)},t.prototype.requestAsyncId=function(r,o,n){return n===void 0&&(n=0),n!=null&&n>0||n==null&&this.delay>0?e.prototype.requestAsyncId.call(this,r,o,n):(r.flush(this),0)},t}(Tt);var Ho=function(e){re(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t}(St);var Pr=new Ho(Co);var ko=function(e){re(t,e);function t(r,o){var n=e.call(this,r,o)||this;return n.scheduler=r,n.work=o,n}return t.prototype.requestAsyncId=function(r,o,n){return n===void 0&&(n=0),n!==null&&n>0?e.prototype.requestAsyncId.call(this,r,o,n):(r.actions.push(this),r._scheduled||(r._scheduled=wt.requestAnimationFrame(function(){return r.flush(void 0)})))},t.prototype.recycleAsyncId=function(r,o,n){var i;if(n===void 0&&(n=0),n!=null?n>0:this.delay>0)return e.prototype.recycleAsyncId.call(this,r,o,n);var a=r.actions;o!=null&&((i=a[a.length-1])===null||i===void 0?void 0:i.id)!==o&&(wt.cancelAnimationFrame(o),r._scheduled=void 0)},t}(Tt);var $o=function(e){re(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.flush=function(r){this._active=!0;var o=this._scheduled;this._scheduled=void 0;var n=this.actions,i;r=r||n.shift();do if(i=r.execute(r.state,r.delay))break;while((r=n[0])&&r.id===o&&n.shift());if(this._active=!1,i){for(;(r=n[0])&&r.id===o&&n.shift();)r.unsubscribe();throw i}},t}(St);var ce=new $o(ko);var M=new F(function(e){return e.complete()});function Qt(e){return e&&k(e.schedule)}function Rr(e){return e[e.length-1]}function Ze(e){return k(Rr(e))?e.pop():void 0}function He(e){return Qt(Rr(e))?e.pop():void 0}function Xt(e,t){return typeof Rr(e)=="number"?e.pop():t}var Ot=function(e){return e&&typeof e.length=="number"&&typeof e!="function"};function Zt(e){return k(e==null?void 0:e.then)}function er(e){return k(e[Et])}function tr(e){return Symbol.asyncIterator&&k(e==null?void 0:e[Symbol.asyncIterator])}function rr(e){return new TypeError("You provided "+(e!==null&&typeof e=="object"?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function ea(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var or=ea();function nr(e){return k(e==null?void 0:e[or])}function ir(e){return go(this,arguments,function(){var r,o,n,i;return Kt(this,function(a){switch(a.label){case 0:r=e.getReader(),a.label=1;case 1:a.trys.push([1,,9,10]),a.label=2;case 2:return[4,it(r.read())];case 3:return o=a.sent(),n=o.value,i=o.done,i?[4,it(void 0)]:[3,5];case 4:return[2,a.sent()];case 5:return[4,it(n)];case 6:return[4,a.sent()];case 7:return a.sent(),[3,2];case 8:return[3,10];case 9:return r.releaseLock(),[7];case 10:return[2]}})})}function ar(e){return k(e==null?void 0:e.getReader)}function D(e){if(e instanceof F)return e;if(e!=null){if(er(e))return ta(e);if(Ot(e))return ra(e);if(Zt(e))return oa(e);if(tr(e))return Po(e);if(nr(e))return na(e);if(ar(e))return ia(e)}throw rr(e)}function ta(e){return new F(function(t){var r=e[Et]();if(k(r.subscribe))return r.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function ra(e){return new F(function(t){for(var r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()})}function oa(e){return new F(function(t){e.then(function(r){t.closed||(t.next(r),t.complete())},function(r){return t.error(r)}).then(null,Jt)})}function na(e){return new F(function(t){var r,o;try{for(var n=be(e),i=n.next();!i.done;i=n.next()){var a=i.value;if(t.next(a),t.closed)return}}catch(s){r={error:s}}finally{try{i&&!i.done&&(o=n.return)&&o.call(n)}finally{if(r)throw r.error}}t.complete()})}function Po(e){return new F(function(t){aa(e,t).catch(function(r){return t.error(r)})})}function ia(e){return Po(ir(e))}function aa(e,t){var r,o,n,i;return vo(this,void 0,void 0,function(){var a,s;return Kt(this,function(l){switch(l.label){case 0:l.trys.push([0,5,6,11]),r=yo(e),l.label=1;case 1:return[4,r.next()];case 2:if(o=l.sent(),!!o.done)return[3,4];if(a=o.value,t.next(a),t.closed)return[2];l.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return s=l.sent(),n={error:s},[3,11];case 6:return l.trys.push([6,,9,10]),o&&!o.done&&(i=r.return)?[4,i.call(r)]:[3,8];case 7:l.sent(),l.label=8;case 8:return[3,10];case 9:if(n)throw n.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})}function Ee(e,t,r,o,n){o===void 0&&(o=0),n===void 0&&(n=!1);var i=t.schedule(function(){r(),n?e.add(this.schedule(null,o)):this.unsubscribe()},o);if(e.add(i),!n)return i}function Le(e,t){return t===void 0&&(t=0),w(function(r,o){r.subscribe(T(o,function(n){return Ee(o,e,function(){return o.next(n)},t)},function(){return Ee(o,e,function(){return o.complete()},t)},function(n){return Ee(o,e,function(){return o.error(n)},t)}))})}function qe(e,t){return t===void 0&&(t=0),w(function(r,o){o.add(e.schedule(function(){return r.subscribe(o)},t))})}function Ro(e,t){return D(e).pipe(qe(t),Le(t))}function Io(e,t){return D(e).pipe(qe(t),Le(t))}function jo(e,t){return new F(function(r){var o=0;return t.schedule(function(){o===e.length?r.complete():(r.next(e[o++]),r.closed||this.schedule())})})}function Fo(e,t){return new F(function(r){var o;return Ee(r,t,function(){o=e[or](),Ee(r,t,function(){var n,i,a;try{n=o.next(),i=n.value,a=n.done}catch(s){r.error(s);return}a?r.complete():r.next(i)},0,!0)}),function(){return k(o==null?void 0:o.return)&&o.return()}})}function sr(e,t){if(!e)throw new Error("Iterable cannot be null");return new F(function(r){Ee(r,t,function(){var o=e[Symbol.asyncIterator]();Ee(r,t,function(){o.next().then(function(n){n.done?r.complete():r.next(n.value)})},0,!0)})})}function Uo(e,t){return sr(ir(e),t)}function Vo(e,t){if(e!=null){if(er(e))return Ro(e,t);if(Ot(e))return jo(e,t);if(Zt(e))return Io(e,t);if(tr(e))return sr(e,t);if(nr(e))return Fo(e,t);if(ar(e))return Uo(e,t)}throw rr(e)}function fe(e,t){return t?Vo(e,t):D(e)}function j(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=He(e);return fe(e,r)}function Ir(e,t){var r=k(e)?e:function(){return e},o=function(n){return n.error(r())};return new F(t?function(n){return t.schedule(o,0,n)}:o)}var Lt=gt(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}});function jr(e,t){var r=typeof t=="object";return new Promise(function(o,n){var i=new ze({next:function(a){o(a),i.unsubscribe()},error:n,complete:function(){r?o(t.defaultValue):n(new Lt)}});e.subscribe(i)})}function No(e){return e instanceof Date&&!isNaN(e)}function f(e,t){return w(function(r,o){var n=0;r.subscribe(T(o,function(i){o.next(e.call(t,i,n++))}))})}var sa=Array.isArray;function ca(e,t){return sa(t)?e.apply(void 0,Q([],Y(t))):e(t)}function et(e){return f(function(t){return ca(e,t)})}var la=Array.isArray,pa=Object.getPrototypeOf,ma=Object.prototype,fa=Object.keys;function Do(e){if(e.length===1){var t=e[0];if(la(t))return{args:t,keys:null};if(ua(t)){var r=fa(t);return{args:r.map(function(o){return t[o]}),keys:r}}}return{args:e,keys:null}}function ua(e){return e&&typeof e=="object"&&pa(e)===ma}function Wo(e,t){return e.reduce(function(r,o,n){return r[o]=t[n],r},{})}function W(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=He(e),o=Ze(e),n=Do(e),i=n.args,a=n.keys;if(i.length===0)return fe([],r);var s=new F(Fr(i,r,a?function(l){return Wo(a,l)}:le));return o?s.pipe(et(o)):s}function Fr(e,t,r){return r===void 0&&(r=le),function(o){zo(t,function(){for(var n=e.length,i=new Array(n),a=n,s=n,l=function(p){zo(t,function(){var m=fe(e[p],t),u=!1;m.subscribe(T(o,function(d){i[p]=d,u||(u=!0,s--),s||o.next(r(i.slice()))},function(){--a||o.complete()}))},o)},c=0;c<n;c++)l(c)},o)}}function zo(e,t,r){e?Ee(r,e,t):t()}function qo(e,t,r,o,n,i,a,s){var l=[],c=0,p=0,m=!1,u=function(){m&&!l.length&&!c&&t.complete()},d=function(b){return c<o?h(b):l.push(b)},h=function(b){i&&t.next(b),c++;var O=!1;D(r(b,p++)).subscribe(T(t,function(K){n==null||n(K),i?d(K):t.next(K)},function(){O=!0},void 0,function(){if(O)try{c--;for(var K=function(){var B=l.shift();a?Ee(t,a,function(){return h(B)}):h(B)};l.length&&c<o;)K();u()}catch(B){t.error(B)}}))};return e.subscribe(T(t,d,function(){m=!0,u()})),function(){s==null||s()}}function te(e,t,r){return r===void 0&&(r=1/0),k(t)?te(function(o,n){return f(function(i,a){return t(o,i,n,a)})(D(e(o,n)))},r):(typeof t=="number"&&(r=t),w(function(o,n){return qo(o,n,e,r)}))}function Mt(e){return e===void 0&&(e=1/0),te(le,e)}function Ko(){return Mt(1)}function Fe(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Ko()(fe(e,He(e)))}function C(e){return new F(function(t){D(e()).subscribe(t)})}var da=["addListener","removeListener"],ha=["addEventListener","removeEventListener"],ba=["on","off"];function v(e,t,r,o){if(k(r)&&(o=r,r=void 0),o)return v(e,t,r).pipe(et(o));var n=Y(ya(e)?ha.map(function(s){return function(l){return e[s](t,l,r)}}):va(e)?da.map(Bo(e,t)):ga(e)?ba.map(Bo(e,t)):[],2),i=n[0],a=n[1];if(!i&&Ot(e))return te(function(s){return v(s,t,r)})(D(e));if(!i)throw new TypeError("Invalid event target");return new F(function(s){var l=function(){for(var c=[],p=0;p<arguments.length;p++)c[p]=arguments[p];return s.next(1<c.length?c:c[0])};return i(l),function(){return a(l)}})}function Bo(e,t){return function(r){return function(o){return e[r](t,o)}}}function va(e){return k(e.addListener)&&k(e.removeListener)}function ga(e){return k(e.on)&&k(e.off)}function ya(e){return k(e.addEventListener)&&k(e.removeEventListener)}function cr(e,t,r){return r?cr(e,t).pipe(et(r)):new F(function(o){var n=function(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];return o.next(a.length===1?a[0]:a)},i=e(n);return k(t)?function(){return t(n,i)}:void 0})}function ve(e,t,r){e===void 0&&(e=0),r===void 0&&(r=$r);var o=-1;return t!=null&&(Qt(t)?r=t:o=t),new F(function(n){var i=No(e)?+e-r.now():e;i<0&&(i=0);var a=0;return r.schedule(function(){n.closed||(n.next(a++),0<=o?this.schedule(void 0,o):n.complete())},i)})}function Ur(e,t){return e===void 0&&(e=0),t===void 0&&(t=se),e<0&&(e=0),ve(e,e,t)}function $(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=He(e),o=Xt(e,1/0),n=e;return n.length?n.length===1?D(n[0]):Mt(o)(fe(n,r)):M}var st=new F(Oe);var xa=Array.isArray;function _t(e){return e.length===1&&xa(e[0])?e[0]:e}function y(e,t){return w(function(r,o){var n=0;r.subscribe(T(o,function(i){return e.call(t,i,n++)&&o.next(i)}))})}function Rt(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=Ze(e),o=_t(e);return o.length?new F(function(n){var i=o.map(function(){return[]}),a=o.map(function(){return!1});n.add(function(){i=a=null});for(var s=function(c){D(o[c]).subscribe(T(n,function(p){if(i[c].push(p),i.every(function(u){return u.length})){var m=i.map(function(u){return u.shift()});n.next(r?r.apply(void 0,Q([],Y(m))):m),i.some(function(u,d){return!u.length&&a[d]})&&n.complete()}},function(){a[c]=!0,!i[c].length&&n.complete()}))},l=0;!n.closed&&l<o.length;l++)s(l);return function(){i=a=null}}):M}function Yo(e){return w(function(t,r){var o=!1,n=null,i=null,a=!1,s=function(){if(i==null||i.unsubscribe(),i=null,o){o=!1;var c=n;n=null,r.next(c)}a&&r.complete()},l=function(){i=null,a&&r.complete()};t.subscribe(T(r,function(c){o=!0,n=c,i||D(e(c)).subscribe(i=T(r,s,l))},function(){a=!0,(!o||!i||i.closed)&&r.complete()}))})}function Me(e,t){return t===void 0&&(t=se),Yo(function(){return ve(e,t)})}function ct(e,t){return t===void 0&&(t=null),t=t!=null?t:e,w(function(r,o){var n=[],i=0;r.subscribe(T(o,function(a){var s,l,c,p,m=null;i++%t===0&&n.push([]);try{for(var u=be(n),d=u.next();!d.done;d=u.next()){var h=d.value;h.push(a),e<=h.length&&(m=m!=null?m:[],m.push(h))}}catch(K){s={error:K}}finally{try{d&&!d.done&&(l=u.return)&&l.call(u)}finally{if(s)throw s.error}}if(m)try{for(var b=be(m),O=b.next();!O.done;O=b.next()){var h=O.value;We(n,h),o.next(h)}}catch(K){c={error:K}}finally{try{O&&!O.done&&(p=b.return)&&p.call(b)}finally{if(c)throw c.error}}},function(){var a,s;try{for(var l=be(n),c=l.next();!c.done;c=l.next()){var p=c.value;o.next(p)}}catch(m){a={error:m}}finally{try{c&&!c.done&&(s=l.return)&&s.call(l)}finally{if(a)throw a.error}}o.complete()},void 0,function(){n=null}))})}function we(e){return w(function(t,r){var o=null,n=!1,i;o=t.subscribe(T(r,void 0,void 0,function(a){i=D(e(a,we(e)(t))),o?(o.unsubscribe(),o=null,i.subscribe(r)):n=!0})),n&&(o.unsubscribe(),o=null,i.subscribe(r))})}function Jo(e,t,r,o,n){return function(i,a){var s=r,l=t,c=0;i.subscribe(T(a,function(p){var m=c++;l=s?e(l,p,m):(s=!0,p),o&&a.next(l)},n&&function(){s&&a.next(l),a.complete()}))}}function Vr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=Ze(e);return r?Oo(Vr.apply(void 0,Q([],Y(e))),et(r)):w(function(o,n){Fr(Q([o],Y(_t(e))))(n)})}function Ke(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Vr.apply(void 0,Q([],Y(e)))}function Nr(e,t){return k(t)?te(e,t,1):te(e,1)}function lt(e){return w(function(t,r){var o=!1,n=null,i=null,a=function(){if(i==null||i.unsubscribe(),i=null,o){o=!1;var s=n;n=null,r.next(s)}};t.subscribe(T(r,function(s){i==null||i.unsubscribe(),o=!0,n=s,i=T(r,a,Oe),D(e(s)).subscribe(i)},function(){a(),r.complete()},void 0,function(){n=i=null}))})}function _e(e,t){return t===void 0&&(t=se),w(function(r,o){var n=null,i=null,a=null,s=function(){if(n){n.unsubscribe(),n=null;var c=i;i=null,o.next(c)}};function l(){var c=a+e,p=t.now();if(p<c){n=this.schedule(void 0,c-p),o.add(n);return}s()}r.subscribe(T(o,function(c){i=c,a=t.now(),n||(n=t.schedule(l,e),o.add(n))},function(){s(),o.complete()},void 0,function(){i=n=null}))})}function Ue(e){return w(function(t,r){var o=!1;t.subscribe(T(r,function(n){o=!0,r.next(n)},function(){o||r.next(e),r.complete()}))})}function Te(e){return e<=0?function(){return M}:w(function(t,r){var o=0;t.subscribe(T(r,function(n){++o<=e&&(r.next(n),e<=o&&r.complete())}))})}function oe(){return w(function(e,t){e.subscribe(T(t,Oe))})}function Go(e){return f(function(){return e})}function Dr(e,t){return t?function(r){return Fe(t.pipe(Te(1),oe()),r.pipe(Dr(e)))}:te(function(r,o){return D(e(r,o)).pipe(Te(1),Go(r))})}function Be(e,t){t===void 0&&(t=se);var r=ve(e,t);return Dr(function(){return r})}function X(e,t){return t===void 0&&(t=le),e=e!=null?e:Ea,w(function(r,o){var n,i=!0;r.subscribe(T(o,function(a){var s=t(a);(i||!e(n,s))&&(i=!1,n=s,o.next(a))}))})}function Ea(e,t){return e===t}function Z(e,t){return X(function(r,o){return t?t(r[e],o[e]):r[e]===o[e]})}function Qo(e){return e===void 0&&(e=wa),w(function(t,r){var o=!1;t.subscribe(T(r,function(n){o=!0,r.next(n)},function(){return o?r.complete():r.error(e())}))})}function wa(){return new Lt}function ne(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(r){return Fe(r,j.apply(void 0,Q([],Y(e))))}}function H(e){return w(function(t,r){try{t.subscribe(r)}finally{r.add(e)}})}function pt(e,t){var r=arguments.length>=2;return function(o){return o.pipe(e?y(function(n,i){return e(n,i,o)}):le,Te(1),r?Ue(t):Qo(function(){return new Lt}))}}function Wr(e){return e<=0?function(){return M}:w(function(t,r){var o=[];t.subscribe(T(r,function(n){o.push(n),e<o.length&&o.shift()},function(){var n,i;try{for(var a=be(o),s=a.next();!s.done;s=a.next()){var l=s.value;r.next(l)}}catch(c){n={error:c}}finally{try{s&&!s.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}r.complete()},void 0,function(){o=null}))})}function Xo(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=He(e),o=Xt(e,1/0);return e=_t(e),w(function(n,i){Mt(o)(fe(Q([n],Y(e)),r)).subscribe(i)})}function Ye(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Xo.apply(void 0,Q([],Y(e)))}function mt(e){var t,r=1/0,o;return e!=null&&(typeof e=="object"?(t=e.count,r=t===void 0?1/0:t,o=e.delay):r=e),r<=0?function(){return M}:w(function(n,i){var a=0,s,l=function(){if(s==null||s.unsubscribe(),s=null,o!=null){var p=typeof o=="number"?ve(o):D(o(a)),m=T(i,function(){m.unsubscribe(),c()});p.subscribe(m)}else c()},c=function(){var p=!1;s=n.subscribe(T(i,void 0,function(){++a<r?s?l():p=!0:i.complete()})),p&&l()};c()})}function zr(e,t){return w(Jo(e,t,arguments.length>=2,!0))}function ue(e){e===void 0&&(e={});var t=e.connector,r=t===void 0?function(){return new E}:t,o=e.resetOnError,n=o===void 0?!0:o,i=e.resetOnComplete,a=i===void 0?!0:i,s=e.resetOnRefCountZero,l=s===void 0?!0:s;return function(c){var p,m,u,d=0,h=!1,b=!1,O=function(){m==null||m.unsubscribe(),m=void 0},K=function(){O(),p=u=void 0,h=b=!1},B=function(){var _=p;K(),_==null||_.unsubscribe()};return w(function(_,De){d++,!b&&!h&&O();var ke=u=u!=null?u:r();De.add(function(){d--,d===0&&!b&&!h&&(m=qr(B,l))}),ke.subscribe(De),!p&&d>0&&(p=new ze({next:function(Ce){return ke.next(Ce)},error:function(Ce){b=!0,O(),m=qr(K,n,Ce),ke.error(Ce)},complete:function(){h=!0,O(),m=qr(K,a),ke.complete()}}),D(_).subscribe(p))})(c)}}function qr(e,t){for(var r=[],o=2;o<arguments.length;o++)r[o-2]=arguments[o];if(t===!0){e();return}if(t!==!1){var n=new ze({next:function(){n.unsubscribe(),e()}});return D(t.apply(void 0,Q([],Y(r)))).subscribe(n)}}function ee(e,t,r){var o,n,i,a,s=!1;return e&&typeof e=="object"?(o=e.bufferSize,a=o===void 0?1/0:o,n=e.windowTime,t=n===void 0?1/0:n,i=e.refCount,s=i===void 0?!1:i,r=e.scheduler):a=e!=null?e:1/0,ue({connector:function(){return new $t(a,t,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:s})}function Ae(e){return y(function(t,r){return e<=r})}function q(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=He(e);return w(function(o,n){(r?Fe(e,o,r):Fe(e,o)).subscribe(n)})}function x(e,t){return w(function(r,o){var n=null,i=0,a=!1,s=function(){return a&&!n&&o.complete()};r.subscribe(T(o,function(l){n==null||n.unsubscribe();var c=0,p=i++;D(e(l,p)).subscribe(n=T(o,function(m){return o.next(t?t(l,m,p,c++):m)},function(){n=null,s()}))},function(){a=!0,s()}))})}function z(e){return w(function(t,r){D(e).subscribe(T(r,function(){return r.complete()},Oe)),!r.closed&&t.subscribe(r)})}function Kr(e,t){return t===void 0&&(t=!1),w(function(r,o){var n=0;r.subscribe(T(o,function(i){var a=e(i,n++);(a||t)&&o.next(i),!a&&o.complete()}))})}function S(e,t,r){var o=k(e)||t||r?{next:e,error:t,complete:r}:e;return o?w(function(n,i){var a;(a=o.subscribe)===null||a===void 0||a.call(o);var s=!0;n.subscribe(T(i,function(l){var c;(c=o.next)===null||c===void 0||c.call(o,l),i.next(l)},function(){var l;s=!1,(l=o.complete)===null||l===void 0||l.call(o),i.complete()},function(l){var c;s=!1,(c=o.error)===null||c===void 0||c.call(o,l),i.error(l)},function(){var l,c;s&&((l=o.unsubscribe)===null||l===void 0||l.call(o)),(c=o.finalize)===null||c===void 0||c.call(o)}))}):le}function Zo(e,t){return w(function(r,o){var n=t!=null?t:{},i=n.leading,a=i===void 0?!0:i,s=n.trailing,l=s===void 0?!1:s,c=!1,p=null,m=null,u=!1,d=function(){m==null||m.unsubscribe(),m=null,l&&(O(),u&&o.complete())},h=function(){m=null,u&&o.complete()},b=function(K){return m=D(e(K)).subscribe(T(o,d,h))},O=function(){if(c){c=!1;var K=p;p=null,o.next(K),!u&&b(K)}};r.subscribe(T(o,function(K){c=!0,p=K,!(m&&!m.closed)&&(a?O():b(K))},function(){u=!0,!(l&&c&&m&&!m.closed)&&o.complete()}))})}function ft(e,t,r){t===void 0&&(t=se);var o=ve(e,t);return Zo(function(){return o},r)}function ie(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=Ze(e);return w(function(o,n){for(var i=e.length,a=new Array(i),s=e.map(function(){return!1}),l=!1,c=function(m){D(e[m]).subscribe(T(n,function(u){a[m]=u,!l&&!s[m]&&(s[m]=!0,(l=s.every(le))&&(s=null))},Oe))},p=0;p<i;p++)c(p);o.subscribe(T(n,function(m){if(l){var u=Q([m],Y(a));n.next(r?r.apply(void 0,Q([],Y(u))):u)}}))})}function en(){let e=new $t(1);return v(document,"DOMContentLoaded",{once:!0}).subscribe(()=>e.next(document)),e}function P(e,t=document){return Array.from(t.querySelectorAll(e))}function R(e,t=document){let r=de(e,t);if(typeof r=="undefined")throw new ReferenceError(`Missing element: expected "${e}" to be present`);return r}function de(e,t=document){return t.querySelector(e)||void 0}function Re(){var e,t,r,o;return(o=(r=(t=(e=document.activeElement)==null?void 0:e.shadowRoot)==null?void 0:t.activeElement)!=null?r:document.activeElement)!=null?o:void 0}var Ta=$(v(document.body,"focusin"),v(document.body,"focusout")).pipe(_e(1),q(void 0),f(()=>Re()||document.body),ee(1));function tt(e){return Ta.pipe(f(t=>e.contains(t)),X())}function It(e,t){return C(()=>$(v(e,"mouseenter").pipe(f(()=>!0)),v(e,"mouseleave").pipe(f(()=>!1))).pipe(t?lt(r=>ve(+!r*t)):le,q(e.matches(":hover"))))}function tn(e,t){if(typeof t=="string"||typeof t=="number")e.innerHTML+=t.toString();else if(t instanceof Node)e.appendChild(t);else if(Array.isArray(t))for(let r of t)tn(e,r)}function g(e,t,...r){let o=document.createElement(e);if(t)for(let n of Object.keys(t))typeof t[n]!="undefined"&&(typeof t[n]!="boolean"?o.setAttribute(n,t[n]):o.setAttribute(n,""));for(let n of r)tn(o,n);return o}function lr(e){if(e>999){let t=+((e-950)%1e3>99);return`${((e+1e-6)/1e3).toFixed(t)}k`}else return e.toString()}function pr(e){let t=g("script",{src:e});return C(()=>(document.head.appendChild(t),$(v(t,"load"),v(t,"error").pipe(x(()=>Ir(()=>new ReferenceError(`Invalid script: ${e}`))))).pipe(f(()=>{}),H(()=>document.head.removeChild(t)),Te(1))))}var rn=new E,Sa=C(()=>typeof ResizeObserver=="undefined"?pr("https://unpkg.com/resize-observer-polyfill"):j(void 0)).pipe(f(()=>new ResizeObserver(e=>e.forEach(t=>rn.next(t)))),x(e=>$(st,j(e)).pipe(H(()=>e.disconnect()))),ee(1));function pe(e){return{width:e.offsetWidth,height:e.offsetHeight}}function he(e){let t=e;for(;t.clientWidth===0&&t.parentElement;)t=t.parentElement;return Sa.pipe(S(r=>r.observe(t)),x(r=>rn.pipe(y(o=>o.target===t),H(()=>r.unobserve(t)))),f(()=>pe(e)),q(pe(e)))}function jt(e){return{width:e.scrollWidth,height:e.scrollHeight}}function on(e){let t=e.parentElement;for(;t&&(e.scrollWidth<=t.scrollWidth&&e.scrollHeight<=t.scrollHeight);)t=(e=t).parentElement;return t?e:void 0}function nn(e){let t=[],r=e.parentElement;for(;r;)(e.clientWidth>r.clientWidth||e.clientHeight>r.clientHeight)&&t.push(r),r=(e=r).parentElement;return t.length===0&&t.push(document.documentElement),t}function Ve(e){return{x:e.offsetLeft,y:e.offsetTop}}function an(e){let t=e.getBoundingClientRect();return{x:t.x+window.scrollX,y:t.y+window.scrollY}}function sn(e){return $(v(window,"load"),v(window,"resize")).pipe(Me(0,ce),f(()=>Ve(e)),q(Ve(e)))}function mr(e){return{x:e.scrollLeft,y:e.scrollTop}}function Je(e){return $(v(e,"scroll"),v(window,"scroll"),v(window,"resize")).pipe(Me(0,ce),f(()=>mr(e)),q(mr(e)))}var cn=new E,Oa=C(()=>j(new IntersectionObserver(e=>{for(let t of e)cn.next(t)},{threshold:0}))).pipe(x(e=>$(st,j(e)).pipe(H(()=>e.disconnect()))),ee(1));function rt(e){return Oa.pipe(S(t=>t.observe(e)),x(t=>cn.pipe(y(({target:r})=>r===e),H(()=>t.unobserve(e)),f(({isIntersecting:r})=>r))))}var fr={drawer:R("[data-md-toggle=drawer]"),search:R("[data-md-toggle=search]")};function ln(e){return fr[e].checked}function Ge(e,t){fr[e].checked!==t&&fr[e].click()}function ot(e){let t=fr[e];return v(t,"change").pipe(f(()=>t.checked),q(t.checked))}function La(e,t){switch(e.constructor){case HTMLInputElement:return e.type==="radio"?/^Arrow/.test(t):!0;case HTMLSelectElement:case HTMLTextAreaElement:return!0;default:return e.isContentEditable}}function Ma(){return $(v(window,"compositionstart").pipe(f(()=>!0)),v(window,"compositionend").pipe(f(()=>!1))).pipe(q(!1))}function pn(){let e=v(window,"keydown").pipe(y(t=>!(t.metaKey||t.ctrlKey)),f(t=>({mode:ln("search")?"search":"global",type:t.key,claim(){t.preventDefault(),t.stopPropagation()}})),y(({mode:t,type:r})=>{if(t==="global"){let o=Re();if(typeof o!="undefined")return!La(o,r)}return!0}),ue());return Ma().pipe(x(t=>t?M:e))}function ge(){return new URL(location.href)}function ut(e,t=!1){if(J("navigation.instant")&&!t){let r=g("a",{href:e.href});document.body.appendChild(r),r.click(),r.remove()}else location.href=e.href}function mn(){return new E}function fn(){return location.hash.slice(1)}function un(e){let t=g("a",{href:e});t.addEventListener("click",r=>r.stopPropagation()),t.click()}function _a(e){return $(v(window,"hashchange"),e).pipe(f(fn),q(fn()),y(t=>t.length>0),ee(1))}function dn(e){return _a(e).pipe(f(t=>de(`[id="${t}"]`)),y(t=>typeof t!="undefined"))}function Ft(e){let t=matchMedia(e);return cr(r=>t.addListener(()=>r(t.matches))).pipe(q(t.matches))}function hn(){let e=matchMedia("print");return $(v(window,"beforeprint").pipe(f(()=>!0)),v(window,"afterprint").pipe(f(()=>!1))).pipe(q(e.matches))}function Br(e,t){return e.pipe(x(r=>r?t():M))}function Yr(e,t){return new F(r=>{let o=new XMLHttpRequest;return o.open("GET",`${e}`),o.responseType="blob",o.addEventListener("load",()=>{o.status>=200&&o.status<300?(r.next(o.response),r.complete()):r.error(new Error(o.statusText))}),o.addEventListener("error",()=>{r.error(new Error("Network error"))}),o.addEventListener("abort",()=>{r.complete()}),typeof(t==null?void 0:t.progress$)!="undefined"&&(o.addEventListener("progress",n=>{var i;if(n.lengthComputable)t.progress$.next(n.loaded/n.total*100);else{let a=(i=o.getResponseHeader("Content-Length"))!=null?i:0;t.progress$.next(n.loaded/+a*100)}}),t.progress$.next(5)),o.send(),()=>o.abort()})}function Qe(e,t){return Yr(e,t).pipe(x(r=>r.text()),f(r=>JSON.parse(r)),ee(1))}function bn(e,t){let r=new DOMParser;return Yr(e,t).pipe(x(o=>o.text()),f(o=>r.parseFromString(o,"text/html")),ee(1))}function vn(e,t){let r=new DOMParser;return Yr(e,t).pipe(x(o=>o.text()),f(o=>r.parseFromString(o,"text/xml")),ee(1))}function gn(){return{x:Math.max(0,scrollX),y:Math.max(0,scrollY)}}function yn(){return $(v(window,"scroll",{passive:!0}),v(window,"resize",{passive:!0})).pipe(f(gn),q(gn()))}function xn(){return{width:innerWidth,height:innerHeight}}function En(){return v(window,"resize",{passive:!0}).pipe(f(xn),q(xn()))}function wn(){return W([yn(),En()]).pipe(f(([e,t])=>({offset:e,size:t})),ee(1))}function ur(e,{viewport$:t,header$:r}){let o=t.pipe(Z("size")),n=W([o,r]).pipe(f(()=>Ve(e)));return W([r,t,n]).pipe(f(([{height:i},{offset:a,size:s},{x:l,y:c}])=>({offset:{x:a.x-l,y:a.y-c+i},size:s})))}var Aa=R("#__config"),At=JSON.parse(Aa.textContent);At.base=`${new URL(At.base,ge())}`;function me(){return At}function J(e){return At.features.includes(e)}function ye(e,t){return typeof t!="undefined"?At.translations[e].replace("#",t.toString()):At.translations[e]}function Se(e,t=document){return R(`[data-md-component=${e}]`,t)}function ae(e,t=document){return P(`[data-md-component=${e}]`,t)}function Ca(e){let t=R(".md-typeset > :first-child",e);return v(t,"click",{once:!0}).pipe(f(()=>R(".md-typeset",e)),f(r=>({hash:__md_hash(r.innerHTML)})))}function Tn(e){if(!J("announce.dismiss")||!e.childElementCount)return M;if(!e.hidden){let t=R(".md-typeset",e);__md_hash(t.innerHTML)===__md_get("__announce")&&(e.hidden=!0)}return C(()=>{let t=new E;return t.subscribe(({hash:r})=>{e.hidden=!0,__md_set("__announce",r)}),Ca(e).pipe(S(r=>t.next(r)),H(()=>t.complete()),f(r=>I({ref:e},r)))})}function Ha(e,{target$:t}){return t.pipe(f(r=>({hidden:r!==e})))}function Sn(e,t){let r=new E;return r.subscribe(({hidden:o})=>{e.hidden=o}),Ha(e,t).pipe(S(o=>r.next(o)),H(()=>r.complete()),f(o=>I({ref:e},o)))}function Ut(e,t){return t==="inline"?g("div",{class:"md-tooltip md-tooltip--inline",id:e,role:"tooltip"},g("div",{class:"md-tooltip__inner md-typeset"})):g("div",{class:"md-tooltip",id:e,role:"tooltip"},g("div",{class:"md-tooltip__inner md-typeset"}))}function On(...e){return g("div",{class:"md-tooltip2",role:"tooltip"},g("div",{class:"md-tooltip2__inner md-typeset"},e))}function Ln(e,t){if(t=t?`${t}_annotation_${e}`:void 0,t){let r=t?`#${t}`:void 0;return g("aside",{class:"md-annotation",tabIndex:0},Ut(t),g("a",{href:r,class:"md-annotation__index",tabIndex:-1},g("span",{"data-md-annotation-id":e})))}else return g("aside",{class:"md-annotation",tabIndex:0},Ut(t),g("span",{class:"md-annotation__index",tabIndex:-1},g("span",{"data-md-annotation-id":e})))}function Mn(e){return g("button",{class:"md-clipboard md-icon",title:ye("clipboard.copy"),"data-clipboard-target":`#${e} > code`})}var An=vt(dr());function Jr(e,t){let r=t&2,o=t&1,n=Object.keys(e.terms).filter(l=>!e.terms[l]).reduce((l,c)=>[...l,g("del",null,(0,An.default)(c))," "],[]).slice(0,-1),i=me(),a=new URL(e.location,i.base);J("search.highlight")&&a.searchParams.set("h",Object.entries(e.terms).filter(([,l])=>l).reduce((l,[c])=>`${l} ${c}`.trim(),""));let{tags:s}=me();return g("a",{href:`${a}`,class:"md-search-result__link",tabIndex:-1},g("article",{class:"md-search-result__article md-typeset","data-md-score":e.score.toFixed(2)},r>0&&g("div",{class:"md-search-result__icon md-icon"}),r>0&&g("h1",null,e.title),r<=0&&g("h2",null,e.title),o>0&&e.text.length>0&&e.text,e.tags&&g("nav",{class:"md-tags"},e.tags.map(l=>{let c=s?l in s?`md-tag-icon md-tag--${s[l]}`:"md-tag-icon":"";return g("span",{class:`md-tag ${c}`},l)})),o>0&&n.length>0&&g("p",{class:"md-search-result__terms"},ye("search.result.term.missing"),": ",...n)))}function Cn(e){let t=e[0].score,r=[...e],o=me(),n=r.findIndex(p=>!`${new URL(p.location,o.base)}`.includes("#")),[i]=r.splice(n,1),a=r.findIndex(p=>p.score<t);a===-1&&(a=r.length);let s=r.slice(0,a),l=r.slice(a);console.log("rendering search result",i.location,{parent:n,index:a});let c=[Jr(i,3),...s.map(p=>Jr(p,1)),...l.length?[g("details",{class:"md-search-result__more"},g("summary",{tabIndex:-1},g("div",null,l.length>0&&l.length===1?ye("search.result.more.one"):ye("search.result.more.other",l.length))),...l.map(p=>Jr(p,1)))]:[]];return g("li",{class:"md-search-result__item"},c)}function Hn(e){return g("ul",{class:"md-source__facts"},Object.entries(e).map(([t,r])=>g("li",{class:`md-source__fact md-source__fact--${t}`},typeof r=="number"?lr(r):r)))}function Gr(e){let t=`tabbed-control tabbed-control--${e}`;return g("div",{class:t,hidden:!0},g("button",{class:"tabbed-button",tabIndex:-1,"aria-hidden":"true"}))}function kn(e){return g("div",{class:"md-typeset__scrollwrap"},g("div",{class:"md-typeset__table"},e))}function Pa(e){var o;let t=me(),r=new URL(`${e.version}/`,new URL("../",t.base));return g("li",{class:"md-version__item"},g("a",{href:`${r}`,class:"md-version__link"},e.title,((o=t.version)==null?void 0:o.alias)&&e.aliases.length>0&&g("span",{class:"md-version__alias"},e.aliases[0])))}function $n(e,t){var o;let r=me();return e=e.filter(n=>{var i;return!((i=n.properties)!=null&&i.hidden)}),g("div",{class:"md-version"},g("button",{class:"md-version__current","aria-label":ye("select.version")},t.title,((o=r.version)==null?void 0:o.alias)&&t.aliases.length>0&&g("span",{class:"md-version__alias"},t.aliases[0])),g("ul",{class:"md-version__list"},e.map(Pa)))}var Ra=0;function Ia(e){let t=W([tt(e),It(e)]).pipe(f(([o,n])=>o||n),X()),r=C(()=>nn(e)).pipe(te(Je),ft(1),Ke(t),f(()=>an(e)));return t.pipe(pt(o=>o),x(()=>W([t,r])),f(([o,n])=>({active:o,offset:n})),ue())}function ja(e,t){let{content$:r,viewport$:o}=t,n=`__tooltip2_${Ra++}`;return C(()=>{let i=new E,a=new Hr(!1);i.pipe(oe(),ne(!1)).subscribe(a);let s=a.pipe(lt(c=>ve(+!c*250,Pr)),X(),x(c=>c?r:M),S(c=>c.id=n),ue());W([i.pipe(f(({active:c})=>c)),s.pipe(x(c=>It(c,250)),q(!1))]).pipe(f(c=>c.some(p=>p))).subscribe(a);let l=a.pipe(y(c=>c),ie(s,o),f(([c,p,{size:m}])=>{let u=e.getBoundingClientRect(),d=u.width/2;if(p.role==="tooltip")return{x:d,y:8+u.height};if(u.y>=m.height/2){let{height:h}=pe(p);return{x:d,y:-16-h}}else return{x:d,y:16+u.height}}));return W([s,i,l]).subscribe(([c,{offset:p},m])=>{c.style.setProperty("--md-tooltip-host-x",`${p.x}px`),c.style.setProperty("--md-tooltip-host-y",`${p.y}px`),c.style.setProperty("--md-tooltip-x",`${m.x}px`),c.style.setProperty("--md-tooltip-y",`${m.y}px`),c.classList.toggle("md-tooltip2--top",m.y<0),c.classList.toggle("md-tooltip2--bottom",m.y>=0)}),a.pipe(y(c=>c),ie(s,(c,p)=>p),y(c=>c.role==="tooltip")).subscribe(c=>{let p=pe(R(":scope > *",c));c.style.setProperty("--md-tooltip-width",`${p.width}px`),c.style.setProperty("--md-tooltip-tail","0px")}),a.pipe(X(),Le(ce),ie(s)).subscribe(([c,p])=>{p.classList.toggle("md-tooltip2--active",c)}),W([a.pipe(y(c=>c)),s]).subscribe(([c,p])=>{p.role==="dialog"?(e.setAttribute("aria-controls",n),e.setAttribute("aria-haspopup","dialog")):e.setAttribute("aria-describedby",n)}),a.pipe(y(c=>!c)).subscribe(()=>{e.removeAttribute("aria-controls"),e.removeAttribute("aria-describedby"),e.removeAttribute("aria-haspopup")}),Ia(e).pipe(S(c=>i.next(c)),H(()=>i.complete()),f(c=>I({ref:e},c)))})}function dt(e,{viewport$:t},r=document.body){return ja(e,{content$:new F(o=>{let n=e.title,i=On(n);return o.next(i),e.removeAttribute("title"),r.append(i),()=>{i.remove(),e.setAttribute("title",n)}}),viewport$:t})}function Fa(e,t){let r=C(()=>W([sn(e),Je(t)])).pipe(f(([{x:o,y:n},i])=>{let{width:a,height:s}=pe(e);return{x:o-i.x+a/2,y:n-i.y+s/2}}));return tt(e).pipe(x(o=>r.pipe(f(n=>({active:o,offset:n})),Te(+!o||1/0))))}function Pn(e,t,{target$:r}){let[o,n]=Array.from(e.children);return C(()=>{let i=new E,a=i.pipe(oe(),ne(!0));return i.subscribe({next({offset:s}){e.style.setProperty("--md-tooltip-x",`${s.x}px`),e.style.setProperty("--md-tooltip-y",`${s.y}px`)},complete(){e.style.removeProperty("--md-tooltip-x"),e.style.removeProperty("--md-tooltip-y")}}),rt(e).pipe(z(a)).subscribe(s=>{e.toggleAttribute("data-md-visible",s)}),$(i.pipe(y(({active:s})=>s)),i.pipe(_e(250),y(({active:s})=>!s))).subscribe({next({active:s}){s?e.prepend(o):o.remove()},complete(){e.prepend(o)}}),i.pipe(Me(16,ce)).subscribe(({active:s})=>{o.classList.toggle("md-tooltip--active",s)}),i.pipe(ft(125,ce),y(()=>!!e.offsetParent),f(()=>e.offsetParent.getBoundingClientRect()),f(({x:s})=>s)).subscribe({next(s){s?e.style.setProperty("--md-tooltip-0",`${-s}px`):e.style.removeProperty("--md-tooltip-0")},complete(){e.style.removeProperty("--md-tooltip-0")}}),v(n,"click").pipe(z(a),y(s=>!(s.metaKey||s.ctrlKey))).subscribe(s=>{s.stopPropagation(),s.preventDefault()}),v(n,"mousedown").pipe(z(a),ie(i)).subscribe(([s,{active:l}])=>{var c;if(s.button!==0||s.metaKey||s.ctrlKey)s.preventDefault();else if(l){s.preventDefault();let p=e.parentElement.closest(".md-annotation");p instanceof HTMLElement?p.focus():(c=Re())==null||c.blur()}}),r.pipe(z(a),y(s=>s===o),Be(125)).subscribe(()=>e.focus()),Fa(e,t).pipe(S(s=>i.next(s)),H(()=>i.complete()),f(s=>I({ref:e},s)))})}function Ua(e){return e.tagName==="CODE"?P(".c, .c1, .cm",e):[e]}function Va(e){let t=[];for(let r of Ua(e)){let o=[],n=document.createNodeIterator(r,NodeFilter.SHOW_TEXT);for(let i=n.nextNode();i;i=n.nextNode())o.push(i);for(let i of o){let a;for(;a=/(\(\d+\))(!)?/.exec(i.textContent);){let[,s,l]=a;if(typeof l=="undefined"){let c=i.splitText(a.index);i=c.splitText(s.length),t.push(c)}else{i.textContent=s,t.push(i);break}}}}return t}function Rn(e,t){t.append(...Array.from(e.childNodes))}function hr(e,t,{target$:r,print$:o}){let n=t.closest("[id]"),i=n==null?void 0:n.id,a=new Map;for(let s of Va(t)){let[,l]=s.textContent.match(/\((\d+)\)/);de(`:scope > li:nth-child(${l})`,e)&&(a.set(l,Ln(l,i)),s.replaceWith(a.get(l)))}return a.size===0?M:C(()=>{let s=new E,l=s.pipe(oe(),ne(!0)),c=[];for(let[p,m]of a)c.push([R(".md-typeset",m),R(`:scope > li:nth-child(${p})`,e)]);return o.pipe(z(l)).subscribe(p=>{e.hidden=!p,e.classList.toggle("md-annotation-list",p);for(let[m,u]of c)p?Rn(m,u):Rn(u,m)}),$(...[...a].map(([,p])=>Pn(p,t,{target$:r}))).pipe(H(()=>s.complete()),ue())})}function In(e){if(e.nextElementSibling){let t=e.nextElementSibling;if(t.tagName==="OL")return t;if(t.tagName==="P"&&!t.children.length)return In(t)}}function jn(e,t){return C(()=>{let r=In(e);return typeof r!="undefined"?hr(r,e,t):M})}var Fn=vt(Xr());var Na=0;function Un(e){if(e.nextElementSibling){let t=e.nextElementSibling;if(t.tagName==="OL")return t;if(t.tagName==="P"&&!t.children.length)return Un(t)}}function Da(e){return he(e).pipe(f(({width:t})=>({scrollable:jt(e).width>t})),Z("scrollable"))}function Vn(e,t){let{matches:r}=matchMedia("(hover)"),o=C(()=>{let n=new E,i=n.pipe(Wr(1));n.subscribe(({scrollable:c})=>{c&&r?e.setAttribute("tabindex","0"):e.removeAttribute("tabindex")});let a=[];if(Fn.default.isSupported()&&(e.closest(".copy")||J("content.code.copy")&&!e.closest(".no-copy"))){let c=e.closest("pre");c.id=`__code_${Na++}`;let p=Mn(c.id);c.insertBefore(p,e),J("content.tooltips")&&a.push(dt(p,{viewport$}))}let s=e.closest(".highlight");if(s instanceof HTMLElement){let c=Un(s);if(typeof c!="undefined"&&(s.classList.contains("annotate")||J("content.code.annotate"))){let p=hr(c,e,t);a.push(he(s).pipe(z(i),f(({width:m,height:u})=>m&&u),X(),x(m=>m?p:M)))}}return P(":scope > span[id]",e).length&&e.classList.add("md-code__content"),Da(e).pipe(S(c=>n.next(c)),H(()=>n.complete()),f(c=>I({ref:e},c)),Ye(...a))});return J("content.lazy")?rt(e).pipe(y(n=>n),Te(1),x(()=>o)):o}function Wa(e,{target$:t,print$:r}){let o=!0;return $(t.pipe(f(n=>n.closest("details:not([open])")),y(n=>e===n),f(()=>({action:"open",reveal:!0}))),r.pipe(y(n=>n||!o),S(()=>o=e.open),f(n=>({action:n?"open":"close"}))))}function Nn(e,t){return C(()=>{let r=new E;return r.subscribe(({action:o,reveal:n})=>{e.toggleAttribute("open",o==="open"),n&&e.scrollIntoView()}),Wa(e,t).pipe(S(o=>r.next(o)),H(()=>r.complete()),f(o=>I({ref:e},o)))})}var Dn=".node circle,.node ellipse,.node path,.node polygon,.node rect{fill:var(--md-mermaid-node-bg-color);stroke:var(--md-mermaid-node-fg-color)}marker{fill:var(--md-mermaid-edge-color)!important}.edgeLabel .label rect{fill:transparent}.flowchartTitleText{fill:var(--md-mermaid-label-fg-color)}.label{color:var(--md-mermaid-label-fg-color);font-family:var(--md-mermaid-font-family)}.label foreignObject{line-height:normal;overflow:visible}.label div .edgeLabel{color:var(--md-mermaid-label-fg-color)}.edgeLabel,.edgeLabel p,.label div .edgeLabel{background-color:var(--md-mermaid-label-bg-color)}.edgeLabel,.edgeLabel p{fill:var(--md-mermaid-label-bg-color);color:var(--md-mermaid-edge-color)}.edgePath .path,.flowchart-link{stroke:var(--md-mermaid-edge-color);stroke-width:.05rem}.edgePath .arrowheadPath{fill:var(--md-mermaid-edge-color);stroke:none}.cluster rect{fill:var(--md-default-fg-color--lightest);stroke:var(--md-default-fg-color--lighter)}.cluster span{color:var(--md-mermaid-label-fg-color);font-family:var(--md-mermaid-font-family)}g #flowchart-circleEnd,g #flowchart-circleStart,g #flowchart-crossEnd,g #flowchart-crossStart,g #flowchart-pointEnd,g #flowchart-pointStart{stroke:none}.classDiagramTitleText{fill:var(--md-mermaid-label-fg-color)}g.classGroup line,g.classGroup rect{fill:var(--md-mermaid-node-bg-color);stroke:var(--md-mermaid-node-fg-color)}g.classGroup text{fill:var(--md-mermaid-label-fg-color);font-family:var(--md-mermaid-font-family)}.classLabel .box{fill:var(--md-mermaid-label-bg-color);background-color:var(--md-mermaid-label-bg-color);opacity:1}.classLabel .label{fill:var(--md-mermaid-label-fg-color);font-family:var(--md-mermaid-font-family)}.node .divider{stroke:var(--md-mermaid-node-fg-color)}.relation{stroke:var(--md-mermaid-edge-color)}.cardinality{fill:var(--md-mermaid-label-fg-color);font-family:var(--md-mermaid-font-family)}.cardinality text{fill:inherit!important}defs marker.marker.composition.class path,defs marker.marker.dependency.class path,defs marker.marker.extension.class path{fill:var(--md-mermaid-edge-color)!important;stroke:var(--md-mermaid-edge-color)!important}defs marker.marker.aggregation.class path{fill:var(--md-mermaid-label-bg-color)!important;stroke:var(--md-mermaid-edge-color)!important}.statediagramTitleText{fill:var(--md-mermaid-label-fg-color)}g.stateGroup rect{fill:var(--md-mermaid-node-bg-color);stroke:var(--md-mermaid-node-fg-color)}g.stateGroup .state-title{fill:var(--md-mermaid-label-fg-color)!important;font-family:var(--md-mermaid-font-family)}g.stateGroup .composit{fill:var(--md-mermaid-label-bg-color)}.nodeLabel,.nodeLabel p{color:var(--md-mermaid-label-fg-color);font-family:var(--md-mermaid-font-family)}a .nodeLabel{text-decoration:underline}.node circle.state-end,.node circle.state-start,.start-state{fill:var(--md-mermaid-edge-color);stroke:none}.end-state-inner,.end-state-outer{fill:var(--md-mermaid-edge-color)}.end-state-inner,.node circle.state-end{stroke:var(--md-mermaid-label-bg-color)}.transition{stroke:var(--md-mermaid-edge-color)}[id^=state-fork] rect,[id^=state-join] rect{fill:var(--md-mermaid-edge-color)!important;stroke:none!important}.statediagram-cluster.statediagram-cluster .inner{fill:var(--md-default-bg-color)}.statediagram-cluster rect{fill:var(--md-mermaid-node-bg-color);stroke:var(--md-mermaid-node-fg-color)}.statediagram-state rect.divider{fill:var(--md-default-fg-color--lightest);stroke:var(--md-default-fg-color--lighter)}defs #statediagram-barbEnd{stroke:var(--md-mermaid-edge-color)}.entityTitleText{fill:var(--md-mermaid-label-fg-color)}.attributeBoxEven,.attributeBoxOdd{fill:var(--md-mermaid-node-bg-color);stroke:var(--md-mermaid-node-fg-color)}.entityBox{fill:var(--md-mermaid-label-bg-color);stroke:var(--md-mermaid-node-fg-color)}.entityLabel{fill:var(--md-mermaid-label-fg-color);font-family:var(--md-mermaid-font-family)}.relationshipLabelBox{fill:var(--md-mermaid-label-bg-color);fill-opacity:1;background-color:var(--md-mermaid-label-bg-color);opacity:1}.relationshipLabel{fill:var(--md-mermaid-label-fg-color)}.relationshipLine{stroke:var(--md-mermaid-edge-color)}defs #ONE_OR_MORE_END *,defs #ONE_OR_MORE_START *,defs #ONLY_ONE_END *,defs #ONLY_ONE_START *,defs #ZERO_OR_MORE_END *,defs #ZERO_OR_MORE_START *,defs #ZERO_OR_ONE_END *,defs #ZERO_OR_ONE_START *{stroke:var(--md-mermaid-edge-color)!important}defs #ZERO_OR_MORE_END circle,defs #ZERO_OR_MORE_START circle{fill:var(--md-mermaid-label-bg-color)}text:not([class]):last-child{fill:var(--md-mermaid-label-fg-color)}.actor{fill:var(--md-mermaid-sequence-actor-bg-color);stroke:var(--md-mermaid-sequence-actor-border-color)}text.actor>tspan{fill:var(--md-mermaid-sequence-actor-fg-color);font-family:var(--md-mermaid-font-family)}line{stroke:var(--md-mermaid-sequence-actor-line-color)}.actor-man circle,.actor-man line{fill:var(--md-mermaid-sequence-actorman-bg-color);stroke:var(--md-mermaid-sequence-actorman-line-color)}.messageLine0,.messageLine1{stroke:var(--md-mermaid-sequence-message-line-color)}.note{fill:var(--md-mermaid-sequence-note-bg-color);stroke:var(--md-mermaid-sequence-note-border-color)}.loopText,.loopText>tspan,.messageText,.noteText>tspan{stroke:none;font-family:var(--md-mermaid-font-family)!important}.messageText{fill:var(--md-mermaid-sequence-message-fg-color)}.loopText,.loopText>tspan{fill:var(--md-mermaid-sequence-loop-fg-color)}.noteText>tspan{fill:var(--md-mermaid-sequence-note-fg-color)}#arrowhead path{fill:var(--md-mermaid-sequence-message-line-color);stroke:none}.loopLine{fill:var(--md-mermaid-sequence-loop-bg-color);stroke:var(--md-mermaid-sequence-loop-border-color)}.labelBox{fill:var(--md-mermaid-sequence-label-bg-color);stroke:none}.labelText,.labelText>span{fill:var(--md-mermaid-sequence-label-fg-color);font-family:var(--md-mermaid-font-family)}.sequenceNumber{fill:var(--md-mermaid-sequence-number-fg-color)}rect.rect{fill:var(--md-mermaid-sequence-box-bg-color);stroke:none}rect.rect+text.text{fill:var(--md-mermaid-sequence-box-fg-color)}defs #sequencenumber{fill:var(--md-mermaid-sequence-number-bg-color)!important}";var Zr,qa=0,Ka=me();function Ba(){return typeof mermaid=="undefined"||mermaid instanceof Element?pr(`${Ka.base}_static/mermaid/mermaid.min.js`):j(void 0)}function Wn(e){return e.classList.remove("mermaid"),Zr||(Zr=Ba().pipe(S(()=>mermaid.initialize({startOnLoad:!1,themeCSS:Dn,sequence:{actorFontSize:"16px",messageFontSize:"16px",noteFontSize:"16px"}})),f(()=>{}),ee(1))),Zr.subscribe(()=>Ie(this,null,function*(){e.classList.add("mermaid");let t=`__mermaid_${qa++}`,r=g("div",{class:"mermaid"}),o=e.textContent,{svg:n,fn:i}=yield mermaid.render(t,o),a=r.attachShadow({mode:"closed"});a.innerHTML=n,e.replaceWith(r),i==null||i(a)})),Zr.pipe(f(()=>({ref:e})))}var zn=g("table");function qn(e){return e.replaceWith(zn),zn.replaceWith(kn(e)),j({ref:e})}function Ya(e){let t=e.find(r=>r.checked)||e[0];return $(...e.map(r=>v(r,"change").pipe(f(()=>R(`label[for="${r.id}"]`))))).pipe(q(R(`label[for="${t.id}"]`)),f(r=>({active:r})))}function Kn(e,{viewport$:t,target$:r}){let o=R(".tabbed-labels",e),n=P(":scope > input",e),i=Gr("prev");e.append(i);let a=Gr("next");return e.append(a),C(()=>{let s=new E,l=s.pipe(oe(),ne(!0));W([s,he(e),rt(e)]).pipe(z(l),Me(1,ce)).subscribe({next([{active:c},p]){let m=Ve(c),{width:u}=pe(c);e.style.setProperty("--md-indicator-x",`${m.x}px`),e.style.setProperty("--md-indicator-width",`${u}px`);let d=mr(o);(m.x<d.x||m.x+u>d.x+p.width)&&o.scrollTo({left:Math.max(0,m.x-16),behavior:"smooth"})},complete(){e.style.removeProperty("--md-indicator-x"),e.style.removeProperty("--md-indicator-width")}}),W([Je(o),he(o)]).pipe(z(l)).subscribe(([c,p])=>{let m=jt(o);i.hidden=c.x<16,a.hidden=c.x>m.width-p.width-16}),$(v(i,"click").pipe(f(()=>-1)),v(a,"click").pipe(f(()=>1))).pipe(z(l)).subscribe(c=>{let{width:p}=pe(o);o.scrollBy({left:p*c,behavior:"smooth"})}),r.pipe(z(l),y(c=>n.includes(c))).subscribe(c=>c.click()),o.classList.add("tabbed-labels--linked");for(let c of n){let p=R(`label[for="${c.id}"]`);p.replaceChildren(g("a",{href:`#${p.htmlFor}`,tabIndex:-1},...Array.from(p.childNodes))),v(p.firstElementChild,"click").pipe(z(l),y(m=>!(m.metaKey||m.ctrlKey)),S(m=>{m.preventDefault(),m.stopPropagation()})).subscribe(()=>{history.replaceState({},"",`#${p.htmlFor}`),p.click()})}return J("content.tabs.link")&&s.pipe(Ae(1),ie(t)).subscribe(([{active:c},{offset:p}])=>{let m=c.innerText.trim();if(c.hasAttribute("data-md-switching"))c.removeAttribute("data-md-switching");else{let u=e.offsetTop-p.y;for(let h of P("[data-tabs]"))for(let b of P(":scope > input",h)){let O=R(`label[for="${b.id}"]`);if(O!==c&&O.innerText.trim()===m){O.setAttribute("data-md-switching",""),b.click();break}}window.scrollTo({top:e.offsetTop-u});let d=__md_get("__tabs")||[];__md_set("__tabs",[...new Set([m,...d])])}}),s.pipe(z(l)).subscribe(()=>{for(let c of P("audio, video",e))c.pause()}),Ya(n).pipe(S(c=>s.next(c)),H(()=>s.complete()),f(c=>I({ref:e},c)))}).pipe(qe(se))}function Bn(e,{viewport$:t,target$:r,print$:o}){return $(...P(".annotate:not(.highlight)",e).map(n=>jn(n,{target$:r,print$:o})),...P("pre:not(.mermaid) > code",e).map(n=>Vn(n,{target$:r,print$:o})),...P("pre.mermaid",e).map(n=>Wn(n)),...P("table:not([class])",e).map(n=>qn(n)),...P("details",e).map(n=>Nn(n,{target$:r,print$:o})),...P("[data-tabs]",e).map(n=>Kn(n,{viewport$:t,target$:r})),...P("[title]",e).filter(()=>J("content.tooltips")).map(n=>dt(n,{viewport$:t})))}function Ja(e,{alert$:t}){return t.pipe(x(r=>$(j(!0),j(!1).pipe(Be(2e3))).pipe(f(o=>({message:r,active:o})))))}function Yn(e,t){let r=R(".md-typeset",e);return C(()=>{let o=new E;return o.subscribe(({message:n,active:i})=>{e.classList.toggle("md-dialog--active",i),r.textContent=n}),Ja(e,t).pipe(S(n=>o.next(n)),H(()=>o.complete()),f(n=>I({ref:e},n)))})}var Ga=0;function Qa(e,t){document.body.append(e);let{width:r}=pe(e);e.style.setProperty("--md-tooltip-width",`${r}px`),e.remove();let o=on(t),n=typeof o!="undefined"?Je(o):j({x:0,y:0}),i=$(tt(t),It(t)).pipe(X());return W([i,n]).pipe(f(([a,s])=>{let{x:l,y:c}=Ve(t),p=pe(t),m=t.closest("table");return m&&t.parentElement&&(l+=m.offsetLeft+t.parentElement.offsetLeft,c+=m.offsetTop+t.parentElement.offsetTop),{active:a,offset:{x:l-s.x+p.width/2-r/2,y:c-s.y+p.height+8}}}))}function Jn(e){let t=e.title;if(!t.length)return M;let r=`__tooltip_${Ga++}`,o=Ut(r,"inline"),n=R(".md-typeset",o);return n.innerHTML=t,C(()=>{let i=new E;return i.subscribe({next({offset:a}){o.style.setProperty("--md-tooltip-x",`${a.x}px`),o.style.setProperty("--md-tooltip-y",`${a.y}px`)},complete(){o.style.removeProperty("--md-tooltip-x"),o.style.removeProperty("--md-tooltip-y")}}),$(i.pipe(y(({active:a})=>a)),i.pipe(_e(250),y(({active:a})=>!a))).subscribe({next({active:a}){a?(e.insertAdjacentElement("afterend",o),e.setAttribute("aria-describedby",r),e.removeAttribute("title")):(o.remove(),e.removeAttribute("aria-describedby"),e.setAttribute("title",t))},complete(){o.remove(),e.removeAttribute("aria-describedby"),e.setAttribute("title",t)}}),i.pipe(Me(16,ce)).subscribe(({active:a})=>{o.classList.toggle("md-tooltip--active",a)}),i.pipe(ft(125,ce),y(()=>!!e.offsetParent),f(()=>e.offsetParent.getBoundingClientRect()),f(({x:a})=>a)).subscribe({next(a){a?o.style.setProperty("--md-tooltip-0",`${-a}px`):o.style.removeProperty("--md-tooltip-0")},complete(){o.style.removeProperty("--md-tooltip-0")}}),Qa(o,e).pipe(S(a=>i.next(a)),H(()=>i.complete()),f(a=>I({ref:e},a)))}).pipe(qe(se))}function Xa({viewport$:e}){if(!J("header.autohide"))return j(!1);let t=e.pipe(f(({offset:{y:n}})=>n),ct(2,1),f(([n,i])=>[n<i,i]),Z(0)),r=W([e,t]).pipe(y(([{offset:n},[,i]])=>Math.abs(i-n.y)>100),f(([,[n]])=>n),X()),o=ot("search");return W([e,o]).pipe(f(([{offset:n},i])=>n.y>400&&!i),X(),x(n=>n?r:j(!1)),q(!1))}function Gn(e,t){return C(()=>W([he(e),Xa(t)])).pipe(f(([{height:r},o])=>({height:r,hidden:o})),X((r,o)=>r.height===o.height&&r.hidden===o.hidden),ee(1))}function Qn(e,{header$:t,main$:r}){return C(()=>{let o=new E,n=o.pipe(oe(),ne(!0));o.pipe(Z("active"),Ke(t)).subscribe(([{active:a},{hidden:s}])=>{e.classList.toggle("md-header--shadow",a&&!s),e.hidden=s});let i=fe(P("[title]",e)).pipe(y(()=>J("content.tooltips")),te(a=>Jn(a)));return r.subscribe(o),t.pipe(z(n),f(a=>I({ref:e},a)),Ye(i.pipe(z(n))))})}function Za(e,{viewport$:t,header$:r}){return ur(e,{viewport$:t,header$:r}).pipe(f(({offset:{y:o}})=>{let{height:n}=pe(e);return{active:o>=n}}),Z("active"))}function Xn(e,t){return C(()=>{let r=new E;r.subscribe({next({active:n}){e.classList.toggle("md-header__title--active",n)},complete(){e.classList.remove("md-header__title--active")}});let o=de(".md-content h1, .objdesc > dt .descname");return typeof o=="undefined"?M:Za(o,t).pipe(S(n=>r.next(n)),H(()=>r.complete()),f(n=>I({ref:e},n)))})}function Zn(e,{viewport$:t,header$:r}){let o=r.pipe(f(({height:i})=>i),X()),n=o.pipe(x(()=>he(e).pipe(f(({height:i})=>({top:e.offsetTop,bottom:e.offsetTop+i})),Z("bottom"))));return W([o,n,t]).pipe(f(([i,{top:a,bottom:s},{offset:{y:l},size:{height:c}}])=>(c=Math.max(0,c-Math.max(0,a-l,i)-Math.max(0,c+l-s)),{offset:a-i,height:c,active:a-i<=l})),X((i,a)=>i.offset===a.offset&&i.height===a.height&&i.active===a.active))}function es(e){let t=__md_get("__palette")||{index:e.findIndex(o=>matchMedia(o.getAttribute("data-md-color-media")).matches)},r=Math.max(0,Math.min(t.index,e.length-1));return j(...e).pipe(te(o=>v(o,"change").pipe(f(()=>o))),q(e[r]),f(o=>({index:e.indexOf(o),color:{media:o.getAttribute("data-md-color-media"),scheme:o.getAttribute("data-md-color-scheme"),primary:o.getAttribute("data-md-color-primary"),accent:o.getAttribute("data-md-color-accent")}})),ee(1))}function ei(e){let t=P("input",e),r=g("meta",{name:"theme-color"});document.head.appendChild(r);let o=g("meta",{name:"color-scheme"});document.head.appendChild(o);let n=Ft("(prefers-color-scheme: light)");return C(()=>{let i=new E;return i.subscribe(a=>{if(document.body.setAttribute("data-md-color-switching",""),a.color.media==="(prefers-color-scheme)"){let s=matchMedia("(prefers-color-scheme: light)"),l=document.querySelector(s.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");a.color.scheme=l.getAttribute("data-md-color-scheme"),a.color.primary=l.getAttribute("data-md-color-primary"),a.color.accent=l.getAttribute("data-md-color-accent")}for(let[s,l]of Object.entries(a.color))document.body.setAttribute(`data-md-color-${s}`,l);for(let s=0;s<t.length;s++){let l=t[s].nextElementSibling;l instanceof HTMLElement&&(l.hidden=a.index!==s)}__md_set("__palette",a)}),v(e,"keydown").pipe(y(a=>a.key==="Enter"),ie(i,(a,s)=>s)).subscribe(({index:a})=>{a=(a+1)%t.length,t[a].click(),t[a].focus()}),i.pipe(f(()=>{let a=Se("header"),s=window.getComputedStyle(a);return o.content=s.colorScheme,s.backgroundColor.match(/\d+/g).map(l=>(+l).toString(16).padStart(2,"0")).join("")})).subscribe(a=>r.content=`#${a}`),i.pipe(Le(se)).subscribe(()=>{document.body.removeAttribute("data-md-color-switching")}),es(t).pipe(z(n.pipe(Ae(1))),mt(),S(a=>i.next(a)),H(()=>i.complete()),f(a=>I({ref:e},a)))})}function ti(e,{progress$:t}){return C(()=>{let r=new E;return r.subscribe(({value:o})=>{e.style.setProperty("--md-progress-value",`${o}`)}),t.pipe(S(o=>r.next({value:o})),H(()=>r.complete()),f(o=>({ref:e,value:o})))})}function ts(e){let{searchParams:t}=ge();t.has("q")&&(Ge("search",!0),e.value=t.get("q"),e.focus(),ot("search").pipe(pt(n=>!n)).subscribe(()=>{let n=ge();n.searchParams.delete("q"),history.replaceState({},"",`${n}`)}));let r=tt(e),o=$(v(e,"keyup"),r).pipe(f(()=>e.value),X());return W([o,r]).pipe(f(([n,i])=>({value:n,focus:i})),ee(1))}function ri(e){let t=new E,r=t.pipe(oe(),ne(!0));t.pipe(Z("focus")).subscribe(({focus:n})=>{n&&Ge("search",n)}),v(e.form,"reset").pipe(z(r)).subscribe(()=>e.focus());let o=R("header [for=__search]");return v(o,"click").subscribe(()=>e.focus()),ts(e).pipe(S(n=>t.next(n)),H(()=>t.complete()),f(n=>I({ref:e},n)),ee(1))}var to=vt(dr());var rs=me();function eo(e){return`${rs.base}${e}`}var br;function oi(e){return new Promise((t,r)=>{let o=document.createElement("script"),n=eo(e);o.src=n,o.addEventListener("load",()=>t()),o.addEventListener("error",()=>{console.error(`Failed to load search data: ${n}`),r()}),document.body.appendChild(o)})}function os(){return br!==void 0||(br=Promise.all([oi("_static/language_data.js"),oi("searchindex.js")]).then(()=>{})),br}var Ne={objNameMatch:11,objPartialMatch:6,objPrio:{0:15,1:5,2:-5},objPrioDefault:0,title:15,partialTitle:7,term:5,partialTerm:2},ro;window.Search={setIndex:e=>{ro=e}};var ns=!1;function is(e,t){let{docurls:r,objects:o,objnames:n,titles:i}=ro,a=[];function s(l,c,p,m,u,d,h){var K;let b=(l?`${l}.`:"")+d,O=b.toLowerCase();if(O.indexOf(e)>-1){let B=0,_=O.split(".");O===e||_[_.length-1]===e?B+=Ne.objNameMatch:_[_.length-1].indexOf(e)>-1&&(B+=Ne.objPartialMatch);let De=n[p][2],ke=i[c];if(t.length>0){let Ce=`${l} ${d} ${De} ${ke} ${h!=null?h:""}`.toLowerCase(),Dt=!0;for(let bt=0;bt<t.length;bt++)if(Ce.indexOf(t[bt])===-1){Dt=!1;break}if(!Dt)return}u===0?u=b:u===1&&(u=`${n[p][1]}-${b}`),B+=(K=Ne.objPrio[m])!=null?K:Ne.objPrioDefault,a.push({docurl:r[c],title:b,anchor:`#${u}`,objectLabel:De,synopsis:h,score:B})}}for(let l in o){let c=o[l];if(Array.isArray(c))for(let p of c){let[m,u,d,h,b,O]=p;s(l,m,u,d,h,b,O)}else for(let p in c){let[m,u,d,h,b]=c[p];s(l,m,u,d,h,p,b)}}return a}function as(e){return e.split(/\s+/)}function oo(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ss(e,t){let{docurls:r,titles:o,terms:n,titleterms:i}=ro,a={},s={},l=[];for(let c=0;c<e.length;c++){let p=e[c],m=[],u=[{files:n[p],score:Ne.term},{files:i[p],score:Ne.title}];if(p.length>2){let d=oo(p);if(!n[p])for(let h in n)h.match(d)&&u.push({files:n[h],score:Ne.partialTerm});if(!i[p])for(let h in i)h.match(d)&&u.push({files:i[h],score:Ne.partialTitle})}if(u.every(d=>d.files===void 0))break;u.forEach(d=>{let h=d.files;if(h!==void 0){Array.isArray(h)||(h=[h]),m.push(...h);for(let b=0;b<h.length;b++){let O=h[b];O in s||(s[O]={}),s[O][p]=d.score}}});for(let d=0;d<m.length;d++){let h=m[d];h in a&&a[h].indexOf(p)===-1?a[h].push(p):a[h]=[p]}}for(let c in a){let p=parseInt(c,10),m=!0,u=e.filter(d=>d.length>2).length;if(!(a[p].length!==e.length&&a[p].length!==u)){for(let d=0;d<t.length;d++){let h=n[t[d]];if(Array.isArray(h)?h.indexOf(p)!==-1:h===p){m=!1;break}let b=i[t[d]];if(Array.isArray(b)?b.indexOf(p)!==-1:b===p){m=!1;break}}if(m){let d=Math.max(...a[c].map(h=>s[c][h]));l.push({docurl:r[c],title:o[c],anchor:"",objectLabel:null,synopsis:null,score:d})}}}return l}function cs(e){let t=new DOMParser().parseFromString(e,"text/html");t.querySelectorAll(".headerlink").forEach(l=>{var c;(c=l.parentNode)==null||c.removeChild(l)});let r=t.querySelector("[role=main]");if(r===null)return console.warn("Content block not found. Sphinx search tries to obtain it via '[role=main]'. Could you check your theme or template."),[];let o=r.querySelectorAll("h1, h2, h3, h4, h5, h6"),n,i=[],a=t.createRange(),s=(l,c)=>{var d;l!==void 0?a.setStartAfter(l):a.setStartBefore(r),c!==void 0?a.setEndBefore(c):a.setEndAfter(r);let p=a.toString().trim(),m=(d=l==null?void 0:l.textContent)==null?void 0:d.trim();if(!m&&!p)return;let u=l!==void 0?`#${l.id}`:"";i.push({title:m!=null?m:"",anchor:u,text:p})};return o.forEach(l=>{if(!l.id)return;let p=n;n=l,s(p,l)}),s(n,void 0),i}function ls(e,t){let r=cs(e),o=t.map(i=>new RegExp(oo(i),"im")),n=[];for(let i=0;i<r.length;++i){let a=r[i],s=0,l=1/0,c={};for(let p=0,m=t.length;p<m;++p){let u=o[p],d=a.title.match(u),h=!1;d!==null?(l=0,s+=Ne.partialTitle,h=!0):(d=a.text.match(u),d!==null&&(s+=Ne.partialTerm,l=Math.min(l,d.index),h=!0)),c[t[p]]=h}s!==0&&n.push({sectionIndex:i,score:s,snippetIndex:l,terms:c})}if(n.sort((i,a)=>i.score!==a.score?a.score-i.score:i.sectionIndex-a.sectionIndex),n.length!==0)return n.map(i=>{let s=r[i.sectionIndex],l=Math.max(i.snippetIndex-240/2,0);return{snippet:(l>0?"\u2026":"")+s.text.substr(l,240).trim()+(l+240<s.text.length?"\u2026":""),anchor:s.anchor,title:s.title,score:i.score,terms:i.terms}})}function ps(e,t,r){return Ie(this,null,function*(){let o=eo(e.docurl)+e.anchor,n=ms(e.title),i={};for(let p of t)i[p]=!0;if(e.objectLabel!==null)return[{location:o,score:e.score,terms:i,title:`${r(n)}<span class="search-result-objlabel">${(0,to.default)(e.objectLabel)}</span>`,text:r(e.synopsis)}];let a=eo(e.docurl),s;if(window.location.protocol!=="file:")try{let m=yield(yield fetch(a)).text();s=ls(m,t)}catch(p){console.warn("Failed to retrieve search result document: ",p)}s===void 0&&(s=[{score:-1,title:"",anchor:"",snippet:"",terms:i}]);let l=[];s[0].score!==-1&&l.push({location:o,score:e.score,terms:i,title:r(n),text:""});let c;for(let p of s)c===void 0&&(c=p.score),l.push({location:o+p.anchor,score:p.score===c?e.score:0,terms:p.terms,title:r(p.title||n),text:r(p.snippet)});return l})}function ms(e){return new DOMParser().parseFromString(e,"text/html").body.textContent||""}function ni(e){return Ie(this,null,function*(){yield os();let t=new Stemmer,r=[],o=[],n=[],i=[];for(let p of as(e)){let m=!1;p[0]==="-"&&(m=!0,p=p.substring(1));let u=p.toLowerCase();if(u.length===0)continue;i.push(u);let d=!1;for(let h of u.matchAll(new RegExp("\\p{L}+","gu"))){let b=h[0];if(stopwords.indexOf(b)!==-1)continue;let O=t.stemWord(b);O.length<3&&b.length>=3&&(O=b);let K;m?K=o:(K=r,d=!0),K.indexOf(O)===-1&&K.push(O)}!m&&d&&n.push(u)}let a=[];for(let p=0;p<i.length;p++){let m=[...i.slice(0,p),...i.slice(p+1,i.length)];a.push(...is(i[p],m))}a.push(...ss(r,o)),a.sort((p,m)=>{let u=p.score,d=m.score;if(u!==d)return d-u;let h=p.title.toLowerCase(),b=m.title.toLowerCase();return h>b?1:h<b?-1:0}),ns&&console.log(a);let s=new RegExp(`(?:${n.map(oo).join("|")})`,"imgu"),l=p=>`<mark data-md-highlight>${p}</mark>`,c=p=>(0,to.default)(p).replace(s,l).replace(/<\/mark>(\s+)<mark[^>]*>/gimu,"$1");return{count:a.length,get:p=>ps(a[p],n,c)}})}function ii(e,{query$:t}){let r=R(":scope > :first-child",e),o=R(":scope > :last-child",e);ot("search").subscribe(m=>o.setAttribute("role",m?"list":"presentation"));let n,i,a=e.parentElement,s=16,l=()=>a.scrollTop+a.clientHeight+s>a.scrollHeight,c=()=>{i!==void 0&&l()&&(i(),i=void 0)};a.addEventListener("scroll",c,{passive:!0}),window.addEventListener("resize",c,{passive:!0});let p=m=>Ie(this,null,function*(){n=m;let u=4,d=u;for(let h=0;h<m.count;++h){if(h===d&&(l()||(yield new Promise(O=>{i=()=>O(void 0)})),d+=u),n!==m)return;let b=yield m.get(h);if(n!==m)return;o.appendChild(Cn(b))}});return t.pipe(Z("value"),lt(()=>Ur(250)),Nr(m=>Ie(this,null,function*(){if(m.value)return ni(m.value)})),Le(ce)).subscribe(m=>{switch(o.innerHTML="",m?m.count:0){case 0:r.textContent=m?ye("search.result.none"):ye("search.result.placeholder");break;case 1:r.textContent=ye("search.result.one");break;default:let u=lr(m.count);r.textContent=ye("search.result.other",u)}m&&p(m)}),j()}function fs(e,{query$:t}){return t.pipe(f(({value:r})=>{let o=ge();return o.hash="",r=r.replace(/\s+/g,"+").replace(/&/g,"%26").replace(/=/g,"%3D"),o.search=`q=${r}`,{url:o}}))}function ai(e,t){let r=new E,o=r.pipe(oe(),ne(!0));return r.subscribe(({url:n})=>{e.setAttribute("data-clipboard-text",e.href),e.href=`${n}`}),v(e,"click").pipe(z(o)).subscribe(n=>n.preventDefault()),fs(e,t).pipe(S(n=>r.next(n)),H(()=>r.complete()),f(n=>I({ref:e},n)))}function si(e,{keyboard$:t}){let r=Se("search-query");return t.pipe(y(({mode:o})=>o==="search")).subscribe(o=>{switch(o.type){case"ArrowRight":e.innerText.length&&r.selectionStart===r.value.length&&(r.value=e.innerText);break}}),j()}function ci(e,{keyboard$:t}){try{let r=Se("search-query",e),o=Se("search-result",e);v(e,"click").pipe(y(({target:i})=>i instanceof Element&&!!i.closest("a"))).subscribe(()=>Ge("search",!1)),t.pipe(y(({mode:i})=>i==="search")).subscribe(i=>{let a=Re();switch(i.type){case"Enter":if(a===r){let s=new Map;for(let l of P(":first-child [href]",o)){let c=l.firstElementChild;s.set(l,parseFloat(c.getAttribute("data-md-score")))}if(s.size){let[[l]]=[...s].sort(([,c],[,p])=>p-c);l.click()}i.claim()}break;case"Escape":case"Tab":Ge("search",!1),r.blur();break;case"ArrowUp":case"ArrowDown":if(typeof a=="undefined")r.focus();else{let s=[r,...P(":not(details) > [href], summary, details[open] [href]",o)],l=Math.max(0,(Math.max(0,s.indexOf(a))+s.length+(i.type==="ArrowUp"?-1:1))%s.length);s[l].focus()}i.claim();break;default:r!==Re()&&r.focus()}}),t.pipe(y(({mode:i})=>i==="global")).subscribe(i=>{switch(i.type){case"f":case"s":case"/":r.focus(),r.select(),i.claim();break}});let n=ri(r);return $(n,ii(o,{query$:n})).pipe(Ye(...ae("search-share",e).map(i=>ai(i,{query$:n})),...ae("search-suggest",e).map(i=>si(i,{keyboard$:t}))))}catch(r){return e.hidden=!0,st}}var no=vt(Xr());function us(e){e.setAttribute("data-md-copying","");let t=e.closest("[data-copy]"),r=t?t.getAttribute("data-copy"):e.innerText;return e.removeAttribute("data-md-copying"),r.trimEnd()}function li({alert$:e}){no.default.isSupported()&&new F(t=>{new no.default("[data-clipboard-target], [data-clipboard-text]",{text:r=>r.getAttribute("data-clipboard-text")||us(R(r.getAttribute("data-clipboard-target")))}).on("success",r=>t.next(r))}).pipe(S(t=>{t.trigger.focus()}),f(()=>ye("clipboard.copied"))).subscribe(e)}function pi(e,t){return e.protocol=t.protocol,e.hostname=t.hostname,e}function ds(e,t){let r=new Map;for(let o of P("url",e)){let n=R("loc",o),i=[pi(new URL(n.textContent),t)];r.set(`${i[0]}`,i);for(let a of P("[rel=alternate]",o)){let s=a.getAttribute("href");s!=null&&i.push(pi(new URL(s),t))}}return r}function vr(e){return vn(new URL("sitemap.xml",e)).pipe(f(t=>ds(t,new URL(e))),we(()=>j(new Map)))}function hs(e,t){if(!(e.target instanceof Element))return M;let r=e.target.closest("a");if(r===null)return M;if(r.target||e.metaKey||e.ctrlKey)return M;let o=new URL(r.href);return o.search=o.hash="",t.has(`${o}`)?(e.preventDefault(),j(new URL(r.href))):M}function mi(e){let t=new Map;for(let r of P(":scope > *",e.head))t.set(r.outerHTML,r);return t}function fi(e){for(let t of P("[href], [src]",e))for(let r of["href","src"]){let o=t.getAttribute(r);if(o&&!/^(?:[a-z]+:)?\/\//i.test(o)){t[r]=t[r];break}}return j(e)}function bs(e){return Ie(this,null,function*(){let t=new Set,r=new Set;for(let a of P("script",document))a.src?t.add(new URL(a.src,document.baseURI).toString()):r.add(a.outerHTML);for(let a of["[data-md-component=announce]","[data-md-component=container]","[data-md-component=header-topic]","[data-md-component=outdated]","[data-md-component=logo]","[data-md-component=skip]",...J("navigation.tabs.sticky")?["[data-md-component=tabs]"]:[]]){let s=de(a),l=de(a,e);typeof s!="undefined"&&typeof l!="undefined"&&s.replaceWith(l)}let o=mi(document);for(let[a,s]of mi(e))o.has(a)?o.delete(a):document.head.appendChild(s);for(let a of o.values()){let s=a.getAttribute("name");s!=="theme-color"&&s!=="color-scheme"&&a.remove()}let n=[];for(let a of P("script",e))if(a.src){let s=new URL(a.src,document.baseURI).toString();if(!t.has(s)){let l=document.createElement("script");for(let c of a.getAttributeNames())l.setAttribute(c,a.getAttribute(c));l.src=s,l.async||n.push(new Promise(c=>l.addEventListener("load",()=>c()))),document.body.appendChild(l),t.add(s)}}else{let s=a.outerHTML;if(!r.has(s)){let l=document.createElement("script");for(let c of a.getAttributeNames())l.setAttribute(c,a.getAttribute(c));l.textContent=a.textContent,document.body.appendChild(l),r.add(s)}}yield Promise.all(n);let i=Se("container");return yield jr(Fe(P("script",i)).pipe(x(a=>{let s=e.createElement("script");if(a.src){for(let l of a.getAttributeNames())s.setAttribute(l,a.getAttribute(l));return a.replaceWith(s),new F(l=>{s.onload=()=>l.complete()})}else return s.textContent=a.textContent,a.replaceWith(s),M}),oe(),ne(document))),document})}function ui({location$:e,viewport$:t,progress$:r}){let o=me();if(location.protocol==="file:")return M;let n=vr(o.base);j(document).subscribe(fi);let i=v(document.body,"click").pipe(Ke(n),x(([l,c])=>hs(l,c)),ue()),a=v(window,"popstate").pipe(f(ge),ue());i.pipe(ie(t)).subscribe(([l,{offset:c}])=>{history.replaceState(c,""),history.pushState(null,"",l)}),$(i,a).subscribe(e);let s=e.pipe(Z("pathname"),x(l=>bn(l,{progress$:r}).pipe(we(()=>(ut(l,!0),M)))),x(fi),x(bs),ue());return $(s.pipe(ie(e,(l,c)=>c)),s.pipe(x(()=>e),Z("hash")),e.pipe(X((l,c)=>l.pathname===c.pathname&&l.hash===c.hash),x(()=>i),S(()=>history.back()))).subscribe(l=>{var c,p;history.state!==null||!l.hash?window.scrollTo(0,(p=(c=history.state)==null?void 0:c.y)!=null?p:0):(history.scrollRestoration="auto",un(l.hash),history.scrollRestoration="manual")}),e.subscribe(()=>{history.scrollRestoration="manual"}),v(window,"beforeunload").subscribe(()=>{history.scrollRestoration="auto"}),t.pipe(Z("offset"),_e(100)).subscribe(({offset:l})=>{history.replaceState(l,"")}),s}var di=vt(dr());function hi(e){let t=e.separator.split("|").map(n=>n.replace(/(\(\?[!=<][^)]+\))/g,"").length===0?"\uFFFD":n).join("|"),r=new RegExp(t,"img"),o=(n,i,a)=>`${i}<mark data-md-highlight>${a}</mark>`;return n=>{n=n.replace(/[\s*+\-:~^]+/g," ").trim();let i=new RegExp(`(^|${e.separator}|)(${n.replace(/[|\\{}()[\]^$+*?.-]/g,"\\$&").replace(r,"|")})`,"img");return a=>(0,di.default)(a).replace(i,o).replace(/<\/mark>(\s+)<mark[^>]*>/img,"$1")}}function bi(e){var p;let{selectedVersionSitemap:t,selectedVersionBaseURL:r,currentLocation:o,currentBaseURL:n}=e,i=(p=io(n))==null?void 0:p.pathname;if(i===void 0)return;let a=vs(o.pathname,i);if(a===void 0)return;let s=ys(t.keys());if(!t.has(s))return;let l=io(a,s);if(!l||!t.has(l.href))return;let c=io(a,r);if(c)return c.hash=o.hash,c.search=o.search,c}function io(e,t){try{return new URL(e,t)}catch(r){return}}function vs(e,t){if(e.startsWith(t))return e.slice(t.length)}function gs(e,t){let r=Math.min(e.length,t.length),o;for(o=0;o<r&&e[o]===t[o];++o);return o}function ys(e){let t;for(let r of e)t===void 0?t=r:t=t.slice(0,gs(t,r));return t!=null?t:""}function vi({document$:e}){var a;let t=me(),r=t.version.staticVersions?j(t.version.staticVersions):Qe(new URL((a=t.version.versionPath)!=null?a:"../versions.json",t.base)),o=new URL("..",t.base),n=s=>new URL(s,o).toString().replace(/\/*$/,""),i=r.pipe(f(s=>{let l=t.base.toString().replace(/\/*$/,"");return s.find(({version:c,aliases:p})=>n(c)===l||p.find(m=>n(m)===l))||s[0]}));r.pipe(f(s=>new Map(s.map(l=>[`${new URL(`../${l.version}/`,t.base)}`,l]))),x(s=>v(document.body,"click").pipe(y(l=>!l.metaKey&&!l.ctrlKey),ie(i),x(([l,c])=>{if(l.target instanceof Element){let p=l.target.closest("a");if(p&&!p.target&&s.has(p.href)){let m=p.href;return!l.target.closest(".md-version")&&s.get(m)===c?M:(l.preventDefault(),j(new URL(m)))}}return M}),x(l=>vr(l).pipe(f(c=>{var p;return(p=bi({selectedVersionSitemap:c,selectedVersionBaseURL:l,currentLocation:ge(),currentBaseURL:t.base}))!=null?p:l})))))).subscribe(s=>ut(s,!0)),W([r,i]).subscribe(([s,l])=>{R(".md-header__topic").appendChild($n(s,l))}),e.pipe(x(()=>i)).subscribe(s=>{var p;let l=new URL(t.base),c=__md_get("__outdated",sessionStorage,l);if(c===null){c=!0;let m=((p=t.version)==null?void 0:p.default)||"latest";Array.isArray(m)||(m=[m]);e:for(let u of m)for(let d of s.aliases.concat(s.version))if(new RegExp(u,"i").test(d)){c=!1;break e}__md_set("__outdated",c,sessionStorage,l)}if(c)for(let m of ae("outdated"))m.hidden=!1})}function gi(e,{location$:t}){let r={lang:[],separator:"\\s+",pipeline:[]};return W([t.pipe(q(ge()),y(o=>!!o.searchParams.get("h")))]).pipe(f(([o])=>hi(r)(o.searchParams.get("h"))),f(o=>{var a;let n=new Map,i=document.createNodeIterator(e,NodeFilter.SHOW_TEXT);for(let s=i.nextNode();s;s=i.nextNode())if((a=s.parentElement)!=null&&a.offsetHeight){let l=s.textContent,c=o(l);c.length>l.length&&n.set(s,c)}for(let[s,l]of n){let{childNodes:c}=g("span",null,l);s.replaceWith(...Array.from(c))}return{ref:e,nodes:n}}))}function xs(e,{viewport$:t,main$:r}){let o=e.closest(".md-grid"),n=o.offsetTop-o.parentElement.offsetTop;return W([r,t]).pipe(f(([{offset:i,height:a},{offset:{y:s}}])=>(a=a+Math.min(n,Math.max(0,s-i))-n,{height:a,locked:s>=i+n})),X((i,a)=>i.height===a.height&&i.locked===a.locked))}function ao(e,o){var n=o,{header$:t}=n,r=uo(n,["header$"]);let i=R(".md-sidebar__scrollwrap",e),{y:a}=Ve(i);return C(()=>{let s=new E,l=s.pipe(oe(),ne(!0)),c=s.pipe(Me(0,ce));return c.pipe(ie(t)).subscribe({next([{height:p},{height:m}]){i.style.height=`${p-2*a}px`,e.style.top=`${m}px`},complete(){i.style.height="",e.style.top=""}}),c.pipe(pt()).subscribe(()=>{for(let p of P(".md-nav__link--active[href]",e)){if(!p.clientHeight)continue;let m=p.closest(".md-sidebar__scrollwrap");if(typeof m!="undefined"){let u=p.offsetTop-m.offsetTop,{height:d}=pe(m);m.scrollTo({top:u-d/2})}}}),fe(P("label[tabindex]",e)).pipe(te(p=>v(p,"click").pipe(Le(se),f(()=>p),z(l)))).subscribe(p=>{let m=R(`[id="${p.htmlFor}"]`);R(`[aria-labelledby="${p.id}"]`).setAttribute("aria-expanded",`${m.checked}`)}),xs(e,r).pipe(S(p=>s.next(p)),H(()=>s.complete()),f(p=>I({ref:e},p)))})}function yi(e,t){if(typeof t!="undefined"){let r=`https://api.github.com/repos/${e}/${t}`;return Rt(Qe(`${r}/releases/latest`).pipe(we(()=>M),f(o=>({version:o.tag_name})),Ue({})),Qe(r).pipe(we(()=>M),f(o=>({stars:o.stargazers_count,forks:o.forks_count})),Ue({}))).pipe(f(([o,n])=>I(I({},o),n)))}else{let r=`https://api.github.com/users/${e}`;return Qe(r).pipe(f(o=>({repositories:o.public_repos})),Ue({}))}}function xi(e,t){let r=`https://${e}/api/v4/projects/${encodeURIComponent(t)}`;return Rt(Qe(`${r}/releases/permalink/latest`).pipe(we(()=>M),f(({tag_name:o})=>({version:o})),Ue({})),Qe(r).pipe(we(()=>M),f(({star_count:o,forks_count:n})=>({stars:o,forks:n})),Ue({}))).pipe(f(([o,n])=>I(I({},o),n)))}function Ei(e){let t=e.match(/^.+github\.com\/([^/]+)\/?([^/]+)?/i);if(t){let[,r,o]=t;return yi(r,o)}if(t=e.match(/^.+?([^/]*gitlab[^/]+)\/(.+?)\/?$/i),t){let[,r,o]=t;return xi(r,o)}return M}var Es;function ws(e){return Es||(Es=C(()=>{let t=__md_get("__source",sessionStorage);if(t)return j(t);if(ae("consent").length){let o=__md_get("__consent");if(!(o&&o.github))return M}return Ei(e.href).pipe(S(o=>__md_set("__source",o,sessionStorage)))}).pipe(we(()=>M),y(t=>Object.keys(t).length>0),f(t=>({facts:t})),ee(1)))}function wi(e){let t=R(":scope > :last-child",e);return C(()=>{let r=new E;return r.subscribe(({facts:o})=>{t.appendChild(Hn(o)),t.classList.add("md-source__repository--active")}),ws(e).pipe(S(o=>r.next(o)),H(()=>r.complete()),f(o=>I({ref:e},o)))})}function Ts(e,{viewport$:t,header$:r}){return he(document.body).pipe(x(()=>ur(e,{header$:r,viewport$:t})),f(({offset:{y:o}})=>({hidden:o>=10})),Z("hidden"))}function Ti(e,t){return C(()=>{let r=new E;return r.subscribe({next({hidden:o}){e.hidden=o},complete(){e.hidden=!1}}),(J("navigation.tabs.sticky")?j({hidden:!1}):Ts(e,t)).pipe(S(o=>r.next(o)),H(()=>r.complete()),f(o=>I({ref:e},o)))})}function Ss(e,{viewport$:t,header$:r,excludedLinks:o}){let n=new Map,i=P("a[href]",e);for(let l of i){if(o!=null&&o.has(l))continue;let c=l.getAttribute("href"),p;if(c.startsWith("#")){let m=decodeURIComponent(l.hash.substring(1));p=de(`[id="${m}"]`)}else p=de(`a.pseudo-toc-entry[href=${CSS.escape(c)}]`);if(typeof p!="undefined"){let m=l.closest(".md-nav__link");m!==null&&n.set(m,p)}}let a=r.pipe(Z("height"),f(({height:l})=>{let c=Se("main"),p=R(":scope > :first-child",c);return l+.8*(p.offsetTop-c.offsetTop)}),ue());return he(document.body).pipe(Z("height"),x(l=>C(()=>{let c=[];return j([...n].reduce((p,[m,u])=>{for(;c.length&&n.get(c[c.length-1]).tagName>=u.tagName;)c.pop();let d=u.offsetTop;for(;!d&&u.parentElement;)u=u.parentElement,d=u.offsetTop;let h=u.offsetParent;for(;h;h=h.offsetParent)d+=h.offsetTop;return p.set([...c=[...c,m]].reverse(),d)},new Map))}).pipe(f(c=>new Map([...c].sort(([,p],[,m])=>p-m))),Ke(a),x(([c,p])=>t.pipe(zr(([m,u],{offset:{y:d},size:h})=>{let b=d+h.height>=Math.floor(l.height);for(;u.length;){let[,O]=u[0];if(O-p<d||b)m=[...m,u.shift()];else break}for(;m.length;){let[,O]=m[m.length-1];if(O-p>=d&&!b)u=[m.pop(),...u];else break}return[m,u]},[[],[...c]]),X((m,u)=>m[0]===u[0]&&m[1]===u[1])))))).pipe(f(([l,c])=>({prev:l.map(([p])=>p),next:c.map(([p])=>p)})),q({prev:[],next:[]}),ct(2,1),f(([l,c])=>l.prev.length<c.prev.length?{prev:c.prev.slice(Math.max(0,l.prev.length-1),c.prev.length),next:[]}:{prev:c.prev.slice(-1),next:c.next.slice(0,c.next.length-l.next.length)}))}function so(e,{viewport$:t,header$:r,target$:o}){return C(()=>{let n=new E,i=n.pipe(oe(),ne(!0)),a=e.dataset.mdComponent==="toc",s=a?"md-nav__link--active":"md-nav__link--in-viewport";if(n.subscribe(({prev:c,next:p})=>{for(let[m]of p)m.classList.remove("md-nav__link--passed"),m.classList.remove(s);for(let[m,[u]]of c.entries())u.classList.add("md-nav__link--passed"),u.classList.toggle(s,m===c.length-1)}),J("toc.follow")&&(a||!J("toc.integrate"))){let c=!a||J("toc.integrate");n.pipe(_e(1)).subscribe(({prev:p})=>{var h;let m;if(p.length===0&&c&&(m=(h=e.querySelector("a[href='#']"))!=null?h:e),c=!1,p.length!==0&&(m=p[p.length-1][0]),m===void 0||!m.offsetHeight)return;let u=m.parentElement,d=5;for(;u!==null&&u.scrollHeight-d<=u.clientHeight;)u=u.parentElement;if(u!==null&&u!==document.body&&u!==document.documentElement){let b=m.getBoundingClientRect(),O=u.getBoundingClientRect();u.scrollTo({top:u.scrollTop+(b.y-O.height/2-O.y)})}})}a&&J("navigation.tracking")&&t.pipe(z(i),Z("offset"),_e(250),Ae(1),z(o.pipe(Ae(1))),mt({delay:250}),ie(n)).subscribe(([,{prev:c}])=>{let p=ge(),m=c[c.length-1];if(m&&m.length){let[u]=m,{hash:d}=new URL(u.href);p.hash!==d&&(p.hash=d,history.replaceState({},"",`${p}`))}else p.hash="",history.replaceState({},"",`${p}`)}),J("toc.sticky")&&he(document.body).pipe(Z("width"),_e(0)).subscribe(()=>{let c=new Map,p="--md-nav__header-height";for(let m of P(".md-nav__link",e)){let u=m.nextElementSibling;if(!(u instanceof HTMLElement)||u.tagName!=="NAV")continue;let d="",h=NaN,b=u.parentElement.closest("nav");if(b!==null){let O=c.get(b);O!==void 0&&(d=`${O.height} + `,h=O.zindex-1)}isNaN(h)&&(h=100),d+=`${m.offsetHeight}px + 0.625em`,m.classList.add("md-nav__sticky"),m.style.setProperty("--md-nav__sticky-zindex",h.toString()),u.style.setProperty(p,`calc(${d})`),c.set(u,{height:d,zindex:h})}});let l=a?void 0:new Set(P("[data-md-component='toc'] a[href]",e));return Ss(e,{viewport$:t,header$:r,excludedLinks:l}).pipe(S(c=>n.next(c)),H(()=>n.complete()),f(c=>I({ref:e},c)))})}function Os(e,{viewport$:t,main$:r,target$:o}){let n=t.pipe(f(({offset:{y:a}})=>a),ct(2,1),f(([a,s])=>a>s&&s>0),X()),i=r.pipe(f(({active:a})=>a));return W([i,n]).pipe(f(([a,s])=>!(a&&s)),X(),z(o.pipe(Ae(1))),ne(!0),mt({delay:250}),f(a=>({hidden:a})))}function Si(e,{viewport$:t,header$:r,main$:o,target$:n}){let i=new E,a=i.pipe(oe(),ne(!0));return i.subscribe({next({hidden:s}){e.hidden=s,s?(e.setAttribute("tabindex","-1"),e.blur()):e.removeAttribute("tabindex")},complete(){e.style.top="",e.hidden=!0,e.removeAttribute("tabindex")}}),r.pipe(z(a),Z("height")).subscribe(({height:s})=>{e.style.top=`${s+16}px`}),v(e,"click").subscribe(s=>{s.preventDefault(),window.scrollTo({top:0})}),Os(e,{viewport$:t,main$:o,target$:n}).pipe(S(s=>i.next(s)),H(()=>i.complete()),f(s=>I({ref:e},s)))}function Oi({document$:e,viewport$:t}){e.pipe(x(()=>P(".md-ellipsis")),te(r=>rt(r).pipe(z(e.pipe(Ae(1))),y(o=>o),f(()=>r),Te(1))),y(r=>r.offsetWidth<r.scrollWidth),te(r=>{let o=r.innerText,n=r.closest("a")||r;return n.title=o,J("content.tooltips")?dt(n,{viewport$:t}).pipe(z(e.pipe(Ae(1))),H(()=>n.removeAttribute("title"))):M})).subscribe(),J("content.tooltips")&&e.pipe(x(()=>P(".md-status")),te(r=>dt(r,{viewport$:t}))).subscribe()}function Li({document$:e,tablet$:t}){e.pipe(x(()=>P(".md-toggle--indeterminate")),S(r=>{r.indeterminate=!0,r.checked=!1}),te(r=>v(r,"change").pipe(Kr(()=>r.classList.contains("md-toggle--indeterminate")),f(()=>r))),ie(t)).subscribe(([r,o])=>{r.classList.remove("md-toggle--indeterminate"),o&&(r.checked=!1)})}function Ls(){return/(iPad|iPhone|iPod)/.test(navigator.userAgent)}function Mi({document$:e}){e.pipe(x(()=>P("[data-md-scrollfix]")),S(t=>t.removeAttribute("data-md-scrollfix")),y(Ls),te(t=>v(t,"touchstart").pipe(f(()=>t)))).subscribe(t=>{let r=t.scrollTop;r===0?t.scrollTop=1:r+t.offsetHeight===t.scrollHeight&&(t.scrollTop=r-1)})}function _i({viewport$:e,tablet$:t}){W([ot("search"),t]).pipe(f(([r,o])=>r&&!o),x(r=>j(r).pipe(Be(r?400:100))),ie(e)).subscribe(([r,{offset:{y:o}}])=>{if(r)document.body.setAttribute("data-md-scrolllock",""),document.body.style.top=`-${o}px`;else{let n=-1*parseInt(document.body.style.top,10);document.body.removeAttribute("data-md-scrolllock"),document.body.style.top="",n&&window.scrollTo(0,n)}})}Object.entries||(Object.entries=function(e){let t=[];for(let r of Object.keys(e))t.push([r,e[r]]);return t});Object.values||(Object.values=function(e){let t=[];for(let r of Object.keys(e))t.push(e[r]);return t});typeof Element!="undefined"&&(Element.prototype.scrollTo||(Element.prototype.scrollTo=function(e,t){typeof e=="object"?(this.scrollLeft=e.left,this.scrollTop=e.top):(this.scrollLeft=e,this.scrollTop=t)}),Element.prototype.replaceWith||(Element.prototype.replaceWith=function(...e){let t=this.parentNode;if(t){e.length===0&&t.removeChild(this);for(let r=e.length-1;r>=0;r--){let o=e[r];typeof o=="string"?o=document.createTextNode(o):o.parentNode&&o.parentNode.removeChild(o),r?t.insertBefore(this.previousSibling,o):t.replaceChild(o,this)}}}));document.documentElement.classList.remove("no-js");document.documentElement.classList.add("js");var nt=en(),Nt=mn(),ht=dn(Nt),co=pn(),xe=wn(),gr=Ft("(min-width: 960px)"),Ci=Ft("(min-width: 1220px)"),Hi=hn(),Ms=me(),lo=new E;li({alert$:lo});var po=new E;J("navigation.instant")&&ui({location$:Nt,viewport$:xe,progress$:po}).subscribe(nt);var Ai;((Ai=Ms.version)==null?void 0:Ai.provider)==="mike"&&vi({document$:nt});$(Nt,ht).pipe(Be(125)).subscribe(()=>{Ge("drawer",!1),Ge("search",!1)});co.pipe(y(({mode:e})=>e==="global")).subscribe(e=>{switch(e.type){case"p":case",":let t=de("link[rel=prev]");typeof t!="undefined"&&ut(t);break;case"n":case".":let r=de("link[rel=next]");typeof r!="undefined"&&ut(r);break;case"Enter":let o=Re();o instanceof HTMLLabelElement&&o.click()}});Oi({viewport$:xe,document$:nt});Li({document$:nt,tablet$:gr});Mi({document$:nt});_i({viewport$:xe,tablet$:gr});var Xe=Gn(Se("header"),{viewport$:xe}),Ct=nt.pipe(f(()=>Se("main")),x(e=>Zn(e,{viewport$:xe,header$:Xe})),ee(1)),_s=$(...ae("consent").map(e=>Sn(e,{target$:ht})),...ae("dialog").map(e=>Yn(e,{alert$:lo})),...ae("palette").map(e=>ei(e)),...ae("progress").map(e=>ti(e,{progress$:po})),...ae("search").map(e=>ci(e,{keyboard$:co})),...ae("source").map(e=>wi(e))),As=C(()=>$(...ae("announce").map(e=>Tn(e)),...ae("content").map(e=>Bn(e,{viewport$:xe,target$:ht,print$:Hi})),...ae("content").map(e=>J("search.highlight")?gi(e,{location$:Nt}):M),...ae("header").map(e=>Qn(e,{viewport$:xe,header$:Xe,main$:Ct})),...ae("header-title").map(e=>Xn(e,{viewport$:xe,header$:Xe})),...ae("sidebar").map(e=>e.getAttribute("data-md-type")==="navigation"?Br(Ci,()=>ao(e,{viewport$:xe,header$:Xe,main$:Ct})):Br(gr,()=>ao(e,{viewport$:xe,header$:Xe,main$:Ct}))),...ae("tabs").map(e=>Ti(e,{viewport$:xe,header$:Xe})),...ae("toc").map(e=>so(e,{viewport$:xe,header$:Xe,main$:Ct,target$:ht})),...ae("sidebar").filter(e=>e.getAttribute("data-md-type")==="navigation").map(e=>so(e,{viewport$:xe,header$:Xe,main$:Ct,target$:ht})),...ae("top").map(e=>Si(e,{viewport$:xe,header$:Xe,main$:Ct,target$:ht})))),ki=nt.pipe(x(()=>As),Ye(_s),ee(1));ki.subscribe();window.document$=nt;window.location$=Nt;window.target$=ht;window.keyboard$=co;window.viewport$=xe;window.tablet$=gr;window.screen$=Ci;window.print$=Hi;window.alert$=lo;window.progress$=po;window.component$=ki;})();
