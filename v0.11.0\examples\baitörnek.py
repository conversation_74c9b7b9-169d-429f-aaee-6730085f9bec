import pandas as pd
import numpy as np
from pyvrp import Model
from pyvrp.stop import MaxRuntime
import matplotlib.pyplot as plt
from pyvrp.plotting import plot_coordinates, plot_solution

# ======================================================
# 1. EXCEL VERİLERİNİ YÜKLEME
# ======================================================
excel_file = r"C:\\Users\\<USER>\\OneDrive - Horoz Lojistik\\_slms_\\PyVRP\\v0.11.0\\examples\\vrp_dataorj.xlsx"

# Lokasyon bilgilerini yükle
df_locations = pd.read_excel(excel_file, sheet_name='Locations')

# Mesafe matrisini yükle
df_distances = pd.read_excel(
    excel_file, 
    sheet_name='DistanceMatrix',
    index_col=0
)

# ======================================================
# 2. VERİLERİ HAZIRLAMA
# ======================================================
# Koordinatlar ve talepler
COORDS = list(zip(df_locations['x'], df_locations['y']))
DEMANDS = df_locations['demand'].tolist()

# Mesafe matrisini numpy array'e çevir
distance_matrix = df_distances.values.astype(int)

# ======================================================
# 3. VRP MODELİNİ OLUŞTURMA (DÜZENLENMİŞ)
# ======================================================
m = Model()

# Araç tipini ekle (4 araç, kapasite=15)
vehicle_type = m.add_vehicle_type(4, capacity=15)

# Depo (ilk lokasyon)
depot = m.add_depot(x=COORDS[0][0], y=COORDS[0][1])

# Müşterileri ekle (daha güvenli yöntem)
clients = []
for i in range(1, len(COORDS)):
    clients.append(
        m.add_client(
            x=COORDS[i][0], 
            y=COORDS[i][1], 
            delivery=DEMANDS[i]
        )
    )

# Mesafe matrisini kullanarak kenarları ekle
n = len(COORDS)
for i in range(n):
    for j in range(n):
        if i != j:  # Kendi kendine mesafe ekleme
            dist = distance_matrix[i][j]
            m.add_edge(
                m.locations(i), 
                m.locations(j), 
                distance=dist
            )

# ======================================================
# 4. LOKASYONLARI GÖRSELLEŞTİRME
# ======================================================
_, ax = plt.subplots(figsize=(10, 10))
plot_coordinates(m.data(), ax=ax)
plt.title("Lokasyonların Dağılımı")
plt.savefig('locations.png')
plt.close()

# ======================================================
# 5. PROBLEMİ ÇÖZME
# ======================================================
print("VRP çözülüyor...")
res = m.solve(stop=MaxRuntime(10), display=True, seed=42)  # 10 saniye, sabit seed
print("\nÇözüm tamamlandı!")

# Sonuçları yazdır
print(res)

# ======================================================
# 6. ÇÖZÜMÜ GÖRSELLEŞTİRME
# ======================================================
_, ax = plt.subplots(figsize=(12, 12))
plot_solution(res.best, m.data(), ax=ax)
plt.title("Optimize Edilmiş Rotalar")
plt.savefig('routes.png')
plt.close()

# ======================================================
# 7. ROTA DETAYLARINI GÖSTERME (GÜVENLİ YÖNTEM)
# ======================================================
def get_client_info(client_id):
    """Güvenli müşteri bilgisi alımı"""
    if client_id == 0:  # Depot
        return "Depot", 0
    return f"L{client_id}", DEMANDS[client_id]

print("\nARAÇ ROTALARI:")
for idx, route in enumerate(res.best.routes(), 1):
    print(f"\nAraç {idx} rotası:")
    total_distance = 0
    total_delivery = 0
    prev_locations = depot
    
    # Rota üzerindeki her ziyaret noktası için
    for visit in route.visits():
        client_id = visit.client
        loc_name, demand = get_client_info(client_id)
        print(f"{loc_name}(Talep:{demand}) → ", end="")
        total_delivery += demand
        
        # Mesafe hesapla (önceki konumdan şimdikine)
        if prev_locations:
            dist = distance_matrix[prev_locations.id][client_id]
            total_distance += dist
        prev_locations = m.locations(client_id)
    
    # Depoya dönüş mesafesi
    dist_to_depot = distance_matrix[prev_locations.id][0]
    total_distance += dist_to_depot
    
    print("Depot")
    print(f"Toplam yol mesafesi: {total_distance:.0f} birim")
    print(f"Toplam taşınan yük: {total_delivery} birim")

# ======================================================
# 8. ÇÖZÜMÜ EXCEL'E KAYDETME
# ======================================================
solution_data = []
for idx, route in enumerate(res.best.routes(), 1):
    route_path = ["Depot"]
    for visit in route.visits():
        client_id = visit.client
        loc_name, _ = get_client_info(client_id)
        route_path.append(loc_name)
    route_path.append("Depot")
    
    solution_data.append({
        "Araç No": idx,
        "Rota": " → ".join(route_path),
        "Toplam Mesafe": total_distance,
        "Toplam Yük": total_delivery
    })

df_solution = pd.DataFrame(solution_data)
df_solution.to_excel('vrp_solution.xlsx', index=False)
print("\nÇözüm 'vrp_solution.xlsx' dosyasına kaydedildi.")