


<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
      
      
      
        <link rel="prev" href="benchmarking.html">
      
      
        <link rel="next" href="supported_vrplib_fields.html">
      
      
      <link rel="icon" href="">
    
    
      
        <title>Contributing - PyVRP 0.11.0 documentation</title>
      
    
    
      
        
      
      


    
    
      
    
    
      
        
        
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
        <link rel="stylesheet" type="text/css" href="../_static/sphinx_immaterial_theme.96fe8683ff2bd71e9.min.css?v=13adf062" />
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="orange" data-md-color-accent="yellow">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#setting-up-a-local-installation" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
      <div data-md-color-scheme="default" data-md-component="outdated" hidden>
        
      </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../index.html" title="PyVRP 0.11.0 documentation" class="md-header__button md-logo" aria-label="PyVRP 0.11.0 documentation" data-md-component="logo">
      <img src="../_static/icon.svg" alt="logo">
    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            PyVRP 0.11.0 documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Contributing
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme="default" data-md-color-primary="orange" data-md-color-accent="yellow"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a7 7 0 0 1 7 7c0 2.38-1.19 4.47-3 5.74V17a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1v-2.26C6.19 13.47 5 11.38 5 9a7 7 0 0 1 7-7M9 21v-1h6v1a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1m3-17a5 5 0 0 0-5 5c0 2.05 1.23 3.81 3 4.58V16h4v-2.42c1.77-.77 3-2.53 3-4.58a5 5 0 0 0-5-5"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme="slate" data-md-color-primary="orange" data-md-color-accent="yellow"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a7 7 0 0 0-7 7c0 2.38 1.19 4.47 3 5.74V17a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-2.26c1.81-1.27 3-3.36 3-5.74a7 7 0 0 0-7-7M9 21a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-1H9z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
      <div class="md-header__source">
        <a href="https://github.com/PyVRP/PyVRP/" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
  </div>
  <div class="md-source__repository">
    PyVRP
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../index.html" title="PyVRP 0.11.0 documentation" class="md-nav__button md-logo" aria-label="PyVRP 0.11.0 documentation" data-md-component="logo">
      <img src="../_static/icon.svg" alt="logo">
    </a>
    PyVRP 0.11.0 documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/PyVRP/PyVRP/" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
  </div>
  <div class="md-source__repository">
    PyVRP
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_1" >
        
          
          <label class="md-nav__link" for="__nav_1" id="__nav_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_1">
            <span class="md-nav__icon md-icon"></span>
            Getting started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/introduction_to_vrp.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/introduction_to_vrp.rst (reference label)">A brief introduction to VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/introduction_to_hgs.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/introduction_to_hgs.rst (reference label)">A brief introduction to HGS</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/installation.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/installation.rst (reference label)">Installation instructions</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/getting_help.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/getting_help.rst (reference label)">Getting help</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/faq.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/faq.rst (reference label)">FAQ</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/concepts.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/concepts.rst (reference label)">Concepts</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/benchmarks.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/benchmarks.rst (reference label)">Benchmarks</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/citing.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/citing.rst (reference label)">Citing Py<wbr>VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Examples
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Examples
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../examples/quick_tutorial.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/quick_tutorial.ipynb (reference label)">A quick tutorial</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../examples/basic_vrps.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/basic_vrps.ipynb (reference label)">Classic VRPs</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../examples/using_pyvrp_components.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/using_pyvrp_components.ipynb (reference label)">Using Py<wbr>VRP’s components</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    API reference
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            API reference
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/pyvrp.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/pyvrp.rst (reference label)">Py<wbr>VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/crossover.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/crossover.rst (reference label)">Crossover operators</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/diversity.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/diversity.rst (reference label)">Diversity measures</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/repair.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/repair.rst (reference label)">Repair operators</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/search.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/search.rst (reference label)">Search methods</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/plotting.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/plotting.rst (reference label)">Plotting tools</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/stop.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/stop.rst (reference label)">Stopping criteria</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
    
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" checked>
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Developing Py<wbr>VRP
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Developing Py<wbr>VRP
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="benchmarking.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/benchmarking.rst (reference label)">Benchmarking</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    <span title="/dev/contributing.rst (reference label)">Contributing</span>
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="#" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/contributing.rst (reference label)">Contributing</span>
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#setting-up-a-local-installation" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#setting-up-a-local-installation (reference label)">Setting up a local installation</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#setting-up-github-codespaces" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#setting-up-github-codespaces (reference label)">Setting up Github Codespaces</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#building-the-extensions" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#building-the-extensions (reference label)">Building the extensions</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-the-extensions" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#debugging-the-extensions (reference label)">Debugging the extensions</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#profiling-the-extensions" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#profiling-the-extensions (reference label)">Profiling the extensions</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#committing-changes" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#committing-changes (reference label)">Committing changes</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#licensing" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#licensing (reference label)">Licensing</span>
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="supported_vrplib_fields.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/supported_vrplib_fields.rst (reference label)">The VRPLIB format</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="new_vrp_variants.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/new_vrp_variants.rst (reference label)">Supporting new VRP variants</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="glossary.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/glossary.rst (reference label)">Glossary</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary">
  
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#setting-up-a-local-installation" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#setting-up-a-local-installation (reference label)">Setting up a local installation</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#setting-up-github-codespaces" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#setting-up-github-codespaces (reference label)">Setting up Github Codespaces</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#building-the-extensions" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#building-the-extensions (reference label)">Building the extensions</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-the-extensions" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#debugging-the-extensions (reference label)">Debugging the extensions</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#profiling-the-extensions" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#profiling-the-extensions (reference label)">Profiling the extensions</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#committing-changes" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#committing-changes (reference label)">Committing changes</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#licensing" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/dev/contributing.rst#licensing (reference label)">Licensing</span>
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset" role="main">
                
                
                  


<h1 id="contributing">Contributing<a class="headerlink" href="#contributing" title="Link to this heading">¶</a></h1>
<p>Conversations about development and issues take place in the <a class="reference external" href="https://github.com/PyVRP/PyVRP/">GitHub repository</a>.
Feel free to open a new issue if you have something to discuss.</p>
<h2 id="setting-up-a-local-installation">Setting up a local installation<a class="headerlink" href="#setting-up-a-local-installation" title="Link to this heading">¶</a></h2>
<p>Make sure you have a reasonably modern C++ compiler.
Any recent version that supports (most of) the C++20 standard should do.
Once you have a compiler installed, you can proceed by forking the PyVRP repository from the <a class="reference external" href="https://github.com/PyVRP/PyVRP/fork">GitHub website</a>.
Then, clone your new fork to some local environment:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/&lt;your<span class="w"> </span>username&gt;/PyVRP.git
</code></pre></div>
</div>
<p>Now, change into the PyVRP directory, and set-up the virtual environment using <code class="docutils literal notranslate"><span class="pre">poetry</span></code>:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code><span class="nb">cd</span><span class="w"> </span>PyVRP

pip<span class="w"> </span>install<span class="w"> </span>--upgrade<span class="w"> </span>poetry
poetry<span class="w"> </span>install<span class="w"> </span>--with<span class="w"> </span>examples,docs,dev
</code></pre></div>
</div>
<p>This might take a few minutes, but only needs to be done once.
Now make sure everything runs smoothly, by executing the test suite:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code>poetry<span class="w"> </span>run<span class="w"> </span>pytest
</code></pre></div>
</div>
<div class="note admonition">
<p class="admonition-title">Note</p>
<p>If you use <a class="reference external" href="https://pre-commit.com/">pre-commit</a>, you can use our pre-commit configuration file to set that up too.
Simply type:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code>pre-commit<span class="w"> </span>install
</code></pre></div>
</div>
<p>After this completes, style and typing issues are automatically checked whenever you make a new commit to your feature branch.</p>
</div>
<h2 id="setting-up-github-codespaces">Setting up Github Codespaces<a class="headerlink" href="#setting-up-github-codespaces" title="Link to this heading">¶</a></h2>
<p>If you are having trouble building PyVRP from source or setting up your local development environment, you can try to build PyVRP online, using <a class="reference external" href="https://docs.github.com/en/codespaces">GitHub Codespaces</a>.
Github Codespaces allows you to create a development environment directly in your browser.</p>
<p>To launch Github Codespaces, go to the <a class="reference external" href="https://github.com/PyVRP/PyVRP">PyVRP repository</a> and click on the green button.
Select the Codespaces tab and click on the <cite>+</cite> icon to create a Codespaces environment.
This environment is configured with all necessary dependencies to build PyVRP.
Once the setup completes, execute the test suite to verify everything runs smoothly:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code>poetry<span class="w"> </span>run<span class="w"> </span>pytest
</code></pre></div>
</div>
<h2 id="building-the-extensions">Building the extensions<a class="headerlink" href="#building-the-extensions" title="Link to this heading">¶</a></h2>
<p>PyVRP uses a number of native Python extensions that are written in C++ for performance.
These extensions are built every time <code class="docutils literal notranslate"><span class="pre">poetry</span> <span class="pre">install</span></code> is used, but that command builds everything in release mode.
While developing, one typically wants to use debug builds.
These (and more) can be made by using the <code class="docutils literal notranslate"><span class="pre">build_extensions.py</span></code> script directly, as follows:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code>poetry<span class="w"> </span>run<span class="w"> </span>python<span class="w"> </span>build_extensions.py
</code></pre></div>
</div>
<p>The script takes a number of command-line arguments, which you can discover using</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code>poetry<span class="w"> </span>run<span class="w"> </span>python<span class="w"> </span>build_extensions.py<span class="w"> </span>--help
</code></pre></div>
</div>
<p>We use the Meson build system to compile the C++ extensions.
Meson is configured using the <code class="docutils literal notranslate"><span class="pre">meson.build</span></code> file in the repository root.
You should not have to touch this file often: all compilation is handled via the <code class="docutils literal notranslate"><span class="pre">build_extensions.py</span></code> script.</p>
<h2 id="debugging-the-extensions">Debugging the extensions<a class="headerlink" href="#debugging-the-extensions" title="Link to this heading">¶</a></h2>
<p>This section explains how to perform cross-debugging for mixed Python and C++ code.
We will use the <a class="reference external" href="https://code.visualstudio.com/">Visual Studio Code</a> IDE and the <a class="reference external" href="https://github.com/benibenj/vscode-pythonCpp">Python C++ Debug</a> extension.</p>
<p>First, build PyVRP in debug mode:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code>poetry<span class="w"> </span>run<span class="w"> </span>python<span class="w"> </span>build_extensions.py<span class="w"> </span>--build_type<span class="w"> </span>debug
</code></pre></div>
</div>
<p>Create a test Python file that calls some C++ code, like so:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp</span><span class="w"> </span><span class="kn">import</span> <span class="n">Client</span>

<span class="n">Client</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
</code></pre></div>
</div>
<p>Set breakpoints in <code class="docutils literal notranslate"><span class="pre">pyvrp/cpp/ProblemData.cpp</span></code> within the <code class="docutils literal notranslate"><span class="pre">Client</span></code> constructor.
Next, set-up your debugger configuration by creating the <code class="docutils literal notranslate"><span class="pre">.vscode/launch.json</span></code> file, with the following content:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;0.2.0&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;configurations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Python C++ Debugger&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;pythoncpp&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;request&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;launch&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;pythonConfig&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;default&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;cppConfig&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;default (gdb) Attach&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="p">}</span>
</code></pre></div>
</div>
<p>Start the debugger in Visual Studio Code and step through the code.
The debugger should break at the breakpoints that you set in <code class="docutils literal notranslate"><span class="pre">pvvrp/cpp/ProblemData.cpp</span></code>.</p>
<h2 id="profiling-the-extensions">Profiling the extensions<a class="headerlink" href="#profiling-the-extensions" title="Link to this heading">¶</a></h2>
<p>Typically, the most computationally intense components in PyVRP are written in C++, as native extensions.
While developing new functionality that touches the C++ components, it is important to pay attention to performance.
For this, profiling is an incredibly useful tool.
There are many ways to get started with profiling, but the following may be helpful.</p>
<p>First, build a debug optimised build of PyVRP, as follows:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code>poetry<span class="w"> </span>run<span class="w"> </span>python<span class="w"> </span>build_extensions.py<span class="w"> </span>--build_type<span class="w"> </span>debugoptimized
</code></pre></div>
</div>
<p>This ensures all debug symbols are retained, so the profiling output contains meaningful information.
Next, we need to use a profiling tool, which varies based on your operating system.</p>
<div class="tabbed-set tabbed-alternate" data-tabs="1:2">
<input checked type="radio" id="__tabbed_1_1" name="__tabbed_1"><input type="radio" id="__tabbed_1_2" name="__tabbed_1"><div class="tabbed-labels docutils container">
<label class="tabbed-label" for="__tabbed_1_1">
Linux</label><label class="tabbed-label" for="__tabbed_1_2">
macOS</label></div>
<div class="tabbed-content docutils container">
<div class="tabbed-block docutils container">
<p>Make sure you install <code class="docutils literal notranslate"><span class="pre">perf</span></code>, the Linux profiling tool.
Now, all we need to do is let <code class="docutils literal notranslate"><span class="pre">perf</span></code> record PyVRP doing some work, like for example:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code>poetry<span class="w"> </span>run<span class="w"> </span>perf<span class="w"> </span>record<span class="w"> </span>pyvrp<span class="w"> </span>instances/VRPTW/RC2_10_5.vrp<span class="w"> </span>--seed<span class="w"> </span><span class="m">6</span><span class="w"> </span>--round_func<span class="w"> </span>dimacs<span class="w"> </span>--max_runtime<span class="w"> </span><span class="m">5</span>
</code></pre></div>
</div>
<p>The resulting <code class="docutils literal notranslate"><span class="pre">perf.data</span></code> file will contain all relevant profiling results.
Such a file can be inspected using <code class="docutils literal notranslate"><span class="pre">perf</span></code> on the command line, or with a GUI using, for example, KDAB’s <a class="reference external" href="https://github.com/KDAB/hotspot">hotspot</a> program.</p>
</div>
<div class="tabbed-block docutils container">
<p>macOS comes with a profiling tool named Instruments, which is bundled inside Apple’s <a class="reference external" href="https://developer.apple.com/xcode/">Xcode</a>.
First, make sure you have Xcode installed.
Now, run PyVRP for a period of time long enough that we can attach to the corresponding process, like for example:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><code>poetry<span class="w"> </span>run<span class="w"> </span>pyvrp<span class="w"> </span>instances/VRPTW/RC2_10_5.vrp<span class="w"> </span>--seed<span class="w"> </span><span class="m">6</span><span class="w"> </span>--round_func<span class="w"> </span>dimacs<span class="w"> </span>--max_runtime<span class="w"> </span><span class="m">60</span>
</code></pre></div>
</div>
<p>Next, open the Instruments application.
Select the “CPU Profiler” template, click on the search bar at the top of the window, and select the corresponding Python process as your target, which is usually the most recent one.
Start profiling by clicking on the red circle in the top-left corner.
Once you are ready, you can stop the profiling and analyze the results.</p>
</div>
</div>
</div><h2 id="committing-changes">Committing changes<a class="headerlink" href="#committing-changes" title="Link to this heading">¶</a></h2>
<p>We use pull requests to develop PyVRP.
For a pull request to be accepted, you must meet the below requirements.
This greatly reduces the job of maintaining and releasing the software.</p>
<ul class="simple">
<li><p><strong>One branch. One feature.</strong>
Branches are cheap and GitHub makes it easy to merge and delete branches with a few clicks.
Avoid the temptation to lump in a bunch of unrelated changes when working on a feature, if possible.
This helps us keep track of what has changed when preparing a release.</p></li>
<li><p>Commit messages should be clear and concise.
This means a subject line of less than 80 characters, and, if necessary, a blank line followed by a commit message body.</p></li>
<li><p>Code submissions should always include tests.</p></li>
<li><p>Each function, class, method, and attribute needs to be documented using docstrings.
We conform to the <a class="reference external" href="https://numpydoc.readthedocs.io/en/latest/format.html#docstring-standard">NumPy docstring standard</a>.</p></li>
<li><p>If you are adding new functionality, you need to add it to the documentation by editing (or creating) the appropriate file in <code class="docutils literal notranslate"><span class="pre">docs/source/</span></code>.</p></li>
<li><p>Make sure your documentation changes parse correctly.
See the documentation in the <code class="docutils literal notranslate"><span class="pre">docs/</span></code> directory for details on how to build the documentation locally.</p></li>
</ul>
<div class="note admonition">
<p class="admonition-title">Note</p>
<p>Please use the “Pull request” template on GitHub when opening a pull request.</p>
</div>
<h2 id="licensing">Licensing<a class="headerlink" href="#licensing" title="Link to this heading">¶</a></h2>
<p>PyVRP is licensed under the MIT license.
All code, documentation and other files added to PyVRP by contributors is licensed under this license, unless another license is explicitly specified in the source file.
For your contribution, please check that it can be included into PyVRP under the MIT license.
If you did not write the code yourself, you must ensure that the existing license is compatible and include the license information in the contributed files, or obtain permission from the original author to relicense the contributed code.
Contributors keep the copyright for code they wrote and submit for inclusion to PyVRP.</p>







  
  






                
              </article>
            </div>
          
          
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  
  
  <div class="md-footer-meta md-typeset">
    
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-footer-copyright__highlight">
        &#169; Copyright 2022 - 2025, PyVRP contributors.
        
    </div>
  
    Created using
    <a href="https://www.sphinx-doc.org/" target="_blank" rel="noopener">Sphinx</a>
    8.1.3.
     and
    <a href="https://github.com/jbms/sphinx-immaterial/" target="_blank" rel="noopener">Sphinx-Immaterial</a>
  
</div>
      
    </div>
    
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    <script id="__config" type="application/json">{"base": "..", "features": ["navigation.expand", "navigation.top"], "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike", "staticVersions": [{"aliases": [], "title": "Development", "version": ""}, {"aliases": [], "title": "v0.10.0", "version": "https://pyvrp.github.io/v0.10.0"}, {"aliases": [], "title": "v0.9.0", "version": "https://pyvrp.github.io/v0.9.0"}, {"aliases": [], "title": "v0.8.1", "version": "https://pyvrp.github.io/v0.8.1"}, {"aliases": [], "title": "v0.7.0", "version": "https://pyvrp.github.io/v0.7.0"}, {"aliases": [], "title": "v0.6.0", "version": "https://pyvrp.github.io/v0.6.0"}, {"aliases": [], "title": "v0.5.0", "version": "https://pyvrp.github.io/v0.5.0"}, {"aliases": [], "title": "v0.4.4", "version": "https://pyvrp.github.io/v0.4.4"}, {"aliases": [], "title": "v0.3.0", "version": "https://pyvrp.github.io/v0.3.0"}, {"aliases": [], "title": "v0.2.1", "version": "https://pyvrp.github.io/v0.2.1"}, {"aliases": [], "title": "v0.1.0", "version": "https://pyvrp.github.io/v0.1.0"}], "versionPath": null}}</script>
    
      
        <script src="../_static/sphinx_immaterial_theme.32136f45f91ae6956.min.js?v=a7a9472a"></script>
        <script crossorigin="anonymous" integrity="sha256-Ae2Vz/4ePdIu6ZyI/5ZGsYnb+m0JlOmKPjt6XZ9JJkA=" src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.4/require.min.js"></script>
    
  </body>
</html>