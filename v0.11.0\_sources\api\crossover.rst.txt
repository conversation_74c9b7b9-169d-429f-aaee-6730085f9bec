.. module:: pyvrp.crossover
   :synopsis: Crossover operators


Crossover operators
===================

The :mod:`pyvrp.crossover` module provides operators that are responsible for generating a new :class:`~pyvrp._pyvrp.Solution` offspring from two parent solutions.

.. automodule:: pyvrp.crossover.ordered_crossover

   .. autofunction:: ordered_crossover

.. automodule:: pyvrp.crossover.selective_route_exchange

   .. autofunction:: selective_route_exchange
