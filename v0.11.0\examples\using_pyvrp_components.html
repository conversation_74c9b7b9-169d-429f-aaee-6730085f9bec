


<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
      
      
      
        <link rel="prev" href="basic_vrps.html">
      
      
        <link rel="next" href="../api/pyvrp.html">
      
      
      <link rel="icon" href="">
    
    
      
        <title>Using PyVRP’s components - PyVRP 0.11.0 documentation</title>
      
    
    
      
        
      
      


    
    
      
    
    
      
        
        
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
        <link rel="stylesheet" type="text/css" href="../_static/sphinx_immaterial_theme.96fe8683ff2bd71e9.min.css?v=13adf062" />
        <link rel="stylesheet" type="text/css" href="../_static/nbsphinx-code-cells.css?v=2aa19091" />
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="orange" data-md-color-accent="yellow">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#A-tour-of-PyVRP" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
      <div data-md-color-scheme="default" data-md-component="outdated" hidden>
        
      </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../index.html" title="PyVRP 0.11.0 documentation" class="md-header__button md-logo" aria-label="PyVRP 0.11.0 documentation" data-md-component="logo">
      <img src="../_static/icon.svg" alt="logo">
    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            PyVRP 0.11.0 documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Using PyVRP’s components
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme="default" data-md-color-primary="orange" data-md-color-accent="yellow"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a7 7 0 0 1 7 7c0 2.38-1.19 4.47-3 5.74V17a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1v-2.26C6.19 13.47 5 11.38 5 9a7 7 0 0 1 7-7M9 21v-1h6v1a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1m3-17a5 5 0 0 0-5 5c0 2.05 1.23 3.81 3 4.58V16h4v-2.42c1.77-.77 3-2.53 3-4.58a5 5 0 0 0-5-5"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme="slate" data-md-color-primary="orange" data-md-color-accent="yellow"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a7 7 0 0 0-7 7c0 2.38 1.19 4.47 3 5.74V17a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-2.26c1.81-1.27 3-3.36 3-5.74a7 7 0 0 0-7-7M9 21a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-1H9z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
      <div class="md-header__source">
        <a href="https://github.com/PyVRP/PyVRP/" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
  </div>
  <div class="md-source__repository">
    PyVRP
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../index.html" title="PyVRP 0.11.0 documentation" class="md-nav__button md-logo" aria-label="PyVRP 0.11.0 documentation" data-md-component="logo">
      <img src="../_static/icon.svg" alt="logo">
    </a>
    PyVRP 0.11.0 documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/PyVRP/PyVRP/" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
  </div>
  <div class="md-source__repository">
    PyVRP
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_1" >
        
          
          <label class="md-nav__link" for="__nav_1" id="__nav_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_1">
            <span class="md-nav__icon md-icon"></span>
            Getting started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/introduction_to_vrp.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/introduction_to_vrp.rst (reference label)">A brief introduction to VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/introduction_to_hgs.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/introduction_to_hgs.rst (reference label)">A brief introduction to HGS</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/installation.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/installation.rst (reference label)">Installation instructions</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/getting_help.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/getting_help.rst (reference label)">Getting help</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/faq.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/faq.rst (reference label)">FAQ</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/concepts.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/concepts.rst (reference label)">Concepts</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/benchmarks.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/benchmarks.rst (reference label)">Benchmarks</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/citing.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/citing.rst (reference label)">Citing Py<wbr>VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
    
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2" checked>
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Examples
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Examples
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="quick_tutorial.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/quick_tutorial.ipynb (reference label)">A quick tutorial</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="basic_vrps.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/basic_vrps.ipynb (reference label)">Classic VRPs</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    <span title="/examples/using_pyvrp_components.ipynb (reference label)">Using Py<wbr>VRP’s components</span>
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="#" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/using_pyvrp_components.ipynb (reference label)">Using Py<wbr>VRP’s components</span>
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#A-tour-of-PyVRP" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#a-tour-of-pyvrp (reference label)">A tour of Py<wbr>VRP</span>
    </span>
  </a>
  
    <nav class="md-nav" aria-label="A tour of PyVRP">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#Random-number-generator" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#random-number-generator (reference label)">Random number generator</span>
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#Search-method" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#search-method (reference label)">Search method</span>
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#Solution-representation-and-evaluation" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#solution-representation-and-evaluation (reference label)">Solution representation and evaluation</span>
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#Population-management" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#population-management (reference label)">Population management</span>
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#The-genetic-algorithm-and-crossover" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#the-genetic-algorithm-and-crossover (reference label)">The genetic algorithm and crossover</span>
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#The-solve-function" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#the-solve-function (reference label)">The solve function</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#Conclusion" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#conclusion (reference label)">Conclusion</span>
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    API reference
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            API reference
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/pyvrp.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/pyvrp.rst (reference label)">Py<wbr>VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/crossover.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/crossover.rst (reference label)">Crossover operators</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/diversity.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/diversity.rst (reference label)">Diversity measures</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/repair.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/repair.rst (reference label)">Repair operators</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/search.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/search.rst (reference label)">Search methods</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/plotting.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/plotting.rst (reference label)">Plotting tools</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/stop.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/stop.rst (reference label)">Stopping criteria</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Developing Py<wbr>VRP
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Developing Py<wbr>VRP
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/benchmarking.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/benchmarking.rst (reference label)">Benchmarking</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/contributing.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/contributing.rst (reference label)">Contributing</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/supported_vrplib_fields.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/supported_vrplib_fields.rst (reference label)">The VRPLIB format</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/new_vrp_variants.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/new_vrp_variants.rst (reference label)">Supporting new VRP variants</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/glossary.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/glossary.rst (reference label)">Glossary</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary">
  
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#A-tour-of-PyVRP" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#a-tour-of-pyvrp (reference label)">A tour of Py<wbr>VRP</span>
    </span>
  </a>
  
    <nav class="md-nav" aria-label="A tour of PyVRP">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#Random-number-generator" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#random-number-generator (reference label)">Random number generator</span>
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#Search-method" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#search-method (reference label)">Search method</span>
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#Solution-representation-and-evaluation" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#solution-representation-and-evaluation (reference label)">Solution representation and evaluation</span>
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#Population-management" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#population-management (reference label)">Population management</span>
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#The-genetic-algorithm-and-crossover" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#the-genetic-algorithm-and-crossover (reference label)">The genetic algorithm and crossover</span>
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#The-solve-function" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#the-solve-function (reference label)">The solve function</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#Conclusion" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/using_pyvrp_components.ipynb#conclusion (reference label)">Conclusion</span>
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset" role="main">
                
                
                  


<h1 id="Using-PyVRP's-components">Using PyVRP’s components<a class="headerlink" href="#Using-PyVRP's-components" title="Link to this heading">¶</a></h1>
<p>We have relied on the <code class="docutils literal notranslate"><span class="pre">Model</span></code> interface to solve VRP instances in the examples so far. That high-level interface hides a lot of the components that available in PyVRP, which uses <a class="reference external" href="https://pyvrp.org/setup/introduction_to_hgs.html">a hybrid genetic search algorithm</a> under the hood. In this notebook we will investigate these components in more detail to build our own <code class="docutils literal notranslate"><span class="pre">solve</span></code> function based on hybrid genetic search.</p>
<p>Along the way we will solve the <code class="docutils literal notranslate"><span class="pre">RC208.vrp</span></code> instance, one of the well-known VRPTW benchmark instances of Solomon. This instance consists of 100 clients.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[1]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp</span><span class="w"> </span><span class="kn">import</span> <span class="n">read</span>

<span class="n">INSTANCE</span> <span class="o">=</span> <span class="n">read</span><span class="p">(</span><span class="s2">&quot;data/RC208.vrp&quot;</span><span class="p">,</span> <span class="s2">&quot;dimacs&quot;</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<p>We will implement a <code class="docutils literal notranslate"><span class="pre">solve()</span></code> function that will take a <code class="docutils literal notranslate"><span class="pre">stop</span></code> stopping criterion, and a <code class="docutils literal notranslate"><span class="pre">seed</span></code> for the random number generator. This definition is very close to that of <code class="docutils literal notranslate"><span class="pre">Model.solve</span></code>. The signature is</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">solve</span><span class="p">(</span><span class="n">stop</span><span class="p">:</span> <span class="n">StoppingCriterion</span><span class="p">,</span> <span class="n">seed</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Solution</span><span class="p">:</span> <span class="o">...</span>
</code></pre></div>
</div>
<h2 id="A-tour-of-PyVRP">A tour of PyVRP<a class="headerlink" href="#A-tour-of-PyVRP" title="Link to this heading">¶</a></h2>
<p>We need to understand the separate components in PyVRP before we are ready to implement this function. PyVRP uses a hybrid genetic search algorithm under the hood. The <code class="docutils literal notranslate"><span class="pre">GeneticAlgorithm</span></code> object manages a population of solutions. In each iteration, two solutions are selected from this population for <em>crossover</em> using a crossover operator from <code class="docutils literal notranslate"><span class="pre">pyvrp.crossover</span></code>, which generates a new offspring solution. That offspring solution is then improved using a method from <code class="docutils literal notranslate"><span class="pre">pyvrp.search</span></code>. The improved
offspring solution is then added to the population. This process continues until a stopping condition is reached (see <code class="docutils literal notranslate"><span class="pre">pyvrp.stop</span></code> for different conditions). Let’s have a look at the different parts of <code class="docutils literal notranslate"><span class="pre">pyvrp</span></code> that implement this algorithm.</p>
<p>To instantiate the <code class="docutils literal notranslate"><span class="pre">GeneticAlgorithm</span></code>, we first need to specify an (initial) population, search method, penalty manager and random number generator. Let’s start with the random number generator because it is the easiest to set up.</p>
<h3 id="Random-number-generator">Random number generator<a class="headerlink" href="#Random-number-generator" title="Link to this heading">¶</a></h3>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[2]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp</span><span class="w"> </span><span class="kn">import</span> <span class="n">RandomNumberGenerator</span>

<span class="n">rng</span> <span class="o">=</span> <span class="n">RandomNumberGenerator</span><span class="p">(</span><span class="n">seed</span><span class="o">=</span><span class="mi">42</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<h3 id="Search-method">Search method<a class="headerlink" href="#Search-method" title="Link to this heading">¶</a></h3>
<p>Let’s now define the search method. PyVRP currently implements a <code class="docutils literal notranslate"><span class="pre">LocalSearch</span></code> method that is very customisable with different operators and search neighbourhoods. Different operators search different parts of the solution space, which can be beneficial in finding better solutions. The neighbourhood defines which edges are evaluated. By restricting that set of edges the local search method works much faster.</p>
<p>We provide default operator sets and neighbourhoods, which can be used as follows.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[3]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp.search</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">LocalSearch</span><span class="p">,</span>
    <span class="n">NODE_OPERATORS</span><span class="p">,</span>
    <span class="n">ROUTE_OPERATORS</span><span class="p">,</span>
    <span class="n">compute_neighbours</span><span class="p">,</span>
<span class="p">)</span>

<span class="n">neighbours</span> <span class="o">=</span> <span class="n">compute_neighbours</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">)</span>
<span class="n">ls</span> <span class="o">=</span> <span class="n">LocalSearch</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">,</span> <span class="n">rng</span><span class="p">,</span> <span class="n">neighbours</span><span class="p">)</span>

<span class="k">for</span> <span class="n">node_op</span> <span class="ow">in</span> <span class="n">NODE_OPERATORS</span><span class="p">:</span>
    <span class="n">ls</span><span class="o">.</span><span class="n">add_node_operator</span><span class="p">(</span><span class="n">node_op</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">))</span>

<span class="k">for</span> <span class="n">route_op</span> <span class="ow">in</span> <span class="n">ROUTE_OPERATORS</span><span class="p">:</span>
    <span class="n">ls</span><span class="o">.</span><span class="n">add_route_operator</span><span class="p">(</span><span class="n">route_op</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">))</span>
</code></pre></div>
</div>
</div>
<h3 id="Solution-representation-and-evaluation">Solution representation and evaluation<a class="headerlink" href="#Solution-representation-and-evaluation" title="Link to this heading">¶</a></h3>
<p>We now have a functioning local search method. All we need are two additional components to make it work: a <code class="docutils literal notranslate"><span class="pre">Solution</span></code> that described a set of routes, and a <code class="docutils literal notranslate"><span class="pre">CostEvaluator</span></code> that can be used to evaluate different moves. Let’s define those.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[4]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp</span><span class="w"> </span><span class="kn">import</span> <span class="n">Solution</span><span class="p">,</span> <span class="n">CostEvaluator</span>

<span class="n">cost_evaluator</span> <span class="o">=</span> <span class="n">CostEvaluator</span><span class="p">(</span>
    <span class="n">load_penalties</span><span class="o">=</span><span class="p">[</span><span class="mi">20</span><span class="p">],</span>
    <span class="n">tw_penalty</span><span class="o">=</span><span class="mi">20</span><span class="p">,</span>
    <span class="n">dist_penalty</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
<span class="p">)</span>
<span class="n">sol</span> <span class="o">=</span> <span class="n">Solution</span><span class="o">.</span><span class="n">make_random</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">,</span> <span class="n">rng</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<p>The random solution <code class="docutils literal notranslate"><span class="pre">sol</span></code> that we created just yet is not feasible. This is not a problem, because PyVRP internally uses penalties to evaluate infeasibilities in each solution. This is done using the <code class="docutils literal notranslate"><span class="pre">CostEvaluator</span></code>’s <code class="docutils literal notranslate"><span class="pre">penalised_cost</span></code> function, which allows us to determine the quality of infeasible solutions as well.</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[5]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="k">assert</span> <span class="ow">not</span> <span class="n">sol</span><span class="o">.</span><span class="n">is_feasible</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="n">cost_evaluator</span><span class="o">.</span><span class="n">penalised_cost</span><span class="p">(</span><span class="n">sol</span><span class="p">))</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
79611
</pre></div></div>
</div>
<p>Let’s see if the local search can improve this solution further.</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[6]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">new_sol</span> <span class="o">=</span> <span class="n">ls</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="n">sol</span><span class="p">,</span> <span class="n">cost_evaluator</span><span class="p">)</span>

<span class="k">assert</span> <span class="ow">not</span> <span class="n">sol</span><span class="o">.</span><span class="n">is_feasible</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="n">cost_evaluator</span><span class="o">.</span><span class="n">penalised_cost</span><span class="p">(</span><span class="n">new_sol</span><span class="p">))</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
8565
</pre></div></div>
</div>
<p>Much better! But the new solution is not yet feasible. Can we hammer out the infeasibilities by increasing the penalties?</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[7]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">cost_evaluator</span> <span class="o">=</span> <span class="n">CostEvaluator</span><span class="p">([</span><span class="mi">200</span><span class="p">],</span> <span class="mi">200</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="n">new_sol</span> <span class="o">=</span> <span class="n">ls</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="n">sol</span><span class="p">,</span> <span class="n">cost_evaluator</span><span class="p">)</span>

<span class="k">assert</span> <span class="n">new_sol</span><span class="o">.</span><span class="n">is_feasible</span><span class="p">()</span>
</code></pre></div>
</div>
</div>
<p>How good is this solution?</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[8]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="nb">print</span><span class="p">(</span><span class="n">cost_evaluator</span><span class="o">.</span><span class="n">penalised_cost</span><span class="p">(</span><span class="n">new_sol</span><span class="p">))</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
8579
</pre></div></div>
</div>
<p>Pretty good! This is how PyVRP manages infeasibilities: it adjusts the penalty parameters to ensure sufficiently many solutions are feasible. Too few feasible solutions and the penalties go up; too many and they go down. This ensures a balanced population of feasible and infeasible solutions, which is good for diversity and crossover.</p>
<p>The object in charge of managing the penalty terms is the <code class="docutils literal notranslate"><span class="pre">PenaltyManager</span></code>, which can be asked to provide a <code class="docutils literal notranslate"><span class="pre">CostEvaluator</span></code> of the form we saw above.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[9]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp</span><span class="w"> </span><span class="kn">import</span> <span class="n">PenaltyManager</span>

<span class="n">pen_manager</span> <span class="o">=</span> <span class="n">PenaltyManager</span><span class="o">.</span><span class="n">init_from</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">)</span>
<span class="n">cost_evaluator</span> <span class="o">=</span> <span class="n">pen_manager</span><span class="o">.</span><span class="n">cost_evaluator</span><span class="p">()</span>
</code></pre></div>
</div>
</div>
<h3 id="Population-management">Population management<a class="headerlink" href="#Population-management" title="Link to this heading">¶</a></h3>
<p>We are nearly there. All we still need to provide is a <code class="docutils literal notranslate"><span class="pre">Population</span></code>, and a set of initial (random) solutions. Let’s tackle the <code class="docutils literal notranslate"><span class="pre">Population</span></code>.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[10]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp</span><span class="w"> </span><span class="kn">import</span> <span class="n">Population</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp.diversity</span><span class="w"> </span><span class="kn">import</span> <span class="n">broken_pairs_distance</span>

<span class="n">pop</span> <span class="o">=</span> <span class="n">Population</span><span class="p">(</span><span class="n">broken_pairs_distance</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<p>The population tracks the diversity of its solutions. Computing the diversity (dissimilarity) of two solutions can be done in several ways. Functions to do so are provided in <code class="docutils literal notranslate"><span class="pre">pyvrp.diversity</span></code>, and can be provided to the <code class="docutils literal notranslate"><span class="pre">Population</span></code>. Here, we use the <code class="docutils literal notranslate"><span class="pre">broken_pairs_distance</span></code>, which computes a number in <span class="math notranslate nohighlight">\([0, 1]\)</span> based on the number of dissimilar edges in the solutions.</p>
<p>A new population starts off empty:</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[11]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">pop</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span>
</code></pre></div>
</div>
</div>
<p>We can add new solutions to the population using <code class="docutils literal notranslate"><span class="pre">Population.add</span></code>. Recall that <code class="docutils literal notranslate"><span class="pre">sol</span></code> and <code class="docutils literal notranslate"><span class="pre">new_sol</span></code> are, respectively, infeasible and feasible solutions.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[12]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="k">assert</span> <span class="ow">not</span> <span class="n">sol</span><span class="o">.</span><span class="n">is_feasible</span><span class="p">()</span>
<span class="n">pop</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">sol</span><span class="p">,</span> <span class="n">cost_evaluator</span><span class="p">)</span>

<span class="k">assert</span> <span class="n">new_sol</span><span class="o">.</span><span class="n">is_feasible</span><span class="p">()</span>
<span class="n">pop</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">new_sol</span><span class="p">,</span> <span class="n">cost_evaluator</span><span class="p">)</span>

<span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">pop</span><span class="p">)</span> <span class="o">==</span> <span class="mi">2</span>
<span class="k">assert</span> <span class="n">pop</span><span class="o">.</span><span class="n">num_feasible</span><span class="p">()</span> <span class="o">==</span> <span class="mi">1</span>
<span class="k">assert</span> <span class="n">pop</span><span class="o">.</span><span class="n">num_infeasible</span><span class="p">()</span> <span class="o">==</span> <span class="mi">1</span>
</code></pre></div>
</div>
</div>
<h3 id="The-genetic-algorithm-and-crossover">The genetic algorithm and crossover<a class="headerlink" href="#The-genetic-algorithm-and-crossover" title="Link to this heading">¶</a></h3>
<p>A set of initial solution can be constructed easily, by generating a list of random solutions.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[13]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">init_sols</span> <span class="o">=</span> <span class="p">[</span><span class="n">Solution</span><span class="o">.</span><span class="n">make_random</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">,</span> <span class="n">rng</span><span class="p">)</span> <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">25</span><span class="p">)]</span>
</code></pre></div>
</div>
</div>
<p>We are now ready to construct the genetic algorithm. This object additionally takes a crossover operator from <code class="docutils literal notranslate"><span class="pre">pyvrp.crossover</span></code>. We will use the selective route exchange (SREX) method.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[14]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp</span><span class="w"> </span><span class="kn">import</span> <span class="n">GeneticAlgorithm</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp.crossover</span><span class="w"> </span><span class="kn">import</span> <span class="n">selective_route_exchange</span> <span class="k">as</span> <span class="n">srex</span>

<span class="n">algo</span> <span class="o">=</span> <span class="n">GeneticAlgorithm</span><span class="p">(</span>
    <span class="n">INSTANCE</span><span class="p">,</span>
    <span class="n">pen_manager</span><span class="p">,</span>
    <span class="n">rng</span><span class="p">,</span>
    <span class="n">pop</span><span class="p">,</span>
    <span class="n">ls</span><span class="p">,</span>
    <span class="n">srex</span><span class="p">,</span>
    <span class="n">init_sols</span><span class="p">,</span>
<span class="p">)</span>
</code></pre></div>
</div>
</div>
<p>We can call <code class="docutils literal notranslate"><span class="pre">algo.run</span></code>, which iterates until a stopping criterion is met. These stopping criteria can be imported from <code class="docutils literal notranslate"><span class="pre">pyvrp.stop</span></code> - see <a class="reference external" href="https://pyvrp.org/api/stop.html">the API documentation</a> for details.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[15]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp.stop</span><span class="w"> </span><span class="kn">import</span> <span class="n">MaxIterations</span><span class="p">,</span> <span class="n">MaxRuntime</span>

<span class="n">iter_res</span> <span class="o">=</span> <span class="n">algo</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">stop</span><span class="o">=</span><span class="n">MaxIterations</span><span class="p">(</span><span class="mi">500</span><span class="p">))</span>
<span class="n">time_res</span> <span class="o">=</span> <span class="n">algo</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">stop</span><span class="o">=</span><span class="n">MaxRuntime</span><span class="p">(</span><span class="mi">1</span><span class="p">))</span>  <span class="c1"># seconds</span>
</code></pre></div>
</div>
</div>
<p>Let’s investigate the solutions!</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[16]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="nb">print</span><span class="p">(</span><span class="n">iter_res</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
Solution results
================
    # routes: 4
     # trips: 4
   # clients: 100
   objective: 7761
    distance: 7761
    duration: 17761
# iterations: 500
    run-time: 2.07 seconds

Routes
------
Route #1: 90 65 82 99 52 83 64 49 19 18 48 21 23 25 77 58 75 97 59 87 74 86 57 24 22 20 66
Route #2: 94 92 95 67 62 50 34 31 29 27 26 28 30 32 33 76 89 63 85 51 84 56 91 80
Route #3: 61 42 44 39 38 36 35 37 40 43 41 72 71 93 96 54 81
Route #4: 69 98 88 2 6 7 79 73 78 12 14 47 17 16 15 13 9 11 10 53 60 8 46 4 45 5 3 1 70 100 55 68

</pre></div></div>
</div>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[17]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="nb">print</span><span class="p">(</span><span class="n">time_res</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
Solution results
================
    # routes: 4
     # trips: 4
   # clients: 100
   objective: 7761
    distance: 7761
    duration: 17761
# iterations: 285
    run-time: 1.00 seconds

Routes
------
Route #1: 90 65 82 99 52 83 64 49 19 18 48 21 23 25 77 58 75 97 59 87 74 86 57 24 22 20 66
Route #2: 94 92 95 67 62 50 34 31 29 27 26 28 30 32 33 76 89 63 85 51 84 56 91 80
Route #3: 61 42 44 39 38 36 35 37 40 43 41 72 71 93 96 54 81
Route #4: 69 98 88 2 6 7 79 73 78 12 14 47 17 16 15 13 9 11 10 53 60 8 46 4 45 5 3 1 70 100 55 68

</pre></div></div>
</div>
<h2 id="The-solve-function">The <code class="docutils literal notranslate"><span class="pre">solve</span></code> function<a class="headerlink" href="#The-solve-function" title="Link to this heading">¶</a></h2>
<p>Let’s put everything we have learned together into a <code class="docutils literal notranslate"><span class="pre">solve</span></code> function.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[18]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">solve</span><span class="p">(</span><span class="n">stop</span><span class="p">,</span> <span class="n">seed</span><span class="p">):</span>
    <span class="n">rng</span> <span class="o">=</span> <span class="n">RandomNumberGenerator</span><span class="p">(</span><span class="n">seed</span><span class="o">=</span><span class="n">seed</span><span class="p">)</span>
    <span class="n">pm</span> <span class="o">=</span> <span class="n">PenaltyManager</span><span class="o">.</span><span class="n">init_from</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">)</span>
    <span class="n">pop</span> <span class="o">=</span> <span class="n">Population</span><span class="p">(</span><span class="n">broken_pairs_distance</span><span class="p">)</span>

    <span class="n">neighbours</span> <span class="o">=</span> <span class="n">compute_neighbours</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">)</span>
    <span class="n">ls</span> <span class="o">=</span> <span class="n">LocalSearch</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">,</span> <span class="n">rng</span><span class="p">,</span> <span class="n">neighbours</span><span class="p">)</span>

    <span class="k">for</span> <span class="n">node_op</span> <span class="ow">in</span> <span class="n">NODE_OPERATORS</span><span class="p">:</span>
        <span class="n">ls</span><span class="o">.</span><span class="n">add_node_operator</span><span class="p">(</span><span class="n">node_op</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">))</span>

    <span class="k">for</span> <span class="n">route_op</span> <span class="ow">in</span> <span class="n">ROUTE_OPERATORS</span><span class="p">:</span>
        <span class="n">ls</span><span class="o">.</span><span class="n">add_route_operator</span><span class="p">(</span><span class="n">route_op</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">))</span>

    <span class="n">init</span> <span class="o">=</span> <span class="p">[</span><span class="n">Solution</span><span class="o">.</span><span class="n">make_random</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">,</span> <span class="n">rng</span><span class="p">)</span> <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">25</span><span class="p">)]</span>
    <span class="n">algo</span> <span class="o">=</span> <span class="n">GeneticAlgorithm</span><span class="p">(</span><span class="n">INSTANCE</span><span class="p">,</span> <span class="n">pm</span><span class="p">,</span> <span class="n">rng</span><span class="p">,</span> <span class="n">pop</span><span class="p">,</span> <span class="n">ls</span><span class="p">,</span> <span class="n">srex</span><span class="p">,</span> <span class="n">init</span><span class="p">)</span>

    <span class="k">return</span> <span class="n">algo</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">stop</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<p>Very good. Let’s solve the instance again, now using the <code class="docutils literal notranslate"><span class="pre">solve</span></code> function.</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[19]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">res</span> <span class="o">=</span> <span class="n">solve</span><span class="p">(</span><span class="n">stop</span><span class="o">=</span><span class="n">MaxIterations</span><span class="p">(</span><span class="mi">1000</span><span class="p">),</span> <span class="n">seed</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">res</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
Solution results
================
    # routes: 4
     # trips: 4
   # clients: 100
   objective: 7761
    distance: 7761
    duration: 17761
# iterations: 1000
    run-time: 3.92 seconds

Routes
------
Route #1: 90 65 82 99 52 83 64 49 19 18 48 21 23 25 77 58 75 97 59 87 74 86 57 24 22 20 66
Route #2: 94 92 95 67 62 50 34 31 29 27 26 28 30 32 33 76 89 63 85 51 84 56 91 80
Route #3: 61 42 44 39 38 36 35 37 40 43 41 72 71 93 96 54 81
Route #4: 69 98 88 2 6 7 79 73 78 12 14 47 17 16 15 13 9 11 10 53 60 8 46 4 45 5 3 1 70 100 55 68

</pre></div></div>
</div>
<p>PyVRP also provides many plotting tools that can be used to investigate a data instance or solution result.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[20]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">import</span><span class="w"> </span><span class="nn">matplotlib.pyplot</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">plt</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp.plotting</span><span class="w"> </span><span class="kn">import</span> <span class="n">plot_result</span>
</code></pre></div>
</div>
</div>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[21]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">fig</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">figure</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">12</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>

<span class="n">plot_result</span><span class="p">(</span><span class="n">res</span><span class="p">,</span> <span class="n">INSTANCE</span><span class="p">,</span> <span class="n">fig</span><span class="o">=</span><span class="n">fig</span><span class="p">)</span>
<span class="n">plt</span><span class="o">.</span><span class="n">tight_layout</span><span class="p">()</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<img alt="../_images/examples_using_pyvrp_components_42_0.png" src="../_images/examples_using_pyvrp_components_42_0.png" />
</div>
</div>
<p>The top-left figure shows the average diversity of the feasible and infeasible populations. The periodic spikes are due to survivor selection: when the population grows too large, bad solutions are purged. It is clear from this figure that periodic survivor selection improves diversity. The middle-left figure shows the best and average objectives of both sub-populations, which improve over time as the search progresses. The bottom-left figure shows average iteration runtimes (in seconds).
Finally, the figure on the right plots the best observed solution.</p>
<h2 id="Conclusion">Conclusion<a class="headerlink" href="#Conclusion" title="Link to this heading">¶</a></h2>
<p>In this notebook we have used some of the different components in PyVRP to implement our own <code class="docutils literal notranslate"><span class="pre">solve</span></code> function. Along the way we learned about how PyVRP works internally.</p>
<p>The components we saw in this notebook can also be used to create different search algorithms altogether. For example, our <code class="docutils literal notranslate"><span class="pre">LocalSearch</span></code> search method could be used to quickly implement an iterated local search scheme. This modularity allows for a lot of reuse of the PyVRP package.</p>







  
  






                
              </article>
            </div>
          
          
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  
  
  <div class="md-footer-meta md-typeset">
    
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-footer-copyright__highlight">
        &#169; Copyright 2022 - 2025, PyVRP contributors.
        
    </div>
  
    Created using
    <a href="https://www.sphinx-doc.org/" target="_blank" rel="noopener">Sphinx</a>
    8.1.3.
     and
    <a href="https://github.com/jbms/sphinx-immaterial/" target="_blank" rel="noopener">Sphinx-Immaterial</a>
  
</div>
      
    </div>
    
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    <script id="__config" type="application/json">{"base": "..", "features": ["navigation.expand", "navigation.top"], "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike", "staticVersions": [{"aliases": [], "title": "Development", "version": ""}, {"aliases": [], "title": "v0.10.0", "version": "https://pyvrp.github.io/v0.10.0"}, {"aliases": [], "title": "v0.9.0", "version": "https://pyvrp.github.io/v0.9.0"}, {"aliases": [], "title": "v0.8.1", "version": "https://pyvrp.github.io/v0.8.1"}, {"aliases": [], "title": "v0.7.0", "version": "https://pyvrp.github.io/v0.7.0"}, {"aliases": [], "title": "v0.6.0", "version": "https://pyvrp.github.io/v0.6.0"}, {"aliases": [], "title": "v0.5.0", "version": "https://pyvrp.github.io/v0.5.0"}, {"aliases": [], "title": "v0.4.4", "version": "https://pyvrp.github.io/v0.4.4"}, {"aliases": [], "title": "v0.3.0", "version": "https://pyvrp.github.io/v0.3.0"}, {"aliases": [], "title": "v0.2.1", "version": "https://pyvrp.github.io/v0.2.1"}, {"aliases": [], "title": "v0.1.0", "version": "https://pyvrp.github.io/v0.1.0"}], "versionPath": null}}</script>
    
      
        <script src="../_static/sphinx_immaterial_theme.32136f45f91ae6956.min.js?v=a7a9472a"></script>
        <script crossorigin="anonymous" integrity="sha256-Ae2Vz/4ePdIu6ZyI/5ZGsYnb+m0JlOmKPjt6XZ9JJkA=" src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.4/require.min.js"></script>
        <script>window.MathJax = {"tex": {"inlineMath": [["$", "$"], ["\\(", "\\)"]], "processEscapes": true}, "options": {"ignoreHtmlClass": "tex2jax_ignore|mathjax_ignore|document", "processHtmlClass": "tex2jax_process|mathjax_process|math|output_area"}}</script>
        <script id="MathJax-script" src="../_static/mathjax/tex-mml-chtml.js?v=cadf963e"></script>
    
  </body>
</html>