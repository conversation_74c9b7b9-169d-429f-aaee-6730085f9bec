


<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
      
      
      
        <link rel="prev" href="../setup/citing.html">
      
      
        <link rel="next" href="basic_vrps.html">
      
      
      <link rel="icon" href="">
    
    
      
        <title>A quick tutorial - PyVRP 0.11.0 documentation</title>
      
    
    
      
        
      
      


    
    
      
    
    
      
        
        
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
        <link rel="stylesheet" type="text/css" href="../_static/sphinx_immaterial_theme.96fe8683ff2bd71e9.min.css?v=13adf062" />
        <link rel="stylesheet" type="text/css" href="../_static/nbsphinx-code-cells.css?v=2aa19091" />
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="orange" data-md-color-accent="yellow">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#Capacitated-VRP" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
      <div data-md-color-scheme="default" data-md-component="outdated" hidden>
        
      </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../index.html" title="PyVRP 0.11.0 documentation" class="md-header__button md-logo" aria-label="PyVRP 0.11.0 documentation" data-md-component="logo">
      <img src="../_static/icon.svg" alt="logo">
    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            PyVRP 0.11.0 documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              A quick tutorial
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme="default" data-md-color-primary="orange" data-md-color-accent="yellow"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a7 7 0 0 1 7 7c0 2.38-1.19 4.47-3 5.74V17a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1v-2.26C6.19 13.47 5 11.38 5 9a7 7 0 0 1 7-7M9 21v-1h6v1a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1m3-17a5 5 0 0 0-5 5c0 2.05 1.23 3.81 3 4.58V16h4v-2.42c1.77-.77 3-2.53 3-4.58a5 5 0 0 0-5-5"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme="slate" data-md-color-primary="orange" data-md-color-accent="yellow"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a7 7 0 0 0-7 7c0 2.38 1.19 4.47 3 5.74V17a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-2.26c1.81-1.27 3-3.36 3-5.74a7 7 0 0 0-7-7M9 21a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-1H9z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
      <div class="md-header__source">
        <a href="https://github.com/PyVRP/PyVRP/" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
  </div>
  <div class="md-source__repository">
    PyVRP
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../index.html" title="PyVRP 0.11.0 documentation" class="md-nav__button md-logo" aria-label="PyVRP 0.11.0 documentation" data-md-component="logo">
      <img src="../_static/icon.svg" alt="logo">
    </a>
    PyVRP 0.11.0 documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/PyVRP/PyVRP/" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
  </div>
  <div class="md-source__repository">
    PyVRP
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_1" >
        
          
          <label class="md-nav__link" for="__nav_1" id="__nav_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_1">
            <span class="md-nav__icon md-icon"></span>
            Getting started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/introduction_to_vrp.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/introduction_to_vrp.rst (reference label)">A brief introduction to VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/introduction_to_hgs.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/introduction_to_hgs.rst (reference label)">A brief introduction to HGS</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/installation.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/installation.rst (reference label)">Installation instructions</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/getting_help.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/getting_help.rst (reference label)">Getting help</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/faq.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/faq.rst (reference label)">FAQ</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/concepts.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/concepts.rst (reference label)">Concepts</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/benchmarks.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/benchmarks.rst (reference label)">Benchmarks</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../setup/citing.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/setup/citing.rst (reference label)">Citing Py<wbr>VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
    
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2" checked>
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Examples
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Examples
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    <span title="/examples/quick_tutorial.ipynb (reference label)">A quick tutorial</span>
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="#" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/quick_tutorial.ipynb (reference label)">A quick tutorial</span>
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#Capacitated-VRP" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#capacitated-vrp (reference label)">Capacitated VRP</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#VRP-with-time-windows" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#vrp-with-time-windows (reference label)">VRP with time windows</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#Multi-depot-VRP-with-time-windows" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#multi-depot-vrp-with-time-windows (reference label)">Multi-<wbr>depot VRP with time windows</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#Prize-collecting-VRP" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#prize-collecting-vrp (reference label)">Prize-<wbr>collecting VRP</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#VRP-with-simultaneous-pickup-and-delivery" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#vrp-with-simultaneous-pickup-and-delivery (reference label)">VRP with simultaneous pickup and delivery</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#VRP-with-zone-restrictions" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#vrp-with-zone-restrictions (reference label)">VRP with zone restrictions</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#VRP-with-reloading" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#vrp-with-reloading (reference label)">VRP with reloading</span>
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="basic_vrps.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/basic_vrps.ipynb (reference label)">Classic VRPs</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="using_pyvrp_components.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/examples/using_pyvrp_components.ipynb (reference label)">Using Py<wbr>VRP’s components</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    API reference
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            API reference
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/pyvrp.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/pyvrp.rst (reference label)">Py<wbr>VRP</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/crossover.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/crossover.rst (reference label)">Crossover operators</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/diversity.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/diversity.rst (reference label)">Diversity measures</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/repair.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/repair.rst (reference label)">Repair operators</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/search.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/search.rst (reference label)">Search methods</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/plotting.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/plotting.rst (reference label)">Plotting tools</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../api/stop.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/api/stop.rst (reference label)">Stopping criteria</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
      
      
  
  
  
  
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Developing Py<wbr>VRP
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Developing Py<wbr>VRP
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/benchmarking.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/benchmarking.rst (reference label)">Benchmarking</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/contributing.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/contributing.rst (reference label)">Contributing</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/supported_vrplib_fields.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/supported_vrplib_fields.rst (reference label)">The VRPLIB format</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/new_vrp_variants.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/new_vrp_variants.rst (reference label)">Supporting new VRP variants</span>
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
  
    <li class="md-nav__item">
      <a href="../dev/glossary.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    <span title="/dev/glossary.rst (reference label)">Glossary</span>
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary">
  
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#Capacitated-VRP" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#capacitated-vrp (reference label)">Capacitated VRP</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#VRP-with-time-windows" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#vrp-with-time-windows (reference label)">VRP with time windows</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#Multi-depot-VRP-with-time-windows" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#multi-depot-vrp-with-time-windows (reference label)">Multi-<wbr>depot VRP with time windows</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#Prize-collecting-VRP" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#prize-collecting-vrp (reference label)">Prize-<wbr>collecting VRP</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#VRP-with-simultaneous-pickup-and-delivery" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#vrp-with-simultaneous-pickup-and-delivery (reference label)">VRP with simultaneous pickup and delivery</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#VRP-with-zone-restrictions" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#vrp-with-zone-restrictions (reference label)">VRP with zone restrictions</span>
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#VRP-with-reloading" class="md-nav__link">
    
    <span class="md-ellipsis">
      <span title="/examples/quick_tutorial.ipynb#vrp-with-reloading (reference label)">VRP with reloading</span>
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset" role="main">
                
                
                  


<h1 id="A-quick-tutorial">A quick tutorial<a class="headerlink" href="#A-quick-tutorial" title="Link to this heading">¶</a></h1>
<p>This notebook provides a brief tutorial to modelling vehicle routing problems with PyVRP, introducing some of its most important modelling features:</p>
<ul class="simple">
<li><p>We first solve a capacitated VRP, introducing the modelling interface and the most basic components.</p></li>
<li><p>We then solve a VRP with time windows, where we introduce the support PyVRP has for problems with duration constraints.</p></li>
<li><p>We then solve a multi-depot VRP with time windows and maximum route duration constraints.</p></li>
<li><p>We also solve a prize-collecting VRP with optional clients to showcase the modelling optional client visits.</p></li>
<li><p>We then solve a VRP with simultaneous pickup and delivery to show problems with deliveries from the depot to clients, and return shipments from clients to depots.</p></li>
<li><p>We briefly show how to use routing profiles to model zone restrictions.</p></li>
<li><p>Finally, we show how to model a VRP with reloading at various reload depots along the route.</p></li>
</ul>
<h2 id="Capacitated-VRP">Capacitated VRP<a class="headerlink" href="#Capacitated-VRP" title="Link to this heading">¶</a></h2>
<p>We will first model and solve the small capacitated VRP instance with 16 clients defined <a class="reference external" href="https://developers.google.com/optimization/routing/cvrp">in the OR-Tools documentation</a>. This instance has an optimal solution of cost 6208. The data are as follows:</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[1]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="c1"># fmt: off</span>
<span class="n">COORDS</span> <span class="o">=</span> <span class="p">[</span>
    <span class="p">(</span><span class="mi">456</span><span class="p">,</span> <span class="mi">320</span><span class="p">),</span>  <span class="c1"># location 0 - the depot</span>
    <span class="p">(</span><span class="mi">228</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>    <span class="c1"># location 1</span>
    <span class="p">(</span><span class="mi">912</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>    <span class="c1"># location 2</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">80</span><span class="p">),</span>     <span class="c1"># location 3</span>
    <span class="p">(</span><span class="mi">114</span><span class="p">,</span> <span class="mi">80</span><span class="p">),</span>   <span class="c1"># location 4</span>
    <span class="p">(</span><span class="mi">570</span><span class="p">,</span> <span class="mi">160</span><span class="p">),</span>  <span class="c1"># location 5</span>
    <span class="p">(</span><span class="mi">798</span><span class="p">,</span> <span class="mi">160</span><span class="p">),</span>  <span class="c1"># location 6</span>
    <span class="p">(</span><span class="mi">342</span><span class="p">,</span> <span class="mi">240</span><span class="p">),</span>  <span class="c1"># location 7</span>
    <span class="p">(</span><span class="mi">684</span><span class="p">,</span> <span class="mi">240</span><span class="p">),</span>  <span class="c1"># location 8</span>
    <span class="p">(</span><span class="mi">570</span><span class="p">,</span> <span class="mi">400</span><span class="p">),</span>  <span class="c1"># location 9</span>
    <span class="p">(</span><span class="mi">912</span><span class="p">,</span> <span class="mi">400</span><span class="p">),</span>  <span class="c1"># location 10</span>
    <span class="p">(</span><span class="mi">114</span><span class="p">,</span> <span class="mi">480</span><span class="p">),</span>  <span class="c1"># location 11</span>
    <span class="p">(</span><span class="mi">228</span><span class="p">,</span> <span class="mi">480</span><span class="p">),</span>  <span class="c1"># location 12</span>
    <span class="p">(</span><span class="mi">342</span><span class="p">,</span> <span class="mi">560</span><span class="p">),</span>  <span class="c1"># location 13</span>
    <span class="p">(</span><span class="mi">684</span><span class="p">,</span> <span class="mi">560</span><span class="p">),</span>  <span class="c1"># location 14</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">640</span><span class="p">),</span>    <span class="c1"># location 15</span>
    <span class="p">(</span><span class="mi">798</span><span class="p">,</span> <span class="mi">640</span><span class="p">),</span>  <span class="c1"># location 16</span>
<span class="p">]</span>
<span class="n">DEMANDS</span> <span class="o">=</span> <span class="p">[</span><span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">]</span>
<span class="c1"># fmt: on</span>
</code></pre></div>
</div>
</div>
<p>We can use the <code class="docutils literal notranslate"><span class="pre">pyvrp.Model</span></code> interface to conveniently specify our vehicle routing problem using this data. A full description of the <code class="docutils literal notranslate"><span class="pre">Model</span></code> interface is given in our <a class="reference external" href="https://pyvrp.org/api/pyvrp.html#pyvrp.Model.Model">API documentation</a>.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[2]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp</span><span class="w"> </span><span class="kn">import</span> <span class="n">Model</span>

<span class="n">m</span> <span class="o">=</span> <span class="n">Model</span><span class="p">()</span>
<span class="n">m</span><span class="o">.</span><span class="n">add_vehicle_type</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="n">capacity</span><span class="o">=</span><span class="mi">15</span><span class="p">)</span>
<span class="n">depot</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_depot</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">])</span>
<span class="n">clients</span> <span class="o">=</span> <span class="p">[</span>
    <span class="n">m</span><span class="o">.</span><span class="n">add_client</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span> <span class="n">delivery</span><span class="o">=</span><span class="n">DEMANDS</span><span class="p">[</span><span class="n">idx</span><span class="p">])</span>
    <span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">COORDS</span><span class="p">))</span>
<span class="p">]</span>

<span class="n">locations</span> <span class="o">=</span> <span class="p">[</span><span class="n">depot</span><span class="p">]</span> <span class="o">+</span> <span class="n">clients</span>
<span class="k">for</span> <span class="n">frm</span> <span class="ow">in</span> <span class="n">locations</span><span class="p">:</span>
    <span class="k">for</span> <span class="n">to</span> <span class="ow">in</span> <span class="n">locations</span><span class="p">:</span>
        <span class="n">distance</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">x</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">x</span><span class="p">)</span> <span class="o">+</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">y</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">y</span><span class="p">)</span>  <span class="c1"># Manhattan</span>
        <span class="n">m</span><span class="o">.</span><span class="n">add_edge</span><span class="p">(</span><span class="n">frm</span><span class="p">,</span> <span class="n">to</span><span class="p">,</span> <span class="n">distance</span><span class="o">=</span><span class="n">distance</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<p>Let’s inspect the resulting data instance.</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[3]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">import</span><span class="w"> </span><span class="nn">matplotlib.pyplot</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">plt</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp.plotting</span><span class="w"> </span><span class="kn">import</span> <span class="n">plot_coordinates</span>

<span class="n">_</span><span class="p">,</span> <span class="n">ax</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">subplots</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
<span class="n">plot_coordinates</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">data</span><span class="p">(),</span> <span class="n">ax</span><span class="o">=</span><span class="n">ax</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<img alt="../_images/examples_quick_tutorial_6_0.png" src="../_images/examples_quick_tutorial_6_0.png" />
</div>
</div>
<p>The instance looks good, so we are ready to solve it. Let’s do so with a second of runtime, and display the search progress using the <code class="docutils literal notranslate"><span class="pre">display</span></code> argument on <code class="docutils literal notranslate"><span class="pre">Model.solve</span></code>.</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[4]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp.stop</span><span class="w"> </span><span class="kn">import</span> <span class="n">MaxRuntime</span>

<span class="n">res</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">solve</span><span class="p">(</span><span class="n">stop</span><span class="o">=</span><span class="n">MaxRuntime</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span> <span class="n">display</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>  <span class="c1"># one second</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
PyVRP v0.11.0

Solving an instance with:
    1 depot
    16 clients
    4 vehicles (1 vehicle type)

                  |       Feasible        |      Infeasible
    Iters    Time |   #      Avg     Best |   #      Avg     Best
H     500      0s |  45     6230     6208 |  39     6995     5905
     1000      1s |  46     6219     6208 |  65     6198     5235

Search terminated in 1.00s after 1183 iterations.
Best-found solution has cost 6208.

Solution results
================
    # routes: 4
     # trips: 4
   # clients: 16
   objective: 6208
    distance: 6208
    duration: 0
# iterations: 1183
    run-time: 1.00 seconds

</pre></div></div>
</div>
<p>By passing the <code class="docutils literal notranslate"><span class="pre">display</span></code> argument, PyVRP displays statistics about the solver progress and the instance being solved. In particular, it outputs the sizes of the feasible and infeasible solution pools, their average objective values, and the objective of the best solutions in either pool. A heuristic improvement is indicated by a <code class="docutils literal notranslate"><span class="pre">H</span></code> at the start of a line.</p>
<p>Let’s print the solution we have found to see the routes.</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[5]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="nb">print</span><span class="p">(</span><span class="n">res</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
Solution results
================
    # routes: 4
     # trips: 4
   # clients: 16
   objective: 6208
    distance: 6208
    duration: 0
# iterations: 1183
    run-time: 1.00 seconds

Routes
------
Route #1: 7 1 4 3
Route #2: 14 16 10 9
Route #3: 5 6 2 8
Route #4: 13 15 11 12

</pre></div></div>
</div>
<p>Good! Our solution attains the same objective value as the optimal solution OR-Tools finds. Let’s inspect our solution more closely.</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[6]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pyvrp.plotting</span><span class="w"> </span><span class="kn">import</span> <span class="n">plot_solution</span>

<span class="n">_</span><span class="p">,</span> <span class="n">ax</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">subplots</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
<span class="n">plot_solution</span><span class="p">(</span><span class="n">res</span><span class="o">.</span><span class="n">best</span><span class="p">,</span> <span class="n">m</span><span class="o">.</span><span class="n">data</span><span class="p">(),</span> <span class="n">ax</span><span class="o">=</span><span class="n">ax</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<img alt="../_images/examples_quick_tutorial_12_0.png" src="../_images/examples_quick_tutorial_12_0.png" />
</div>
</div>
<p>We have just solved our first vehicle routing problem using PyVRP!</p>
<div class="warning admonition">
<p class="admonition-title">Warning</p>
<p>PyVRP automatically converts all numeric input values to integers. If your data has decimal values, you must scale and convert them to integers first to avoid unexpected behaviour.</p>
</div>
<h2 id="VRP-with-time-windows">VRP with time windows<a class="headerlink" href="#VRP-with-time-windows" title="Link to this heading">¶</a></h2>
<p>Besides the capacitated VRP, PyVRP also supports the VRP with time windows. Let’s see if we can also solve such an instance, again following the <a class="reference external" href="https://developers.google.com/optimization/routing/vrptw">OR-Tools documentation</a>. Like in the OR-Tools example, we will ignore capacity restrictions, and give each vehicle a maximum route duration of 30. Unlike the OR-Tools example, we still aim to minimise the total travel <em>distance</em>, not <em>duration</em>.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[7]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="c1"># fmt: off</span>
<span class="n">DURATION_MATRIX</span> <span class="o">=</span> <span class="p">[</span>
        <span class="p">[</span><span class="mi">0</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">7</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">6</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">13</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">12</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">14</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">9</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">11</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">14</span><span class="p">,</span> <span class="mi">13</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">18</span><span class="p">,</span> <span class="mi">9</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">8</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">11</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">14</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">14</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">16</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">7</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">13</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">12</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">14</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">3</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">12</span><span class="p">,</span> <span class="mi">8</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">6</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">12</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">5</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">2</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">10</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">3</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">13</span><span class="p">,</span> <span class="mi">6</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">2</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">5</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">6</span><span class="p">,</span> <span class="mi">13</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">14</span><span class="p">,</span> <span class="mi">13</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">13</span><span class="p">,</span> <span class="mi">4</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">6</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">12</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">4</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">14</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">8</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">4</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">13</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">6</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">5</span><span class="p">,</span> <span class="mi">12</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">14</span><span class="p">,</span> <span class="mi">12</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">2</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">9</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">18</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">12</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">13</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">13</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">9</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">7</span><span class="p">,</span> <span class="mi">14</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">16</span><span class="p">,</span> <span class="mi">14</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">0</span><span class="p">],</span>
<span class="p">]</span>
<span class="n">TIME_WINDOWS</span> <span class="o">=</span> <span class="p">[</span>
        <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">999</span><span class="p">),</span>  <span class="c1"># location 0 - the depot (modified to be unrestricted)</span>
        <span class="p">(</span><span class="mi">7</span><span class="p">,</span> <span class="mi">12</span><span class="p">),</span>   <span class="c1"># location 1</span>
        <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">),</span>  <span class="c1"># location 2</span>
        <span class="p">(</span><span class="mi">16</span><span class="p">,</span> <span class="mi">18</span><span class="p">),</span>  <span class="c1"># location 3</span>
        <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">13</span><span class="p">),</span>  <span class="c1"># location 4</span>
        <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">5</span><span class="p">),</span>    <span class="c1"># location 5</span>
        <span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span>   <span class="c1"># location 6</span>
        <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>    <span class="c1"># location 7</span>
        <span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span>   <span class="c1"># location 8</span>
        <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span>    <span class="c1"># location 9</span>
        <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">16</span><span class="p">),</span>  <span class="c1"># location 10</span>
        <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">),</span>  <span class="c1"># location 11</span>
        <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">5</span><span class="p">),</span>    <span class="c1"># location 12</span>
        <span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span>   <span class="c1"># location 13</span>
        <span class="p">(</span><span class="mi">7</span><span class="p">,</span> <span class="mi">8</span><span class="p">),</span>    <span class="c1"># location 14</span>
        <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">),</span>  <span class="c1"># location 15</span>
        <span class="p">(</span><span class="mi">11</span><span class="p">,</span> <span class="mi">15</span><span class="p">),</span>  <span class="c1"># location 16</span>
<span class="p">]</span>
<span class="c1"># fmt: on</span>
</code></pre></div>
</div>
</div>
<p>We now need to specify the time windows for all locations, and the duration of travelling along each edge. The depot’s time window is also applied to the vehicle type, to indicate shift time windows.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[8]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">m</span> <span class="o">=</span> <span class="n">Model</span><span class="p">()</span>
<span class="n">m</span><span class="o">.</span><span class="n">add_vehicle_type</span><span class="p">(</span>
    <span class="mi">4</span><span class="p">,</span>
    <span class="n">max_duration</span><span class="o">=</span><span class="mi">30</span><span class="p">,</span>
    <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
    <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
<span class="p">)</span>

<span class="n">depot</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_depot</span><span class="p">(</span>
    <span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
    <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
    <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
<span class="p">)</span>
<span class="n">clients</span> <span class="o">=</span> <span class="p">[</span>
    <span class="n">m</span><span class="o">.</span><span class="n">add_client</span><span class="p">(</span>
        <span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
        <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="p">)</span>
    <span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">COORDS</span><span class="p">))</span>
<span class="p">]</span>

<span class="k">for</span> <span class="n">frm_idx</span><span class="p">,</span> <span class="n">frm</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">to_idx</span><span class="p">,</span> <span class="n">to</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">):</span>
        <span class="n">distance</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">x</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">x</span><span class="p">)</span> <span class="o">+</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">y</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">y</span><span class="p">)</span>  <span class="c1"># Manhattan</span>
        <span class="n">duration</span> <span class="o">=</span> <span class="n">DURATION_MATRIX</span><span class="p">[</span><span class="n">frm_idx</span><span class="p">][</span><span class="n">to_idx</span><span class="p">]</span>
        <span class="n">m</span><span class="o">.</span><span class="n">add_edge</span><span class="p">(</span><span class="n">frm</span><span class="p">,</span> <span class="n">to</span><span class="p">,</span> <span class="n">distance</span><span class="o">=</span><span class="n">distance</span><span class="p">,</span> <span class="n">duration</span><span class="o">=</span><span class="n">duration</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[9]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">res</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">solve</span><span class="p">(</span><span class="n">stop</span><span class="o">=</span><span class="n">MaxRuntime</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span> <span class="n">display</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>  <span class="c1"># one second</span>
<span class="nb">print</span><span class="p">(</span><span class="n">res</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
Solution results
================
    # routes: 4
     # trips: 4
   # clients: 16
   objective: 6528
    distance: 6528
    duration: 79
# iterations: 882
    run-time: 1.00 seconds

Routes
------
Route #1: 7 1 4 3
Route #2: 12 13 15 11
Route #3: 5 8 6 2 10
Route #4: 9 14 16

</pre></div></div>
</div>
<p>Due to the hard time windows requirements, the total travel distance has increased slightly compared to our solution for the capacitated VRP. Let’s have a look at the new solution.</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[10]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">_</span><span class="p">,</span> <span class="n">ax</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">subplots</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
<span class="n">plot_solution</span><span class="p">(</span><span class="n">res</span><span class="o">.</span><span class="n">best</span><span class="p">,</span> <span class="n">m</span><span class="o">.</span><span class="n">data</span><span class="p">(),</span> <span class="n">ax</span><span class="o">=</span><span class="n">ax</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<img alt="../_images/examples_quick_tutorial_21_0.png" src="../_images/examples_quick_tutorial_21_0.png" />
</div>
</div>
<h2 id="Multi-depot-VRP-with-time-windows">Multi-depot VRP with time windows<a class="headerlink" href="#Multi-depot-VRP-with-time-windows" title="Link to this heading">¶</a></h2>
<p>Let’s now solve a VRP with multiple depots and time windows. We consider two depots, and two vehicles per depot that have to start and end their routes at their respective depot. PyVRP additionally supports vehicles ending their routes at a different depot from where they start, by passing different depots to the <code class="docutils literal notranslate"><span class="pre">start_depot</span></code> and <code class="docutils literal notranslate"><span class="pre">end_depot</span></code> arguments of the <code class="docutils literal notranslate"><span class="pre">VehicleType</span></code>.</p>
<p>We will re-use some of the data from the VRPTW case, but change the time window data slightly: the first client now becomes the second depot. Note that in the case of multiple depots, the distinction between vehicle shifts and depot opening times becomes particularly important.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[11]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="c1"># fmt: off</span>
<span class="n">TIME_WINDOWS</span> <span class="o">=</span> <span class="p">[</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">999</span><span class="p">),</span>  <span class="c1"># location 0 - a depot (modified to be unrestricted)</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">999</span><span class="p">),</span>  <span class="c1"># location 1 - a depot (modified to be unrestricted)</span>
    <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">),</span>  <span class="c1"># location 2</span>
    <span class="p">(</span><span class="mi">16</span><span class="p">,</span> <span class="mi">18</span><span class="p">),</span>  <span class="c1"># location 3</span>
    <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">13</span><span class="p">),</span>  <span class="c1"># location 4</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">5</span><span class="p">),</span>    <span class="c1"># location 5</span>
    <span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span>   <span class="c1"># location 6</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>    <span class="c1"># location 7</span>
    <span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span>   <span class="c1"># location 8</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span>    <span class="c1"># location 9</span>
    <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">16</span><span class="p">),</span>  <span class="c1"># location 10</span>
    <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">),</span>  <span class="c1"># location 11</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">5</span><span class="p">),</span>    <span class="c1"># location 12</span>
    <span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span>   <span class="c1"># location 13</span>
    <span class="p">(</span><span class="mi">7</span><span class="p">,</span> <span class="mi">8</span><span class="p">),</span>    <span class="c1"># location 14</span>
    <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">),</span>  <span class="c1"># location 15</span>
    <span class="p">(</span><span class="mi">11</span><span class="p">,</span> <span class="mi">15</span><span class="p">),</span>  <span class="c1"># location 16</span>
<span class="p">]</span>
<span class="c1"># fmt: on</span>
</code></pre></div>
</div>
</div>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[12]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">m</span> <span class="o">=</span> <span class="n">Model</span><span class="p">()</span>

<span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">):</span>
    <span class="n">depot</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_depot</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">])</span>

    <span class="c1"># Two vehicles at each depot, with 30 maximum route duration.</span>
    <span class="n">m</span><span class="o">.</span><span class="n">add_vehicle_type</span><span class="p">(</span>
        <span class="mi">2</span><span class="p">,</span>
        <span class="n">start_depot</span><span class="o">=</span><span class="n">depot</span><span class="p">,</span>
        <span class="n">end_depot</span><span class="o">=</span><span class="n">depot</span><span class="p">,</span>
        <span class="n">max_duration</span><span class="o">=</span><span class="mi">30</span><span class="p">,</span>
        <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="p">)</span>

<span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">COORDS</span><span class="p">)):</span>
    <span class="n">m</span><span class="o">.</span><span class="n">add_client</span><span class="p">(</span>
        <span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
        <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="p">)</span>

<span class="k">for</span> <span class="n">frm_idx</span><span class="p">,</span> <span class="n">frm</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">to_idx</span><span class="p">,</span> <span class="n">to</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">):</span>
        <span class="n">distance</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">x</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">x</span><span class="p">)</span> <span class="o">+</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">y</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">y</span><span class="p">)</span>  <span class="c1"># Manhattan</span>
        <span class="n">duration</span> <span class="o">=</span> <span class="n">DURATION_MATRIX</span><span class="p">[</span><span class="n">frm_idx</span><span class="p">][</span><span class="n">to_idx</span><span class="p">]</span>
        <span class="n">m</span><span class="o">.</span><span class="n">add_edge</span><span class="p">(</span><span class="n">frm</span><span class="p">,</span> <span class="n">to</span><span class="p">,</span> <span class="n">distance</span><span class="o">=</span><span class="n">distance</span><span class="p">,</span> <span class="n">duration</span><span class="o">=</span><span class="n">duration</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<p>Let’s have a look at the modified data instance to familiarise ourself with the changes.</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[13]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">_</span><span class="p">,</span> <span class="n">ax</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">subplots</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
<span class="n">plot_coordinates</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">data</span><span class="p">(),</span> <span class="n">ax</span><span class="o">=</span><span class="n">ax</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<img alt="../_images/examples_quick_tutorial_26_0.png" src="../_images/examples_quick_tutorial_26_0.png" />
</div>
</div>
<p>Let’s solve the instance.</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[14]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">res</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">solve</span><span class="p">(</span><span class="n">stop</span><span class="o">=</span><span class="n">MaxRuntime</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span> <span class="n">display</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>  <span class="c1"># one second</span>
<span class="nb">print</span><span class="p">(</span><span class="n">res</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
Solution results
================
    # routes: 4
     # trips: 4
   # clients: 15
   objective: 6004
    distance: 6004
    duration: 69
# iterations: 1002
    run-time: 1.00 seconds

Routes
------
Route #1: 9 14 16
Route #2: 7 5 8 6 2 10
Route #3: 12 13 15 11
Route #4: 4 3

</pre></div></div>
</div>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[15]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">_</span><span class="p">,</span> <span class="n">ax</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">subplots</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
<span class="n">plot_solution</span><span class="p">(</span><span class="n">res</span><span class="o">.</span><span class="n">best</span><span class="p">,</span> <span class="n">m</span><span class="o">.</span><span class="n">data</span><span class="p">(),</span> <span class="n">ax</span><span class="o">=</span><span class="n">ax</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<img alt="../_images/examples_quick_tutorial_29_0.png" src="../_images/examples_quick_tutorial_29_0.png" />
</div>
</div>
<h2 id="Prize-collecting-VRP">Prize-collecting VRP<a class="headerlink" href="#Prize-collecting-VRP" title="Link to this heading">¶</a></h2>
<p>We now have a basic familiarity with PyVRP’s <code class="docutils literal notranslate"><span class="pre">Model</span></code> interface, but have not seen some of its additional features yet. In this short section we will discuss <em>optional</em> clients, which offer a reward (a prize) when they are visited, but are not required for feasibility. This VRP variant is often called a prize-collecting VRP, and PyVRP supports this out-of-the-box.</p>
<p>Let’s stick to the multiple depot setting, and also define a <code class="docutils literal notranslate"><span class="pre">PRIZES</span></code> list that provides the prizes of visiting each client.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[16]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="c1"># fmt: off</span>
<span class="n">PRIZES</span> <span class="o">=</span> <span class="p">[</span>
    <span class="mi">0</span><span class="p">,</span>    <span class="c1"># location 0 - a depot</span>
    <span class="mi">0</span><span class="p">,</span>    <span class="c1"># location 1 - a depot</span>
    <span class="mi">334</span><span class="p">,</span>  <span class="c1"># location 2</span>
    <span class="mi">413</span><span class="p">,</span>  <span class="c1"># location 3</span>
    <span class="mi">295</span><span class="p">,</span>  <span class="c1"># location 4</span>
    <span class="mi">471</span><span class="p">,</span>  <span class="c1"># location 5</span>
    <span class="mi">399</span><span class="p">,</span>  <span class="c1"># location 6</span>
    <span class="mi">484</span><span class="p">,</span>  <span class="c1"># location 7</span>
    <span class="mi">369</span><span class="p">,</span>  <span class="c1"># location 8</span>
    <span class="mi">410</span><span class="p">,</span>  <span class="c1"># location 9</span>
    <span class="mi">471</span><span class="p">,</span>  <span class="c1"># location 10</span>
    <span class="mi">382</span><span class="p">,</span>  <span class="c1"># location 11</span>
    <span class="mi">347</span><span class="p">,</span>  <span class="c1"># location 12</span>
    <span class="mi">380</span><span class="p">,</span>  <span class="c1"># location 13</span>
    <span class="mi">409</span><span class="p">,</span>  <span class="c1"># location 14</span>
    <span class="mi">302</span><span class="p">,</span>  <span class="c1"># location 15</span>
    <span class="mi">411</span><span class="p">,</span>  <span class="c1"># location 16</span>
<span class="p">]</span>
<span class="c1"># fmt: on</span>
</code></pre></div>
</div>
</div>
<p>When modelling optional clients, it is important to provide both a reward (the <code class="docutils literal notranslate"><span class="pre">prize</span></code> argument to <code class="docutils literal notranslate"><span class="pre">add_client</span></code>), and to mark the client as optional by passing <code class="docutils literal notranslate"><span class="pre">required=False</span></code>:</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[17]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">m</span> <span class="o">=</span> <span class="n">Model</span><span class="p">()</span>

<span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">):</span>
    <span class="n">depot</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_depot</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">])</span>
    <span class="n">m</span><span class="o">.</span><span class="n">add_vehicle_type</span><span class="p">(</span>
        <span class="mi">2</span><span class="p">,</span>
        <span class="n">start_depot</span><span class="o">=</span><span class="n">depot</span><span class="p">,</span>
        <span class="n">end_depot</span><span class="o">=</span><span class="n">depot</span><span class="p">,</span>
        <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="p">)</span>


<span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">COORDS</span><span class="p">)):</span>
    <span class="n">m</span><span class="o">.</span><span class="n">add_client</span><span class="p">(</span>
        <span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
        <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
        <span class="n">prize</span><span class="o">=</span><span class="n">PRIZES</span><span class="p">[</span><span class="n">idx</span><span class="p">],</span>
        <span class="n">required</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
    <span class="p">)</span>

<span class="k">for</span> <span class="n">frm_idx</span><span class="p">,</span> <span class="n">frm</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">to_idx</span><span class="p">,</span> <span class="n">to</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">):</span>
        <span class="n">distance</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">x</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">x</span><span class="p">)</span> <span class="o">+</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">y</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">y</span><span class="p">)</span>  <span class="c1"># Manhattan</span>
        <span class="n">duration</span> <span class="o">=</span> <span class="n">DURATION_MATRIX</span><span class="p">[</span><span class="n">frm_idx</span><span class="p">][</span><span class="n">to_idx</span><span class="p">]</span>
        <span class="n">m</span><span class="o">.</span><span class="n">add_edge</span><span class="p">(</span><span class="n">frm</span><span class="p">,</span> <span class="n">to</span><span class="p">,</span> <span class="n">distance</span><span class="o">=</span><span class="n">distance</span><span class="p">,</span> <span class="n">duration</span><span class="o">=</span><span class="n">duration</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[18]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">res</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">solve</span><span class="p">(</span><span class="n">stop</span><span class="o">=</span><span class="n">MaxRuntime</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span> <span class="n">display</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>  <span class="c1"># one second</span>
<span class="nb">print</span><span class="p">(</span><span class="n">res</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
Solution results
================
    # routes: 3
     # trips: 3
   # clients: 10
   objective: 5145
    distance: 3400
    duration: 40
# iterations: 1289
    run-time: 1.00 seconds

Routes
------
Route #1: 9 14 16 10
Route #2: 7 5 6 8
Route #3: 4 3

</pre></div></div>
</div>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[19]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">_</span><span class="p">,</span> <span class="n">ax</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">subplots</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
<span class="n">plot_solution</span><span class="p">(</span><span class="n">res</span><span class="o">.</span><span class="n">best</span><span class="p">,</span> <span class="n">m</span><span class="o">.</span><span class="n">data</span><span class="p">(),</span> <span class="n">plot_clients</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">ax</span><span class="o">=</span><span class="n">ax</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<img alt="../_images/examples_quick_tutorial_35_0.png" src="../_images/examples_quick_tutorial_35_0.png" />
</div>
</div>
<p>Some clients are not visited in the figure above. These clients are too far from other locations for their prizes to be worth the additional travel cost of visiting. Thus, PyVRP’s solver opts not to visit such optional clients.</p>
<h2 id="VRP-with-simultaneous-pickup-and-delivery">VRP with simultaneous pickup and delivery<a class="headerlink" href="#VRP-with-simultaneous-pickup-and-delivery" title="Link to this heading">¶</a></h2>
<p>We will now consider the VRP with simultaneous pickup and delivery. In this problem variant, clients request items from the depot, and also produce return shipments that needs to be delivered back to the depot after visiting the client. Thus, there are both deliveries from the depot to the clients, and pickups from the clients to the depot.</p>
<p>Let’s remain in the multi-depot, prize-collecting world we entered through the last example. We first define a <code class="docutils literal notranslate"><span class="pre">LOADS</span></code> list that tracks the delivery and pickup amount for each location:</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[20]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="c1"># fmt: off</span>
<span class="n">LOADS</span> <span class="o">=</span> <span class="p">[</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>   <span class="c1"># location 0 - a depot</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>   <span class="c1"># location 1 - a depot</span>
    <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>   <span class="c1"># location 2 - simultaneous pickup and delivery</span>
    <span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>   <span class="c1"># location 3 - pure delivery</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">5</span><span class="p">),</span>   <span class="c1"># location 4 - pure pickup</span>
    <span class="p">(</span><span class="mi">6</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span>   <span class="c1"># location 5 - simultaneous pickup and delivery</span>
    <span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">7</span><span class="p">),</span>   <span class="c1"># location 6 - simultaneous pickup and delivery</span>
    <span class="p">(</span><span class="mi">11</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>  <span class="c1"># location 7 - pure delivery</span>
    <span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>   <span class="c1"># location 8 - pure delivery</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">5</span><span class="p">),</span>   <span class="c1"># location 9 - pure pickup</span>
    <span class="p">(</span><span class="mi">6</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>   <span class="c1"># location 10 - simultaneous pickup and delivery</span>
    <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>   <span class="c1"># location 11 - simultaneous pickup and delivery</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span>   <span class="c1"># location 12 - pure pickup</span>
    <span class="p">(</span><span class="mi">6</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>   <span class="c1"># location 13 - pure delivery</span>
    <span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">2</span><span class="p">),</span>   <span class="c1"># location 14 - simultaneous pickup and delivery</span>
    <span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span>   <span class="c1"># location 15 - simultaneous pickup and delivery</span>
    <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">6</span><span class="p">),</span>   <span class="c1"># location 16 - pure pickup</span>
<span class="p">]</span>
<span class="c1"># fmt: on</span>
</code></pre></div>
</div>
</div>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[21]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">m</span> <span class="o">=</span> <span class="n">Model</span><span class="p">()</span>

<span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">):</span>
    <span class="n">depot</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_depot</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">])</span>
    <span class="n">m</span><span class="o">.</span><span class="n">add_vehicle_type</span><span class="p">(</span>
        <span class="mi">2</span><span class="p">,</span>
        <span class="n">start_depot</span><span class="o">=</span><span class="n">depot</span><span class="p">,</span>
        <span class="n">end_depot</span><span class="o">=</span><span class="n">depot</span><span class="p">,</span>
        <span class="n">capacity</span><span class="o">=</span><span class="mi">15</span><span class="p">,</span>
        <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="p">)</span>


<span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">COORDS</span><span class="p">)):</span>
    <span class="n">m</span><span class="o">.</span><span class="n">add_client</span><span class="p">(</span>
        <span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
        <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
        <span class="n">delivery</span><span class="o">=</span><span class="n">LOADS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">pickup</span><span class="o">=</span><span class="n">LOADS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
        <span class="n">prize</span><span class="o">=</span><span class="n">PRIZES</span><span class="p">[</span><span class="n">idx</span><span class="p">],</span>
        <span class="n">required</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
    <span class="p">)</span>

<span class="k">for</span> <span class="n">frm_idx</span><span class="p">,</span> <span class="n">frm</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">to_idx</span><span class="p">,</span> <span class="n">to</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">):</span>
        <span class="n">distance</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">x</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">x</span><span class="p">)</span> <span class="o">+</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">y</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">y</span><span class="p">)</span>  <span class="c1"># Manhattan</span>
        <span class="n">duration</span> <span class="o">=</span> <span class="n">DURATION_MATRIX</span><span class="p">[</span><span class="n">frm_idx</span><span class="p">][</span><span class="n">to_idx</span><span class="p">]</span>
        <span class="n">m</span><span class="o">.</span><span class="n">add_edge</span><span class="p">(</span><span class="n">frm</span><span class="p">,</span> <span class="n">to</span><span class="p">,</span> <span class="n">distance</span><span class="o">=</span><span class="n">distance</span><span class="p">,</span> <span class="n">duration</span><span class="o">=</span><span class="n">duration</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[22]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">res</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">solve</span><span class="p">(</span><span class="n">stop</span><span class="o">=</span><span class="n">MaxRuntime</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span> <span class="n">display</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>  <span class="c1"># one second</span>
<span class="nb">print</span><span class="p">(</span><span class="n">res</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
Solution results
================
    # routes: 3
     # trips: 3
   # clients: 6
   objective: 5375
    distance: 1940
    duration: 21
# iterations: 1271
    run-time: 1.00 seconds

Routes
------
Route #1: 7
Route #2: 9 5 8
Route #3: 4 3

</pre></div></div>
</div>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[23]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">_</span><span class="p">,</span> <span class="n">ax</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">subplots</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
<span class="n">plot_solution</span><span class="p">(</span><span class="n">res</span><span class="o">.</span><span class="n">best</span><span class="p">,</span> <span class="n">m</span><span class="o">.</span><span class="n">data</span><span class="p">(),</span> <span class="n">plot_clients</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">ax</span><span class="o">=</span><span class="n">ax</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<img alt="../_images/examples_quick_tutorial_41_0.png" src="../_images/examples_quick_tutorial_41_0.png" />
</div>
</div>
<h2 id="VRP-with-zone-restrictions">VRP with zone restrictions<a class="headerlink" href="#VRP-with-zone-restrictions" title="Link to this heading">¶</a></h2>
<p>We have seen several VRP variants in this notebook already. Let us conclude with a variant showing how to model zone restrictions, where some vehicles are not allowed to visit clients located inside a particular area. Such restrictions commonly apply in urban environments with emission zones, where several types of (heavy) trucks may not enter. We will add one regular vehicle type to the model that can enter the restricted zone. Additionally, we will consider a vehicle type that cannot enter the
restricted zone, and has to travel from the first to the second depot.</p>
<p>Suppose we have a rectangular zone defined by the following <code class="docutils literal notranslate"><span class="pre">(x,</span> <span class="pre">y)</span></code> coordinates.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[24]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">ZONE</span> <span class="o">=</span> <span class="p">((</span><span class="mi">500</span><span class="p">,</span> <span class="mi">125</span><span class="p">),</span> <span class="p">(</span><span class="mi">850</span><span class="p">,</span> <span class="mi">275</span><span class="p">))</span>


<span class="k">def</span><span class="w"> </span><span class="nf">in_zone</span><span class="p">(</span><span class="n">client</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
    <span class="k">return</span> <span class="p">(</span>
        <span class="n">ZONE</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">]</span> <span class="o">&lt;=</span> <span class="n">client</span><span class="o">.</span><span class="n">x</span> <span class="o">&lt;=</span> <span class="n">ZONE</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">0</span><span class="p">]</span>
        <span class="ow">and</span> <span class="n">ZONE</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">]</span> <span class="o">&lt;=</span> <span class="n">client</span><span class="o">.</span><span class="n">y</span> <span class="o">&lt;=</span> <span class="n">ZONE</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">1</span><span class="p">]</span>
    <span class="p">)</span>
</code></pre></div>
</div>
</div>
<p>We can now set up a <code class="docutils literal notranslate"><span class="pre">Model</span></code> as follows, using routing profiles to restrict which vehicle types can enter the zone to visit clients there.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[25]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">m</span> <span class="o">=</span> <span class="n">Model</span><span class="p">()</span>

<span class="n">depot1</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_depot</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">])</span>
<span class="n">depot2</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_depot</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">1</span><span class="p">])</span>

<span class="n">regular</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_profile</span><span class="p">()</span>
<span class="n">m</span><span class="o">.</span><span class="n">add_vehicle_type</span><span class="p">(</span>
    <span class="mi">2</span><span class="p">,</span>
    <span class="n">start_depot</span><span class="o">=</span><span class="n">depot1</span><span class="p">,</span>
    <span class="n">end_depot</span><span class="o">=</span><span class="n">depot1</span><span class="p">,</span>
    <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
    <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="n">profile</span><span class="o">=</span><span class="n">regular</span><span class="p">,</span>
<span class="p">)</span>

<span class="n">restricted</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_profile</span><span class="p">()</span>
<span class="n">m</span><span class="o">.</span><span class="n">add_vehicle_type</span><span class="p">(</span>
    <span class="mi">2</span><span class="p">,</span>
    <span class="n">start_depot</span><span class="o">=</span><span class="n">depot1</span><span class="p">,</span>
    <span class="n">end_depot</span><span class="o">=</span><span class="n">depot2</span><span class="p">,</span>
    <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
    <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="n">profile</span><span class="o">=</span><span class="n">restricted</span><span class="p">,</span>
<span class="p">)</span>

<span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">COORDS</span><span class="p">)):</span>
    <span class="n">m</span><span class="o">.</span><span class="n">add_client</span><span class="p">(</span>
        <span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
        <span class="n">tw_early</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">tw_late</span><span class="o">=</span><span class="n">TIME_WINDOWS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="p">)</span>

<span class="k">for</span> <span class="n">frm_idx</span><span class="p">,</span> <span class="n">frm</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">to_idx</span><span class="p">,</span> <span class="n">to</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">):</span>
        <span class="n">distance</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">x</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">x</span><span class="p">)</span> <span class="o">+</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">y</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">y</span><span class="p">)</span>  <span class="c1"># Manhattan</span>
        <span class="n">duration</span> <span class="o">=</span> <span class="n">DURATION_MATRIX</span><span class="p">[</span><span class="n">frm_idx</span><span class="p">][</span><span class="n">to_idx</span><span class="p">]</span>

        <span class="c1"># Edges without a specific profile assignment are added to all</span>
        <span class="c1"># profiles, unless a profile-specific edge overrides them.</span>
        <span class="n">m</span><span class="o">.</span><span class="n">add_edge</span><span class="p">(</span><span class="n">frm</span><span class="p">,</span> <span class="n">to</span><span class="p">,</span> <span class="n">distance</span><span class="o">=</span><span class="n">distance</span><span class="p">,</span> <span class="n">duration</span><span class="o">=</span><span class="n">duration</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">frm_idx</span> <span class="o">!=</span> <span class="n">to_idx</span> <span class="ow">and</span> <span class="n">in_zone</span><span class="p">(</span><span class="n">to</span><span class="p">):</span>
            <span class="c1"># Here we specify an edge with a high distance and duration</span>
            <span class="c1"># for the restricted profile. This ensures vehicles with</span>
            <span class="c1"># that profile do not travel over this edge.</span>
            <span class="n">m</span><span class="o">.</span><span class="n">add_edge</span><span class="p">(</span>
                <span class="n">frm</span><span class="p">,</span>
                <span class="n">to</span><span class="p">,</span>
                <span class="n">distance</span><span class="o">=</span><span class="mi">1_000</span><span class="p">,</span>
                <span class="n">duration</span><span class="o">=</span><span class="mi">1_000</span><span class="p">,</span>
                <span class="n">profile</span><span class="o">=</span><span class="n">restricted</span><span class="p">,</span>
            <span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[26]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">res</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">solve</span><span class="p">(</span><span class="n">stop</span><span class="o">=</span><span class="n">MaxRuntime</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span> <span class="n">display</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>  <span class="c1"># one second</span>
</code></pre></div>
</div>
</div>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[27]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">_</span><span class="p">,</span> <span class="n">ax</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">subplots</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
<span class="n">plot_solution</span><span class="p">(</span><span class="n">res</span><span class="o">.</span><span class="n">best</span><span class="p">,</span> <span class="n">m</span><span class="o">.</span><span class="n">data</span><span class="p">(),</span> <span class="n">ax</span><span class="o">=</span><span class="n">ax</span><span class="p">)</span>

<span class="c1"># Highlight the restricted zone.</span>
<span class="n">ax</span><span class="o">.</span><span class="n">fill_between</span><span class="p">(</span>
    <span class="p">[</span><span class="n">ZONE</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">ZONE</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">0</span><span class="p">]],</span>
    <span class="n">ZONE</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="n">ZONE</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
    <span class="n">color</span><span class="o">=</span><span class="s2">&quot;red&quot;</span><span class="p">,</span>
    <span class="n">alpha</span><span class="o">=</span><span class="mf">0.15</span><span class="p">,</span>
<span class="p">);</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<img alt="../_images/examples_quick_tutorial_48_0.png" src="../_images/examples_quick_tutorial_48_0.png" />
</div>
</div>
<h2 id="VRP-with-reloading">VRP with reloading<a class="headerlink" href="#VRP-with-reloading" title="Link to this heading">¶</a></h2>
<p>Sometimes vehicles can execute multiple trips over a time horizon, by reloading at the depot between trips. This effectively mitigates the capacity constraint we have so far seen, because vehicles can instead opt to return to the depot to reload if needed. PyVRP supports a very general form of reloading, with free depot selection. Optionally, the maximum number of reloads per vehicle type may also be restricted. See the FAQ for modelling service or loading durations at the depots. We will solve
a small example problem here to showcase some of these features, focusing on reloading, rather than time window support.</p>
<div class="nbinput nblast docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[28]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">m</span> <span class="o">=</span> <span class="n">Model</span><span class="p">()</span>

<span class="n">depot1</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_depot</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">])</span>
<span class="n">depot2</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">add_depot</span><span class="p">(</span><span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span> <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">1</span><span class="p">])</span>

<span class="n">m</span><span class="o">.</span><span class="n">add_vehicle_type</span><span class="p">(</span>
    <span class="mi">3</span><span class="p">,</span>
    <span class="n">capacity</span><span class="o">=</span><span class="mi">15</span><span class="p">,</span>
    <span class="n">start_depot</span><span class="o">=</span><span class="n">depot1</span><span class="p">,</span>
    <span class="n">end_depot</span><span class="o">=</span><span class="n">depot1</span><span class="p">,</span>
    <span class="n">reload_depots</span><span class="o">=</span><span class="p">[</span><span class="n">depot1</span><span class="p">,</span> <span class="n">depot2</span><span class="p">],</span>  <span class="c1"># where reloads may take place</span>
    <span class="n">max_reloads</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>  <span class="c1"># maximum number of reload depot visits on a route</span>
<span class="p">)</span>

<span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">COORDS</span><span class="p">)):</span>
    <span class="n">m</span><span class="o">.</span><span class="n">add_client</span><span class="p">(</span>
        <span class="n">x</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">0</span><span class="p">],</span>
        <span class="n">y</span><span class="o">=</span><span class="n">COORDS</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="mi">1</span><span class="p">],</span>
        <span class="n">delivery</span><span class="o">=</span><span class="n">DEMANDS</span><span class="p">[</span><span class="n">idx</span><span class="p">],</span>
    <span class="p">)</span>

<span class="k">for</span> <span class="n">frm</span> <span class="ow">in</span> <span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">:</span>
    <span class="k">for</span> <span class="n">to</span> <span class="ow">in</span> <span class="n">m</span><span class="o">.</span><span class="n">locations</span><span class="p">:</span>
        <span class="n">distance</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">x</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">x</span><span class="p">)</span> <span class="o">+</span> <span class="nb">abs</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">y</span> <span class="o">-</span> <span class="n">to</span><span class="o">.</span><span class="n">y</span><span class="p">)</span>  <span class="c1"># Manhattan</span>
        <span class="n">m</span><span class="o">.</span><span class="n">add_edge</span><span class="p">(</span><span class="n">frm</span><span class="p">,</span> <span class="n">to</span><span class="p">,</span> <span class="n">distance</span><span class="o">=</span><span class="n">distance</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<p>Returns to a reload depot are marked with a <code class="docutils literal notranslate"><span class="pre">|</span></code> in the result summary, as follows:</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[29]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">res</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">solve</span><span class="p">(</span><span class="n">stop</span><span class="o">=</span><span class="n">MaxRuntime</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span> <span class="n">display</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>  <span class="c1"># one second</span>
<span class="nb">print</span><span class="p">(</span><span class="n">res</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
Solution results
================
    # routes: 2
     # trips: 4
   # clients: 15
   objective: 5728
    distance: 5728
    duration: 0
# iterations: 1030
    run-time: 1.00 seconds

Routes
------
Route #1: 5 8 6 2 | 4 3 7
Route #2: 12 11 15 13 | 10 16 14 9

</pre></div></div>
</div>
<p>Each route in the solution consists of two trips. Let’s investigate the first route in more detail:</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[30]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">route</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">res</span><span class="o">.</span><span class="n">best</span><span class="o">.</span><span class="n">routes</span><span class="p">()</span>

<span class="k">for</span> <span class="n">idx</span><span class="p">,</span> <span class="n">trip</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">route</span><span class="o">.</span><span class="n">trips</span><span class="p">()):</span>
    <span class="n">start_depot</span> <span class="o">=</span> <span class="n">trip</span><span class="o">.</span><span class="n">start_depot</span><span class="p">()</span>
    <span class="n">end_depot</span> <span class="o">=</span> <span class="n">trip</span><span class="o">.</span><span class="n">end_depot</span><span class="p">()</span>
    <span class="n">delivery</span> <span class="o">=</span> <span class="n">trip</span><span class="o">.</span><span class="n">delivery</span><span class="p">()[</span><span class="mi">0</span><span class="p">]</span>

    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">idx</span><span class="si">}</span><span class="s2">: Trip visits clients </span><span class="si">{</span><span class="n">trip</span><span class="o">.</span><span class="n">visits</span><span class="p">()</span><span class="si">}</span><span class="s2">.&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;   It starts at depot </span><span class="si">{</span><span class="n">start_depot</span><span class="si">}</span><span class="s2"> and ends at </span><span class="si">{</span><span class="n">end_depot</span><span class="si">}</span><span class="s2">.&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;   Trip distance is </span><span class="si">{</span><span class="n">trip</span><span class="o">.</span><span class="n">distance</span><span class="p">()</span><span class="si">}</span><span class="s2">, total delivery </span><span class="si">{</span><span class="n">delivery</span><span class="si">}</span><span class="s2">.&quot;</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<div class="highlight"><pre>
0: Trip visits clients [5, 8, 6, 2].
   It starts at depot 0 and ends at 1.
   Trip distance is 1620, total delivery 15.
1: Trip visits clients [4, 3, 7].
   It starts at depot 1 and ends at 0.
   Trip distance is 1004, total delivery 14.
</pre></div></div>
</div>
<p>A plot reveals the routing and reloading decisions that PyVRP determined:</p>
<div class="nbinput docutils container">
<div class="prompt highlight-none notranslate"><div class="highlight"><pre><span></span><code>[31]:
</code></pre></div>
</div>
<div class="input_area highlight-ipython3 notranslate"><div class="highlight"><pre><span></span><code><span class="n">_</span><span class="p">,</span> <span class="n">ax</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">subplots</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
<span class="n">plot_solution</span><span class="p">(</span><span class="n">res</span><span class="o">.</span><span class="n">best</span><span class="p">,</span> <span class="n">m</span><span class="o">.</span><span class="n">data</span><span class="p">(),</span> <span class="n">ax</span><span class="o">=</span><span class="n">ax</span><span class="p">)</span>
</code></pre></div>
</div>
</div>
<div class="nboutput nblast docutils container">
<div class="prompt empty docutils container">
</div>
<div class="output_area docutils container">
<img alt="../_images/examples_quick_tutorial_56_0.png" src="../_images/examples_quick_tutorial_56_0.png" />
</div>
</div>
<p>This concludes the brief tutorial: you now know how to model and solve vehicle routing problems using PyVRP’s <code class="docutils literal notranslate"><span class="pre">Model</span></code> interface. PyVRP supports several additional VRP variants we have not covered here. Have a look at the VRP introduction and other documentation pages to see how those can be modelled and solved.</p>







  
  






                
              </article>
            </div>
          
          
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  
  
  <div class="md-footer-meta md-typeset">
    
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-footer-copyright__highlight">
        &#169; Copyright 2022 - 2025, PyVRP contributors.
        
    </div>
  
    Created using
    <a href="https://www.sphinx-doc.org/" target="_blank" rel="noopener">Sphinx</a>
    8.1.3.
     and
    <a href="https://github.com/jbms/sphinx-immaterial/" target="_blank" rel="noopener">Sphinx-Immaterial</a>
  
</div>
      
    </div>
    
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    <script id="__config" type="application/json">{"base": "..", "features": ["navigation.expand", "navigation.top"], "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike", "staticVersions": [{"aliases": [], "title": "Development", "version": ""}, {"aliases": [], "title": "v0.10.0", "version": "https://pyvrp.github.io/v0.10.0"}, {"aliases": [], "title": "v0.9.0", "version": "https://pyvrp.github.io/v0.9.0"}, {"aliases": [], "title": "v0.8.1", "version": "https://pyvrp.github.io/v0.8.1"}, {"aliases": [], "title": "v0.7.0", "version": "https://pyvrp.github.io/v0.7.0"}, {"aliases": [], "title": "v0.6.0", "version": "https://pyvrp.github.io/v0.6.0"}, {"aliases": [], "title": "v0.5.0", "version": "https://pyvrp.github.io/v0.5.0"}, {"aliases": [], "title": "v0.4.4", "version": "https://pyvrp.github.io/v0.4.4"}, {"aliases": [], "title": "v0.3.0", "version": "https://pyvrp.github.io/v0.3.0"}, {"aliases": [], "title": "v0.2.1", "version": "https://pyvrp.github.io/v0.2.1"}, {"aliases": [], "title": "v0.1.0", "version": "https://pyvrp.github.io/v0.1.0"}], "versionPath": null}}</script>
    
      
        <script src="../_static/sphinx_immaterial_theme.32136f45f91ae6956.min.js?v=a7a9472a"></script>
        <script crossorigin="anonymous" integrity="sha256-Ae2Vz/4ePdIu6ZyI/5ZGsYnb+m0JlOmKPjt6XZ9JJkA=" src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.4/require.min.js"></script>
        <script>window.MathJax = {"tex": {"inlineMath": [["$", "$"], ["\\(", "\\)"]], "processEscapes": true}, "options": {"ignoreHtmlClass": "tex2jax_ignore|mathjax_ignore|document", "processHtmlClass": "tex2jax_process|mathjax_process|math|output_area"}}</script>
        <script id="MathJax-script" src="../_static/mathjax/tex-mml-chtml.js?v=cadf963e"></script>
    
  </body>
</html>