.. module:: pyvrp.plotting
   :synopsis: Plotting tools


Plotting tools
==============

The :mod:`pyvrp.plotting` module contains various functions for plotting problem instances and solutions to those problem instances.
These can be used to better understand your problem, and to help investigate the solutions returned by the genetic algorithm.

.. automodule:: pyvrp.plotting.plot_coordinates

   .. autofunction:: plot_coordinates

.. automodule:: pyvrp.plotting.plot_demands

   .. autofunction:: plot_demands

.. automodule:: pyvrp.plotting.plot_diversity

   .. autofunction:: plot_diversity

.. automodule:: pyvrp.plotting.plot_instance

   .. autofunction:: plot_instance

.. automodule:: pyvrp.plotting.plot_objectives

   .. autofunction:: plot_objectives

.. automodule:: pyvrp.plotting.plot_result

   .. autofunction:: plot_result

.. automodule:: pyvrp.plotting.plot_route_schedule

   .. autofunction:: plot_route_schedule

.. automodule:: pyvrp.plotting.plot_runtimes

   .. autofunction:: plot_runtimes

.. automodule:: pyvrp.plotting.plot_solution

   .. autofunction:: plot_solution

.. automodule:: pyvrp.plotting.plot_time_windows

   .. autofunction:: plot_time_windows
