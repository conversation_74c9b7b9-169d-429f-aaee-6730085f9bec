Search.setIndex({"alltitles": {"A brief introduction to HGS": [[22, null]], "A brief introduction to VRP": [[23, null]], "A quick tutorial": [[13, null]], "A tour of PyVRP": [[14, "A-tour-of-PyVRP"]], "API reference": [[15, null]], "Benchmarking": [[7, null]], "Benchmarks": [[16, null]], "Building the extensions": [[8, "building-the-extensions"]], "Capacitated VRP": [[13, "Capacitated-VRP"]], "Capacitated vehicle routing problem": [[23, "capacitated-vehicle-routing-problem"]], "Citing PyVRP": [[17, null]], "Classic VRPs": [[12, null]], "Clients": [[18, "clients"]], "Committing changes": [[8, "committing-changes"]], "Concepts": [[18, null]], "Conclusion": [[12, "Conclusion"], [14, "Conclusion"]], "Contents": [[15, "contents"]], "Contributing": [[8, null]], "Crossover operators": [[0, null]], "Data sections": [[11, "data-sections"]], "Debugging the extensions": [[8, "debugging-the-extensions"]], "Developing PyVRP": [[15, null]], "Diversity measures": [[1, null]], "Examples": [[15, null]], "FAQ": [[19, null]], "Getting help": [[20, null]], "Getting started": [[15, null]], "Glossary": [[9, null]], "Hint": [[3, null], [10, null], [10, null], [10, null], [10, null], [10, null], [10, null], [15, null], [16, null], [22, null], [23, null]], "Installation": [[19, "installation"]], "Installation instructions": [[21, null]], "Installing from source": [[21, "installing-from-source"]], "Licensing": [[8, "licensing"]], "Modelling": [[19, "modelling"]], "Multi-depot VRP with time windows": [[13, "Multi-depot-VRP-with-time-windows"]], "Multi-depot vehicle routing problem": [[23, "multi-depot-vehicle-routing-problem"]], "Node operators": [[5, "node-operators"]], "Note": [[0, null], [1, null], [1, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [3, null], [5, null], [8, null], [8, null], [10, null], [10, null], [11, null], [16, null], [20, null], [22, null], [23, null], [23, null]], "Plotting tools": [[2, null]], "Population management": [[14, "Population-management"]], "Prize-collecting VRP": [[13, "Prize-collecting-VRP"]], "Prize-collecting vehicle routing problem": [[23, "prize-collecting-vehicle-routing-problem"]], "Profiling the extensions": [[8, "profiling-the-extensions"]], "PyVRP": [[3, null], [16, "pyvrp"]], "Random number generator": [[14, "Random-number-generator"]], "Reading the instance": [[12, "Reading-the-instance"], [12, "id1"]], "Reference VRP solvers": [[16, "reference-vrp-solvers"]], "Repair operators": [[4, null]], "Route operators": [[5, "route-operators"]], "Running the examples locally": [[21, "running-the-examples-locally"]], "Search method": [[14, "Search-method"]], "Search methods": [[5, null]], "Setting up Github Codespaces": [[8, "setting-up-github-codespaces"]], "Setting up a local installation": [[8, "setting-up-a-local-installation"]], "Solution representation and evaluation": [[14, "Solution-representation-and-evaluation"]], "Solving a larger VRPTW instance": [[12, "Solving-a-larger-VRPTW-instance"]], "Solving the instance": [[12, "Solving-the-instance"], [12, "id2"]], "Specifications": [[11, "specifications"]], "Stopping criteria": [[6, null]], "Submitting a bug report": [[20, "submitting-a-bug-report"]], "Submitting a feature request": [[20, "submitting-a-feature-request"]], "Supported VRP variants": [[23, "supported-vrp-variants"]], "Supporting new VRP variants": [[10, null]], "The VRP with time windows": [[12, "The-VRP-with-time-windows"]], "The VRPLIB format": [[11, null]], "The capacitated VRP": [[12, "The-capacitated-VRP"]], "The genetic algorithm and crossover": [[14, "The-genetic-algorithm-and-crossover"]], "The solve function": [[14, "The-solve-function"]], "Time and duration constraints": [[18, "time-and-duration-constraints"]], "Using PyVRP\u2019s components": [[14, null]], "VRP with reloading": [[13, "VRP-with-reloading"]], "VRP with simultaneous pickup and delivery": [[13, "VRP-with-simultaneous-pickup-and-delivery"]], "VRP with time windows": [[13, "VRP-with-time-windows"]], "VRP with zone restrictions": [[13, "VRP-with-zone-restrictions"]], "Vehicle routing problem with time windows": [[23, "vehicle-routing-problem-with-time-windows"]], "Vehicles": [[18, "vehicles"]], "Warning": [[0, null], [3, null], [3, null], [13, null]]}, "docurls": ["api/crossover.html", "api/diversity.html", "api/plotting.html", "api/pyvrp.html", "api/repair.html", "api/search.html", "api/stop.html", "dev/benchmarking.html", "dev/contributing.html", "dev/glossary.html", "dev/new_vrp_variants.html", "dev/supported_vrplib_fields.html", "examples/basic_vrps.html", "examples/quick_tutorial.html", "examples/using_pyvrp_components.html", "index.html", "setup/benchmarks.html", "setup/citing.html", "setup/concepts.html", "setup/faq.html", "setup/getting_help.html", "setup/installation.html", "setup/introduction_to_hgs.html", "setup/introduction_to_vrp.html"], "envversion": {"nbsphinx": 4, "sphinx": 64, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1}, "indexentries": {"__and__() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.__and__", false]], "__call__() (localsearch method)": [[5, "pyvrp.search.LocalSearch.LocalSearch.__call__", false]], "__call__() (randomnumbergenerator method)": [[3, "pyvrp._pyvrp.RandomNumberGenerator.__call__", false]], "__call__() (searchmethod method)": [[5, "pyvrp.search.SearchMethod.SearchMethod.__call__", false]], "__call__() (stoppingcriterion method)": [[6, "pyvrp.stop.StoppingCriterion.StoppingCriterion.__call__", false]], "__eq__() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.__eq__", false]], "__getitem__() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.__getitem__", false]], "__invert__() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.__invert__", false]], "__iter__() (clientgroup method)": [[3, "pyvrp._pyvrp.ClientGroup.__iter__", false]], "__iter__() (population method)": [[3, "pyvrp.Population.Population.__iter__", false]], "__len__() (clientgroup method)": [[3, "pyvrp._pyvrp.ClientGroup.__len__", false]], "__len__() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.__len__", false]], "__len__() (population method)": [[3, "pyvrp.Population.Population.__len__", false]], "__or__() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.__or__", false]], "__setitem__() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.__setitem__", false]], "__xor__() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.__xor__", false]], "add() (population method)": [[3, "pyvrp.Population.Population.add", false]], "add_client() (clientgroup method)": [[3, "pyvrp._pyvrp.ClientGroup.add_client", false]], "add_client() (model method)": [[3, "pyvrp.Model.Model.add_client", false]], "add_client_group() (model method)": [[3, "pyvrp.Model.Model.add_client_group", false]], "add_depot() (model method)": [[3, "pyvrp.Model.Model.add_depot", false]], "add_edge() (model method)": [[3, "pyvrp.Model.Model.add_edge", false]], "add_edge() (profile method)": [[3, "pyvrp.Model.Profile.add_edge", false]], "add_node_operator() (localsearch method)": [[5, "pyvrp.search.LocalSearch.LocalSearch.add_node_operator", false]], "add_profile() (model method)": [[3, "pyvrp.Model.Model.add_profile", false]], "add_route_operator() (localsearch method)": [[5, "pyvrp.search.LocalSearch.LocalSearch.add_route_operator", false]], "add_vehicle_type() (model method)": [[3, "pyvrp.Model.Model.add_vehicle_type", false]], "all() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.all", false]], "any() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.any", false]], "backhaul_section": [[11, "term-BACKHAUL_SECTION", true]], "booster_cost_evaluator() (penaltymanager method)": [[3, "pyvrp.PenaltyManager.PenaltyManager.booster_cost_evaluator", false]], "broken_pairs_distance() (in module pyvrp.diversity._diversity)": [[1, "pyvrp.diversity._diversity.broken_pairs_distance", false]], "capacity": [[11, "term-CAPACITY", true]], "capacity (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.capacity", false]], "capacity_section": [[11, "term-CAPACITY_SECTION", true]], "centroid() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.centroid", false]], "centroid() (route method)": [[3, "pyvrp._pyvrp.Route.centroid", false]], "centroid() (trip method)": [[3, "pyvrp._pyvrp.Trip.centroid", false]], "clear() (clientgroup method)": [[3, "pyvrp._pyvrp.ClientGroup.clear", false]], "clear() (population method)": [[3, "pyvrp.Population.Population.clear", false]], "client (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.Client", false]], "clientgroup (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.ClientGroup", false]], "clients (clientgroup attribute)": [[3, "pyvrp._pyvrp.ClientGroup.clients", false]], "clients() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.clients", false]], "collect_from() (statistics method)": [[3, "pyvrp.Statistics.Statistics.collect_from", false]], "compute_neighbours() (in module pyvrp.search.neighbourhood)": [[5, "pyvrp.search.neighbourhood.compute_neighbours", false]], "concatenation scheme": [[9, "term-Concatenation-scheme", true]], "cost() (costevaluator method)": [[3, "pyvrp._pyvrp.CostEvaluator.cost", false]], "cost() (result method)": [[3, "pyvrp.Result.Result.cost", false]], "cost_evaluator() (penaltymanager method)": [[3, "pyvrp.PenaltyManager.PenaltyManager.cost_evaluator", false]], "costevaluator (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.CostEvaluator", false]], "count() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.count", false]], "data() (model method)": [[3, "pyvrp.Model.Model.data", false]], "delivery (client attribute)": [[3, "pyvrp._pyvrp.Client.delivery", false]], "delivery() (route method)": [[3, "pyvrp._pyvrp.Route.delivery", false]], "delivery() (trip method)": [[3, "pyvrp._pyvrp.Trip.delivery", false]], "demand_section": [[11, "term-DEMAND_SECTION", true]], "depot (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.Depot", false]], "depot_section": [[11, "term-DEPOT_SECTION", true]], "depots() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.depots", false]], "dimension": [[11, "term-DIMENSION", true]], "dist_penalty() (costevaluator method)": [[3, "pyvrp._pyvrp.CostEvaluator.dist_penalty", false]], "distance() (route method)": [[3, "pyvrp._pyvrp.Route.distance", false]], "distance() (solution method)": [[3, "pyvrp._pyvrp.Solution.distance", false]], "distance() (trip method)": [[3, "pyvrp._pyvrp.Trip.distance", false]], "distance_cost() (route method)": [[3, "pyvrp._pyvrp.Route.distance_cost", false]], "distance_cost() (solution method)": [[3, "pyvrp._pyvrp.Solution.distance_cost", false]], "distance_matrices() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.distance_matrices", false]], "distance_matrix() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.distance_matrix", false]], "duration() (route method)": [[3, "pyvrp._pyvrp.Route.duration", false]], "duration() (solution method)": [[3, "pyvrp._pyvrp.Solution.duration", false]], "duration_cost() (route method)": [[3, "pyvrp._pyvrp.Route.duration_cost", false]], "duration_cost() (solution method)": [[3, "pyvrp._pyvrp.Solution.duration_cost", false]], "duration_matrices() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.duration_matrices", false]], "duration_matrix() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.duration_matrix", false]], "dynamicbitset (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.DynamicBitset", false]], "edge (class in pyvrp.model)": [[3, "pyvrp.Model.Edge", false]], "edge_weight_format": [[11, "term-EDGE_WEIGHT_FORMAT", true]], "edge_weight_section": [[11, "term-EDGE_WEIGHT_SECTION", true]], "edge_weight_type": [[11, "term-EDGE_WEIGHT_TYPE", true]], "end_depot (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.end_depot", false]], "end_depot() (route method)": [[3, "pyvrp._pyvrp.Route.end_depot", false]], "end_depot() (trip method)": [[3, "pyvrp._pyvrp.Trip.end_depot", false]], "end_service (scheduledvisit attribute)": [[3, "pyvrp._pyvrp.ScheduledVisit.end_service", false]], "end_time() (route method)": [[3, "pyvrp._pyvrp.Route.end_time", false]], "excess_distance() (route method)": [[3, "pyvrp._pyvrp.Route.excess_distance", false]], "excess_distance() (solution method)": [[3, "pyvrp._pyvrp.Solution.excess_distance", false]], "excess_load() (route method)": [[3, "pyvrp._pyvrp.Route.excess_load", false]], "excess_load() (solution method)": [[3, "pyvrp._pyvrp.Solution.excess_load", false]], "excess_load() (trip method)": [[3, "pyvrp._pyvrp.Trip.excess_load", false]], "exchange10 (class in pyvrp.search._search)": [[5, "pyvrp.search._search.Exchange10", false]], "exchange11 (class in pyvrp.search._search)": [[5, "pyvrp.search._search.Exchange11", false]], "exchange20 (class in pyvrp.search._search)": [[5, "pyvrp.search._search.Exchange20", false]], "exchange21 (class in pyvrp.search._search)": [[5, "pyvrp.search._search.Exchange21", false]], "exchange22 (class in pyvrp.search._search)": [[5, "pyvrp.search._search.Exchange22", false]], "exchange30 (class in pyvrp.search._search)": [[5, "pyvrp.search._search.Exchange30", false]], "exchange31 (class in pyvrp.search._search)": [[5, "pyvrp.search._search.Exchange31", false]], "exchange32 (class in pyvrp.search._search)": [[5, "pyvrp.search._search.Exchange32", false]], "exchange33 (class in pyvrp.search._search)": [[5, "pyvrp.search._search.Exchange33", false]], "firstfeasible (class in pyvrp.stop.firstfeasible)": [[6, "pyvrp.stop.FirstFeasible.FirstFeasible", false]], "fixed_cost (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.fixed_cost", false]], "fixed_vehicle_cost() (solution method)": [[3, "pyvrp._pyvrp.Solution.fixed_vehicle_cost", false]], "from_csv() (statistics class method)": [[3, "pyvrp.Statistics.Statistics.from_csv", false]], "from_data() (model class method)": [[3, "pyvrp.Model.Model.from_data", false]], "from_file() (solveparams class method)": [[3, "pyvrp.solve.SolveParams.from_file", false]], "generation_size (populationparams attribute)": [[3, "pyvrp.Population.PopulationParams.generation_size", false]], "geneticalgorithm (class in pyvrp.geneticalgorithm)": [[3, "pyvrp.GeneticAlgorithm.GeneticAlgorithm", false]], "geneticalgorithmparams (class in pyvrp.geneticalgorithm)": [[3, "pyvrp.GeneticAlgorithm.GeneticAlgorithmParams", false]], "greedy_repair() (in module pyvrp.repair._repair)": [[4, "pyvrp.repair._repair.greedy_repair", false]], "group (client attribute)": [[3, "pyvrp._pyvrp.Client.group", false]], "group() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.group", false]], "groups (model property)": [[3, "pyvrp.Model.Model.groups", false]], "groups() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.groups", false]], "has_excess_distance() (route method)": [[3, "pyvrp._pyvrp.Route.has_excess_distance", false]], "has_excess_distance() (solution method)": [[3, "pyvrp._pyvrp.Solution.has_excess_distance", false]], "has_excess_load() (route method)": [[3, "pyvrp._pyvrp.Route.has_excess_load", false]], "has_excess_load() (solution method)": [[3, "pyvrp._pyvrp.Solution.has_excess_load", false]], "has_excess_load() (trip method)": [[3, "pyvrp._pyvrp.Trip.has_excess_load", false]], "has_time_warp() (route method)": [[3, "pyvrp._pyvrp.Route.has_time_warp", false]], "has_time_warp() (solution method)": [[3, "pyvrp._pyvrp.Solution.has_time_warp", false]], "how do i compile pyvrp from source on windows machines?": [[19, "term-How-do-I-compile-PyVRP-from-source-on-Windows-machines", true]], "how do i install the latest pyvrp from main without having to compile things myself?": [[19, "term-How-do-I-install-the-latest-PyVRP-from-main-without-having-to-compile-things-myself", true]], "how do i model vehicle load or service duration at the depots?": [[19, "term-How-do-I-model-vehicle-load-or-service-duration-at-the-depots", true]], "init_from() (penaltymanager class method)": [[3, "pyvrp.PenaltyManager.PenaltyManager.init_from", false]], "initial_load (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.initial_load", false]], "intensify() (localsearch method)": [[5, "pyvrp.search.LocalSearch.LocalSearch.intensify", false]], "is_complete() (solution method)": [[3, "pyvrp._pyvrp.Solution.is_complete", false]], "is_feasible() (result method)": [[3, "pyvrp.Result.Result.is_feasible", false]], "is_feasible() (route method)": [[3, "pyvrp._pyvrp.Route.is_feasible", false]], "is_feasible() (solution method)": [[3, "pyvrp._pyvrp.Solution.is_feasible", false]], "is_group_feasible() (solution method)": [[3, "pyvrp._pyvrp.Solution.is_group_feasible", false]], "lb_diversity (populationparams attribute)": [[3, "pyvrp.Population.PopulationParams.lb_diversity", false]], "linehaul_section": [[11, "term-LINEHAUL_SECTION", true]], "load() (trip method)": [[3, "pyvrp._pyvrp.Trip.load", false]], "load_penalty() (costevaluator method)": [[3, "pyvrp._pyvrp.CostEvaluator.load_penalty", false]], "localsearch (class in pyvrp.search.localsearch)": [[5, "pyvrp.search.LocalSearch.LocalSearch", false]], "location (scheduledvisit attribute)": [[3, "pyvrp._pyvrp.ScheduledVisit.location", false]], "location() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.location", false]], "locations (model property)": [[3, "pyvrp.Model.Model.locations", false]], "make_random() (solution method)": [[3, "pyvrp._pyvrp.Solution.make_random", false]], "max() (randomnumbergenerator method)": [[3, "pyvrp._pyvrp.RandomNumberGenerator.max", false]], "max_distance (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.max_distance", false]], "max_duration (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.max_duration", false]], "max_pop_size (populationparams property)": [[3, "pyvrp.Population.PopulationParams.max_pop_size", false]], "max_reloads (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.max_reloads", false]], "maxiterations (class in pyvrp.stop.maxiterations)": [[6, "pyvrp.stop.MaxIterations.MaxIterations", false]], "maxruntime (class in pyvrp.stop.maxruntime)": [[6, "pyvrp.stop.MaxRuntime.MaxRuntime", false]], "min() (randomnumbergenerator method)": [[3, "pyvrp._pyvrp.RandomNumberGenerator.min", false]], "min_pop_size (populationparams attribute)": [[3, "pyvrp.Population.PopulationParams.min_pop_size", false]], "minimise_fleet() (in module pyvrp.minimise_fleet)": [[3, "pyvrp.minimise_fleet.minimise_fleet", false]], "model (class in pyvrp.model)": [[3, "pyvrp.Model.Model", false]], "module": [[0, "module-pyvrp.crossover", false], [0, "module-pyvrp.crossover.ordered_crossover", false], [0, "module-pyvrp.crossover.selective_route_exchange", false], [1, "module-pyvrp.diversity", false], [1, "module-pyvrp.diversity._diversity", false], [2, "module-pyvrp.plotting", false], [2, "module-pyvrp.plotting.plot_coordinates", false], [2, "module-pyvrp.plotting.plot_demands", false], [2, "module-pyvrp.plotting.plot_diversity", false], [2, "module-pyvrp.plotting.plot_instance", false], [2, "module-pyvrp.plotting.plot_objectives", false], [2, "module-pyvrp.plotting.plot_result", false], [2, "module-pyvrp.plotting.plot_route_schedule", false], [2, "module-pyvrp.plotting.plot_runtimes", false], [2, "module-pyvrp.plotting.plot_solution", false], [2, "module-pyvrp.plotting.plot_time_windows", false], [3, "module-pyvrp", false], [3, "module-pyvrp.GeneticAlgorithm", false], [3, "module-pyvrp.Model", false], [3, "module-pyvrp.PenaltyManager", false], [3, "module-pyvrp.Population", false], [3, "module-pyvrp.Result", false], [3, "module-pyvrp.Statistics", false], [3, "module-pyvrp._pyvrp", false], [3, "module-pyvrp.exceptions", false], [3, "module-pyvrp.minimise_fleet", false], [3, "module-pyvrp.read", false], [3, "module-pyvrp.show_versions", false], [3, "module-pyvrp.solve", false], [4, "module-pyvrp.repair", false], [4, "module-pyvrp.repair._repair", false], [5, "module-pyvrp.search", false], [5, "module-pyvrp.search.LocalSearch", false], [5, "module-pyvrp.search.SearchMethod", false], [5, "module-pyvrp.search._search", false], [5, "module-pyvrp.search.neighbourhood", false], [6, "module-pyvrp.stop", false], [6, "module-pyvrp.stop.FirstFeasible", false], [6, "module-pyvrp.stop.MaxIterations", false], [6, "module-pyvrp.stop.MaxRuntime", false], [6, "module-pyvrp.stop.MultipleCriteria", false], [6, "module-pyvrp.stop.NoImprovement", false], [6, "module-pyvrp.stop.StoppingCriterion", false]], "multiplecriteria (class in pyvrp.stop.multiplecriteria)": [[6, "pyvrp.stop.MultipleCriteria.MultipleCriteria", false]], "mutually_exclusive (clientgroup attribute)": [[3, "pyvrp._pyvrp.ClientGroup.mutually_exclusive", false]], "mutually_exclusive_group_section": [[11, "term-MUTUALLY_EXCLUSIVE_GROUP_SECTION", true]], "name (client attribute)": [[3, "pyvrp._pyvrp.Client.name", false]], "name (depot attribute)": [[3, "pyvrp._pyvrp.Depot.name", false]], "name (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.name", false]], "nb_close (populationparams attribute)": [[3, "pyvrp.Population.PopulationParams.nb_close", false]], "nb_elite (populationparams attribute)": [[3, "pyvrp.Population.PopulationParams.nb_elite", false]], "nb_granular (neighbourhoodparams attribute)": [[5, "pyvrp.search.neighbourhood.NeighbourhoodParams.nb_granular", false]], "nb_iter_no_improvement (geneticalgorithmparams attribute)": [[3, "pyvrp.GeneticAlgorithm.GeneticAlgorithmParams.nb_iter_no_improvement", false]], "nearest_route_insert() (in module pyvrp.repair._repair)": [[4, "pyvrp.repair._repair.nearest_route_insert", false]], "neighbourhoodparams (class in pyvrp.search.neighbourhood)": [[5, "pyvrp.search.neighbourhood.NeighbourhoodParams", false]], "neighbours() (localsearch method)": [[5, "pyvrp.search.LocalSearch.LocalSearch.neighbours", false]], "neighbours() (solution method)": [[3, "pyvrp._pyvrp.Solution.neighbours", false]], "node_coord_section": [[11, "term-NODE_COORD_SECTION", true]], "nodeoperator (class in pyvrp.search._search)": [[5, "pyvrp.search._search.NodeOperator", false]], "noimprovement (class in pyvrp.stop.noimprovement)": [[6, "pyvrp.stop.NoImprovement.NoImprovement", false]], "none() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.none", false]], "num_available (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.num_available", false]], "num_clients (problemdata property)": [[3, "pyvrp._pyvrp.ProblemData.num_clients", false]], "num_clients() (solution method)": [[3, "pyvrp._pyvrp.Solution.num_clients", false]], "num_depots (problemdata property)": [[3, "pyvrp._pyvrp.ProblemData.num_depots", false]], "num_feasible() (population method)": [[3, "pyvrp.Population.Population.num_feasible", false]], "num_groups (problemdata property)": [[3, "pyvrp._pyvrp.ProblemData.num_groups", false]], "num_infeasible() (population method)": [[3, "pyvrp.Population.Population.num_infeasible", false]], "num_load_dimensions (problemdata property)": [[3, "pyvrp._pyvrp.ProblemData.num_load_dimensions", false]], "num_locations (problemdata property)": [[3, "pyvrp._pyvrp.ProblemData.num_locations", false]], "num_missing_clients() (solution method)": [[3, "pyvrp._pyvrp.Solution.num_missing_clients", false]], "num_profiles (problemdata property)": [[3, "pyvrp._pyvrp.ProblemData.num_profiles", false]], "num_routes() (solution method)": [[3, "pyvrp._pyvrp.Solution.num_routes", false]], "num_trips() (route method)": [[3, "pyvrp._pyvrp.Route.num_trips", false]], "num_trips() (solution method)": [[3, "pyvrp._pyvrp.Solution.num_trips", false]], "num_vehicle_types (problemdata property)": [[3, "pyvrp._pyvrp.ProblemData.num_vehicle_types", false]], "num_vehicles (problemdata property)": [[3, "pyvrp._pyvrp.ProblemData.num_vehicles", false]], "ordered_crossover() (in module pyvrp.crossover.ordered_crossover)": [[0, "pyvrp.crossover.ordered_crossover.ordered_crossover", false]], "penalised cost": [[9, "term-Penalised-cost", true]], "penalised_cost() (costevaluator method)": [[3, "pyvrp._pyvrp.CostEvaluator.penalised_cost", false]], "penalties() (penaltymanager method)": [[3, "pyvrp.PenaltyManager.PenaltyManager.penalties", false]], "penalty_decrease (penaltyparams attribute)": [[3, "pyvrp.PenaltyManager.PenaltyParams.penalty_decrease", false]], "penalty_increase (penaltyparams attribute)": [[3, "pyvrp.PenaltyManager.PenaltyParams.penalty_increase", false]], "penaltyboundwarning": [[3, "pyvrp.exceptions.PenaltyBoundWarning", false]], "penaltymanager (class in pyvrp.penaltymanager)": [[3, "pyvrp.PenaltyManager.PenaltyManager", false]], "penaltyparams (class in pyvrp.penaltymanager)": [[3, "pyvrp.PenaltyManager.PenaltyParams", false]], "pickup (client attribute)": [[3, "pyvrp._pyvrp.Client.pickup", false]], "pickup() (route method)": [[3, "pyvrp._pyvrp.Route.pickup", false]], "pickup() (trip method)": [[3, "pyvrp._pyvrp.Trip.pickup", false]], "plot_coordinates() (in module pyvrp.plotting.plot_coordinates)": [[2, "pyvrp.plotting.plot_coordinates.plot_coordinates", false]], "plot_demands() (in module pyvrp.plotting.plot_demands)": [[2, "pyvrp.plotting.plot_demands.plot_demands", false]], "plot_diversity() (in module pyvrp.plotting.plot_diversity)": [[2, "pyvrp.plotting.plot_diversity.plot_diversity", false]], "plot_instance() (in module pyvrp.plotting.plot_instance)": [[2, "pyvrp.plotting.plot_instance.plot_instance", false]], "plot_objectives() (in module pyvrp.plotting.plot_objectives)": [[2, "pyvrp.plotting.plot_objectives.plot_objectives", false]], "plot_result() (in module pyvrp.plotting.plot_result)": [[2, "pyvrp.plotting.plot_result.plot_result", false]], "plot_route_schedule() (in module pyvrp.plotting.plot_route_schedule)": [[2, "pyvrp.plotting.plot_route_schedule.plot_route_schedule", false]], "plot_runtimes() (in module pyvrp.plotting.plot_runtimes)": [[2, "pyvrp.plotting.plot_runtimes.plot_runtimes", false]], "plot_solution() (in module pyvrp.plotting.plot_solution)": [[2, "pyvrp.plotting.plot_solution.plot_solution", false]], "plot_time_windows() (in module pyvrp.plotting.plot_time_windows)": [[2, "pyvrp.plotting.plot_time_windows.plot_time_windows", false]], "population (class in pyvrp.population)": [[3, "pyvrp.Population.Population", false]], "populationparams (class in pyvrp.population)": [[3, "pyvrp.Population.PopulationParams", false]], "prize (client attribute)": [[3, "pyvrp._pyvrp.Client.prize", false]], "prize_section": [[11, "term-PRIZE_SECTION", true]], "prizes() (route method)": [[3, "pyvrp._pyvrp.Route.prizes", false]], "prizes() (solution method)": [[3, "pyvrp._pyvrp.Solution.prizes", false]], "prizes() (trip method)": [[3, "pyvrp._pyvrp.Trip.prizes", false]], "problemdata (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.ProblemData", false]], "profile (class in pyvrp.model)": [[3, "pyvrp.Model.Profile", false]], "profile (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.profile", false]], "profiles (model property)": [[3, "pyvrp.Model.Model.profiles", false]], "pyvrp": [[3, "module-pyvrp", false]], "pyvrp._pyvrp": [[3, "module-pyvrp._pyvrp", false]], "pyvrp.crossover": [[0, "module-pyvrp.crossover", false]], "pyvrp.crossover.ordered_crossover": [[0, "module-pyvrp.crossover.ordered_crossover", false]], "pyvrp.crossover.selective_route_exchange": [[0, "module-pyvrp.crossover.selective_route_exchange", false]], "pyvrp.diversity": [[1, "module-pyvrp.diversity", false]], "pyvrp.diversity._diversity": [[1, "module-pyvrp.diversity._diversity", false]], "pyvrp.exceptions": [[3, "module-pyvrp.exceptions", false]], "pyvrp.geneticalgorithm": [[3, "module-pyvrp.GeneticAlgorithm", false]], "pyvrp.minimise_fleet": [[3, "module-pyvrp.minimise_fleet", false]], "pyvrp.model": [[3, "module-pyvrp.Model", false]], "pyvrp.penaltymanager": [[3, "module-pyvrp.PenaltyManager", false]], "pyvrp.plotting": [[2, "module-pyvrp.plotting", false]], "pyvrp.plotting.plot_coordinates": [[2, "module-pyvrp.plotting.plot_coordinates", false]], "pyvrp.plotting.plot_demands": [[2, "module-pyvrp.plotting.plot_demands", false]], "pyvrp.plotting.plot_diversity": [[2, "module-pyvrp.plotting.plot_diversity", false]], "pyvrp.plotting.plot_instance": [[2, "module-pyvrp.plotting.plot_instance", false]], "pyvrp.plotting.plot_objectives": [[2, "module-pyvrp.plotting.plot_objectives", false]], "pyvrp.plotting.plot_result": [[2, "module-pyvrp.plotting.plot_result", false]], "pyvrp.plotting.plot_route_schedule": [[2, "module-pyvrp.plotting.plot_route_schedule", false]], "pyvrp.plotting.plot_runtimes": [[2, "module-pyvrp.plotting.plot_runtimes", false]], "pyvrp.plotting.plot_solution": [[2, "module-pyvrp.plotting.plot_solution", false]], "pyvrp.plotting.plot_time_windows": [[2, "module-pyvrp.plotting.plot_time_windows", false]], "pyvrp.population": [[3, "module-pyvrp.Population", false]], "pyvrp.read": [[3, "module-pyvrp.read", false]], "pyvrp.repair": [[4, "module-pyvrp.repair", false]], "pyvrp.repair._repair": [[4, "module-pyvrp.repair._repair", false]], "pyvrp.result": [[3, "module-pyvrp.Result", false]], "pyvrp.search": [[5, "module-pyvrp.search", false]], "pyvrp.search._search": [[5, "module-pyvrp.search._search", false]], "pyvrp.search.localsearch": [[5, "module-pyvrp.search.LocalSearch", false]], "pyvrp.search.neighbourhood": [[5, "module-pyvrp.search.neighbourhood", false]], "pyvrp.search.searchmethod": [[5, "module-pyvrp.search.SearchMethod", false]], "pyvrp.show_versions": [[3, "module-pyvrp.show_versions", false]], "pyvrp.solve": [[3, "module-pyvrp.solve", false]], "pyvrp.statistics": [[3, "module-pyvrp.Statistics", false]], "pyvrp.stop": [[6, "module-pyvrp.stop", false]], "pyvrp.stop.firstfeasible": [[6, "module-pyvrp.stop.FirstFeasible", false]], "pyvrp.stop.maxiterations": [[6, "module-pyvrp.stop.MaxIterations", false]], "pyvrp.stop.maxruntime": [[6, "module-pyvrp.stop.MaxRuntime", false]], "pyvrp.stop.multiplecriteria": [[6, "module-pyvrp.stop.MultipleCriteria", false]], "pyvrp.stop.noimprovement": [[6, "module-pyvrp.stop.NoImprovement", false]], "pyvrp.stop.stoppingcriterion": [[6, "module-pyvrp.stop.StoppingCriterion", false]], "rand() (randomnumbergenerator method)": [[3, "pyvrp._pyvrp.RandomNumberGenerator.rand", false]], "randint() (randomnumbergenerator method)": [[3, "pyvrp._pyvrp.RandomNumberGenerator.randint", false]], "randomnumbergenerator (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.RandomNumberGenerator", false]], "read() (in module pyvrp.read)": [[3, "pyvrp.read.read", false]], "read_solution() (in module pyvrp.read)": [[3, "pyvrp.read.read_solution", false]], "register() (penaltymanager method)": [[3, "pyvrp.PenaltyManager.PenaltyManager.register", false]], "release_time (client attribute)": [[3, "pyvrp._pyvrp.Client.release_time", false]], "release_time() (route method)": [[3, "pyvrp._pyvrp.Route.release_time", false]], "release_time() (trip method)": [[3, "pyvrp._pyvrp.Trip.release_time", false]], "release_time_section": [[11, "term-RELEASE_TIME_SECTION", true]], "reload_depots (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.reload_depots", false]], "repair_booster (penaltyparams attribute)": [[3, "pyvrp.PenaltyManager.PenaltyParams.repair_booster", false]], "repair_probability (geneticalgorithmparams attribute)": [[3, "pyvrp.GeneticAlgorithm.GeneticAlgorithmParams.repair_probability", false]], "replace() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.replace", false]], "replace() (vehicletype method)": [[3, "pyvrp._pyvrp.VehicleType.replace", false]], "required (client attribute)": [[3, "pyvrp._pyvrp.Client.required", false]], "required (clientgroup attribute)": [[3, "pyvrp._pyvrp.ClientGroup.required", false]], "reset() (dynamicbitset method)": [[3, "pyvrp._pyvrp.DynamicBitset.reset", false]], "result (class in pyvrp.result)": [[3, "pyvrp.Result.Result", false]], "route (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.Route", false]], "route segment": [[9, "term-Route-segment", true]], "routeoperator (class in pyvrp.search._search)": [[5, "pyvrp.search._search.RouteOperator", false]], "routes() (solution method)": [[3, "pyvrp._pyvrp.Solution.routes", false]], "run() (geneticalgorithm method)": [[3, "pyvrp.GeneticAlgorithm.GeneticAlgorithm.run", false]], "scalingwarning": [[3, "pyvrp.exceptions.ScalingWarning", false]], "schedule() (route method)": [[3, "pyvrp._pyvrp.Route.schedule", false]], "scheduledvisit (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.ScheduledVisit", false]], "search() (localsearch method)": [[5, "pyvrp.search.LocalSearch.LocalSearch.search", false]], "searchmethod (class in pyvrp.search.searchmethod)": [[5, "pyvrp.search.SearchMethod.SearchMethod", false]], "select() (population method)": [[3, "pyvrp.Population.Population.select", false]], "selective_route_exchange() (in module pyvrp.crossover.selective_route_exchange)": [[0, "pyvrp.crossover.selective_route_exchange.selective_route_exchange", false]], "service_duration (client attribute)": [[3, "pyvrp._pyvrp.Client.service_duration", false]], "service_duration (scheduledvisit attribute)": [[3, "pyvrp._pyvrp.ScheduledVisit.service_duration", false]], "service_duration() (route method)": [[3, "pyvrp._pyvrp.Route.service_duration", false]], "service_duration() (trip method)": [[3, "pyvrp._pyvrp.Trip.service_duration", false]], "service_time": [[11, "term-SERVICE_TIME", true]], "service_time_section": [[11, "term-SERVICE_TIME_SECTION", true]], "set_neighbours() (localsearch method)": [[5, "pyvrp.search.LocalSearch.LocalSearch.set_neighbours", false]], "show_versions() (in module pyvrp.show_versions)": [[3, "pyvrp.show_versions.show_versions", false]], "slack() (route method)": [[3, "pyvrp._pyvrp.Route.slack", false]], "solution (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.Solution", false]], "solutions_between_updates (penaltyparams attribute)": [[3, "pyvrp.PenaltyManager.PenaltyParams.solutions_between_updates", false]], "solve() (in module pyvrp.solve)": [[3, "pyvrp.solve.solve", false]], "solve() (model method)": [[3, "pyvrp.Model.Model.solve", false]], "solveparams (class in pyvrp.solve)": [[3, "pyvrp.solve.SolveParams", false]], "start_depot (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.start_depot", false]], "start_depot() (route method)": [[3, "pyvrp._pyvrp.Route.start_depot", false]], "start_depot() (trip method)": [[3, "pyvrp._pyvrp.Trip.start_depot", false]], "start_late (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.start_late", false]], "start_service (scheduledvisit attribute)": [[3, "pyvrp._pyvrp.ScheduledVisit.start_service", false]], "start_time() (route method)": [[3, "pyvrp._pyvrp.Route.start_time", false]], "state() (randomnumbergenerator method)": [[3, "pyvrp._pyvrp.RandomNumberGenerator.state", false]], "statistics (class in pyvrp.statistics)": [[3, "pyvrp.Statistics.Statistics", false]], "stoppingcriterion (class in pyvrp.stop.stoppingcriterion)": [[6, "pyvrp.stop.StoppingCriterion.StoppingCriterion", false]], "summary() (result method)": [[3, "pyvrp.Result.Result.summary", false]], "swaproutes (class in pyvrp.search._search)": [[5, "pyvrp.search._search.SwapRoutes", false]], "swapstar (class in pyvrp.search._search)": [[5, "pyvrp.search._search.SwapStar", false]], "swaptails (class in pyvrp.search._search)": [[5, "pyvrp.search._search.SwapTails", false]], "symmetric_neighbours (neighbourhoodparams attribute)": [[5, "pyvrp.search.neighbourhood.NeighbourhoodParams.symmetric_neighbours", false]], "symmetric_proximity (neighbourhoodparams attribute)": [[5, "pyvrp.search.neighbourhood.NeighbourhoodParams.symmetric_proximity", false]], "target_feasible (penaltyparams attribute)": [[3, "pyvrp.PenaltyManager.PenaltyParams.target_feasible", false]], "time_warp (scheduledvisit attribute)": [[3, "pyvrp._pyvrp.ScheduledVisit.time_warp", false]], "time_warp() (route method)": [[3, "pyvrp._pyvrp.Route.time_warp", false]], "time_warp() (solution method)": [[3, "pyvrp._pyvrp.Solution.time_warp", false]], "time_window_section": [[11, "term-TIME_WINDOW_SECTION", true]], "to_csv() (statistics method)": [[3, "pyvrp.Statistics.Statistics.to_csv", false]], "tournament() (population method)": [[3, "pyvrp.Population.Population.tournament", false]], "travel_duration() (route method)": [[3, "pyvrp._pyvrp.Route.travel_duration", false]], "travel_duration() (trip method)": [[3, "pyvrp._pyvrp.Trip.travel_duration", false]], "trip (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.Trip", false]], "trip (scheduledvisit attribute)": [[3, "pyvrp._pyvrp.ScheduledVisit.trip", false]], "trip() (route method)": [[3, "pyvrp._pyvrp.Route.trip", false]], "triprelocate (class in pyvrp.search._search)": [[5, "pyvrp.search._search.TripRelocate", false]], "trips() (route method)": [[3, "pyvrp._pyvrp.Route.trips", false]], "tspwarning": [[3, "pyvrp.exceptions.TspWarning", false]], "tw_early (client attribute)": [[3, "pyvrp._pyvrp.Client.tw_early", false]], "tw_early (depot attribute)": [[3, "pyvrp._pyvrp.Depot.tw_early", false]], "tw_early (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.tw_early", false]], "tw_late (client attribute)": [[3, "pyvrp._pyvrp.Client.tw_late", false]], "tw_late (depot attribute)": [[3, "pyvrp._pyvrp.Depot.tw_late", false]], "tw_late (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.tw_late", false]], "tw_penalty() (costevaluator method)": [[3, "pyvrp._pyvrp.CostEvaluator.tw_penalty", false]], "ub_diversity (populationparams attribute)": [[3, "pyvrp.Population.PopulationParams.ub_diversity", false]], "uncollected_prizes() (solution method)": [[3, "pyvrp._pyvrp.Solution.uncollected_prizes", false]], "unit_distance_cost (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.unit_distance_cost", false]], "unit_duration_cost (vehicletype attribute)": [[3, "pyvrp._pyvrp.VehicleType.unit_duration_cost", false]], "vehicle_type() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.vehicle_type", false]], "vehicle_type() (route method)": [[3, "pyvrp._pyvrp.Route.vehicle_type", false]], "vehicle_type() (trip method)": [[3, "pyvrp._pyvrp.Trip.vehicle_type", false]], "vehicle_types (model property)": [[3, "pyvrp.Model.Model.vehicle_types", false]], "vehicle_types() (problemdata method)": [[3, "pyvrp._pyvrp.ProblemData.vehicle_types", false]], "vehicles": [[11, "term-VEHICLES", true]], "vehicles_allowed_clients_section": [[11, "term-VEHICLES_ALLOWED_CLIENTS_SECTION", true]], "vehicles_depot_section": [[11, "term-VEHICLES_DEPOT_SECTION", true]], "vehicles_fixed_cost_section": [[11, "term-VEHICLES_FIXED_COST_SECTION", true]], "vehicles_max_distance": [[11, "term-VEHICLES_MAX_DISTANCE", true]], "vehicles_max_distance_section": [[11, "term-VEHICLES_MAX_DISTANCE_SECTION", true]], "vehicles_max_duration": [[11, "term-VEHICLES_MAX_DURATION", true]], "vehicles_max_duration_section": [[11, "term-VEHICLES_MAX_DURATION_SECTION", true]], "vehicles_max_reloads": [[11, "term-VEHICLES_MAX_RELOADS", true]], "vehicles_max_reloads_section": [[11, "term-VEHICLES_MAX_RELOADS_SECTION", true]], "vehicles_reload_depot_section": [[11, "term-VEHICLES_RELOAD_DEPOT_SECTION", true]], "vehicles_unit_distance_cost_section": [[11, "term-VEHICLES_UNIT_DISTANCE_COST_SECTION", true]], "vehicletype (class in pyvrp._pyvrp)": [[3, "pyvrp._pyvrp.VehicleType", false]], "visits() (route method)": [[3, "pyvrp._pyvrp.Route.visits", false]], "visits() (trip method)": [[3, "pyvrp._pyvrp.Trip.visits", false]], "wait_duration (scheduledvisit attribute)": [[3, "pyvrp._pyvrp.ScheduledVisit.wait_duration", false]], "wait_duration() (route method)": [[3, "pyvrp._pyvrp.Route.wait_duration", false]], "weight_time_warp (neighbourhoodparams attribute)": [[5, "pyvrp.search.neighbourhood.NeighbourhoodParams.weight_time_warp", false]], "weight_wait_time (neighbourhoodparams attribute)": [[5, "pyvrp.search.neighbourhood.NeighbourhoodParams.weight_wait_time", false]], "x (client attribute)": [[3, "pyvrp._pyvrp.Client.x", false]], "x (depot attribute)": [[3, "pyvrp._pyvrp.Depot.x", false]], "y (client attribute)": [[3, "pyvrp._pyvrp.Client.y", false]], "y (depot attribute)": [[3, "pyvrp._pyvrp.Depot.y", false]]}, "objects": {"": [[3, 0, 0, 1, "pyvrp", ""]], "pyvrp": [[3, 0, 0, 1, "GeneticAlgorithm", ""], [3, 0, 0, 1, "Model", ""], [3, 0, 0, 1, "PenaltyManager", ""], [3, 0, 0, 1, "Population", ""], [3, 0, 0, 1, "Result", ""], [3, 0, 0, 1, "Statistics", ""], [3, 0, 0, 1, "_pyvrp", ""], [0, 0, 0, 1, "crossover", ""], [1, 0, 0, 1, "diversity", ""], [3, 0, 0, 1, "exceptions", ""], [3, 0, 0, 1, "minimise_fleet", ""], [2, 0, 0, 1, "plotting", ""], [3, 0, 0, 1, "read", ""], [4, 0, 0, 1, "repair", ""], [5, 0, 0, 1, "search", ""], [3, 0, 0, 1, "show_versions", ""], [3, 0, 0, 1, "solve", ""], [6, 0, 0, 1, "stop", ""]], "pyvrp.GeneticAlgorithm": [[3, 1, 1, 0, "GeneticAlgorithm", "Creates a GeneticAlgorithm instance."], [3, 1, 1, 0, "GeneticAlgorithmParams", "Parameters for the genetic algorithm."]], "pyvrp.GeneticAlgorithm.GeneticAlgorithm": [[3, 3, 1, 0, "run", "Runs the genetic algorithm with the provided stopping criterion."]], "pyvrp.GeneticAlgorithm.GeneticAlgorithm.__init__": [[3, 2, 2, 0, "crossover_op", "Crossover operator to use for generating offspring."], [3, 2, 2, 0, "data", "Data object describing the problem to be solved."], [3, 2, 2, 0, "initial_solutions", "Initial solutions to use to initialise the population."], [3, 2, 2, 0, "params", "Genetic algorithm parameters."], [3, 2, 2, 0, "penalty_manager", "Penalty manager to use."], [3, 2, 2, 0, "population", "Population to use."], [3, 2, 2, 0, "rng", "Random number generator."], [3, 2, 2, 0, "search_method", "Search method to use."]], "pyvrp.GeneticAlgorithm.GeneticAlgorithm.run": [[3, 2, 2, 0, "collect_stats", "Whether to collect statistics about the solver's progress."], [3, 2, 2, 0, "display", "Whether to display information about the solver progress."], [3, 2, 2, 0, "stop", "Stopping criterion to use."]], "pyvrp.GeneticAlgorithm.GeneticAlgorithmParams": [[3, 4, 1, 0, "nb_iter_no_improvement", "Number of iterations without improvement before a restart occurs."], [3, 4, 1, 0, "repair_probability", "Probability of repairing an infeasible solution."]], "pyvrp.GeneticAlgorithm.GeneticAlgorithmParams.__init__": [[3, 2, 2, 0, "nb_iter_no_improvement", "Number of iterations without any improvement needed before a restart occurs."], [3, 2, 2, 0, "repair_probability", "Probability (in [0, 1]) of repairing an infeasible solution. If the reparation makes the solution feasible, it is also added to the population in the same iteration."]], "pyvrp.Model": [[3, 1, 1, 0, "Edge", "Stores an edge connecting two locations."], [3, 1, 1, 0, "Model", "A simple interface for modelling vehicle routing problems with PyVRP."], [3, 1, 1, 0, "Profile", "Stores a routing profile."]], "pyvrp.Model.Edge.__init__": [[3, 2, 2, "pyvrp.Model.Edge", "distance", "Stores an edge connecting two locations."], [3, 2, 2, "pyvrp.Model.Edge", "duration", "Stores an edge connecting two locations."], [3, 2, 2, "pyvrp.Model.Edge", "frm", "Stores an edge connecting two locations."], [3, 2, 2, "pyvrp.Model.Edge", "to", "Stores an edge connecting two locations."]], "pyvrp.Model.Model": [[3, 3, 1, 0, "add_client", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 3, 1, 0, "add_client_group", "Adds a new, possibly optional, client group to the model. Returns the created group."], [3, 3, 1, 0, "add_depot", "Adds a depot with the given attributes to the model. Returns the created Depot instance."], [3, 3, 1, 0, "add_edge", "Adds an edge (i, j) between frm (i) and to (j). The edge can be given distance and duration attributes. Distance is required, but the default duration is zero. Returns the created edge."], [3, 3, 1, 0, "add_profile", "Adds a new routing profile to the model."], [3, 3, 1, 0, "add_vehicle_type", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 3, 1, 0, "data", "Creates and returns a ProblemData instance from this model's attributes."], [3, 3, 1, 0, "from_data", "Constructs a model instance from the given data."], [3, 5, 1, 0, "groups", "Returns all client groups currently in the model."], [3, 5, 1, 0, "locations", "Returns all locations (depots and clients) in the current model. The clients in the routes of the solution returned by solve() can be used to index these locations."], [3, 5, 1, 0, "profiles", "Returns all routing profiles currently in the model."], [3, 3, 1, 0, "solve", "Solve this model."], [3, 5, 1, 0, "vehicle_types", "Returns the vehicle types in the current model. The routes of the solution returned by solve() have a property vehicle_type() that can be used to index these vehicle types."]], "pyvrp.Model.Model.add_client": [[3, 2, 2, "pyvrp.Model.Model.add_client", "delivery", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "group", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "name", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "pickup", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "prize", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "release_time", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "required", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "service_duration", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "tw_early", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "tw_late", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "x", "Adds a client with the given attributes to the model. Returns the created Client instance."], [3, 2, 2, "pyvrp.Model.Model.add_client", "y", "Adds a client with the given attributes to the model. Returns the created Client instance."]], "pyvrp.Model.Model.add_client_group": [[3, 2, 2, "pyvrp.Model.Model.add_client_group", "required", "Adds a new, possibly optional, client group to the model. Returns the created group."]], "pyvrp.Model.Model.add_depot": [[3, 2, 2, "pyvrp.Model.Model.add_depot", "name", "Adds a depot with the given attributes to the model. Returns the created Depot instance."], [3, 2, 2, "pyvrp.Model.Model.add_depot", "tw_early", "Adds a depot with the given attributes to the model. Returns the created Depot instance."], [3, 2, 2, "pyvrp.Model.Model.add_depot", "tw_late", "Adds a depot with the given attributes to the model. Returns the created Depot instance."], [3, 2, 2, "pyvrp.Model.Model.add_depot", "x", "Adds a depot with the given attributes to the model. Returns the created Depot instance."], [3, 2, 2, "pyvrp.Model.Model.add_depot", "y", "Adds a depot with the given attributes to the model. Returns the created Depot instance."]], "pyvrp.Model.Model.add_edge": [[3, 2, 2, "pyvrp.Model.Model.add_edge", "distance", "Adds an edge (i, j) between frm (i) and to (j). The edge can be given distance and duration attributes. Distance is required, but the default duration is zero. Returns the created edge."], [3, 2, 2, "pyvrp.Model.Model.add_edge", "duration", "Adds an edge (i, j) between frm (i) and to (j). The edge can be given distance and duration attributes. Distance is required, but the default duration is zero. Returns the created edge."], [3, 2, 2, "pyvrp.Model.Model.add_edge", "frm", "Adds an edge (i, j) between frm (i) and to (j). The edge can be given distance and duration attributes. Distance is required, but the default duration is zero. Returns the created edge."], [3, 2, 2, "pyvrp.Model.Model.add_edge", "profile", "Adds an edge (i, j) between frm (i) and to (j). The edge can be given distance and duration attributes. Distance is required, but the default duration is zero. Returns the created edge."], [3, 2, 2, "pyvrp.Model.Model.add_edge", "to", "Adds an edge (i, j) between frm (i) and to (j). The edge can be given distance and duration attributes. Distance is required, but the default duration is zero. Returns the created edge."]], "pyvrp.Model.Model.add_vehicle_type": [[3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "capacity", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "end_depot", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "fixed_cost", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "initial_load", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "max_distance", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "max_duration", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "max_reloads", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "name", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "num_available", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "profile", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "reload_depots", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "start_depot", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "start_late", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "tw_early", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "tw_late", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "unit_distance_cost", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."], [3, 2, 2, "pyvrp.Model.Model.add_vehicle_type", "unit_duration_cost", "Adds a vehicle type with the given attributes to the model. Returns the created VehicleType instance."]], "pyvrp.Model.Model.from_data": [[3, 2, 2, 0, "data", "Problem data to feed into the model."]], "pyvrp.Model.Model.solve": [[3, 2, 2, 0, "collect_stats", "Whether to collect statistics about the solver's progress."], [3, 2, 2, 0, "display", "Whether to display information about the solver progress."], [3, 2, 2, 0, "params", "Solver parameters to use."], [3, 2, 2, 0, "seed", "Seed value to use for the random number stream."], [3, 2, 2, 0, "stop", "Stopping criterion to use."]], "pyvrp.Model.Profile": [[3, 3, 1, 0, "add_edge", "Adds a new edge to this routing profile."]], "pyvrp.Model.Profile.add_edge": [[3, 2, 2, "pyvrp.Model.Profile.add_edge", "distance", "Adds a new edge to this routing profile."], [3, 2, 2, "pyvrp.Model.Profile.add_edge", "duration", "Adds a new edge to this routing profile."], [3, 2, 2, "pyvrp.Model.Profile.add_edge", "frm", "Adds a new edge to this routing profile."], [3, 2, 2, "pyvrp.Model.Profile.add_edge", "to", "Adds a new edge to this routing profile."]], "pyvrp.PenaltyManager": [[3, 1, 1, 0, "PenaltyManager", "Creates a PenaltyManager instance."], [3, 1, 1, 0, "PenaltyParams", "The penalty manager parameters."]], "pyvrp.PenaltyManager.PenaltyManager": [[3, 3, 1, 0, "booster_cost_evaluator", "Get a cost evaluator using the boosted current penalty values."], [3, 3, 1, 0, "cost_evaluator", "Get a cost evaluator using the current penalty values."], [3, 3, 1, 0, "init_from", "Initialises from the given data instance and parameter object. The initial penalty values are computed from the problem data."], [3, 3, 1, 0, "penalties", "Returns the current penalty values."], [3, 3, 1, 0, "register", "Registers the feasibility dimensions of the given solution."]], "pyvrp.PenaltyManager.PenaltyManager.__init__": [[3, 2, 2, 0, "initial_penalties", "Initial penalty values for units of load (idx 0), duration (1), and distance (2) violations."], [3, 2, 2, 0, "params", "PenaltyManager parameters."]], "pyvrp.PenaltyManager.PenaltyManager.init_from": [[3, 2, 2, 0, "data", "Data instance to use when computing penalty values."], [3, 2, 2, 0, "params", "PenaltyManager parameters."]], "pyvrp.PenaltyManager.PenaltyManager.register": [[3, 2, 2, "pyvrp.PenaltyManager.PenaltyManager.register", "sol", "Registers the feasibility dimensions of the given solution."]], "pyvrp.PenaltyManager.PenaltyParams": [[3, 4, 1, 0, "penalty_decrease", "Amount p_d \\in [0, 1] by which the current penalties are decreased when sufficient feasible solutions (see target_feasible) have been found amongst the most recent registrations."], [3, 4, 1, 0, "penalty_increase", "Amount p_i \\ge 1 by which the current penalties are increased when insufficient feasible solutions (see target_feasible) have been found amongst the most recent registrations."], [3, 4, 1, 0, "repair_booster", "A repair booster value."], [3, 4, 1, 0, "solutions_between_updates", "Number of feasibility registrations between penalty value updates."], [3, 4, 1, 0, "target_feasible", "Target percentage p_f \\in [0, 1] of feasible registrations in the last solutions_between_updates registrations."]], "pyvrp.PenaltyManager.PenaltyParams.__init__": [[3, 2, 2, 0, "penalty_decrease", "Amount p_d \\in [0, 1] by which the current penalties are decreased when sufficient feasible solutions (see target_feasible) have been found amongst the most recent registrations."], [3, 2, 2, 0, "penalty_increase", "Amount p_i \\ge 1 by which the current penalties are increased when insufficient feasible solutions (see target_feasible) have been found amongst the most recent registrations."], [3, 2, 2, 0, "repair_booster", "A repair booster value r \\ge 1."], [3, 2, 2, 0, "solutions_between_updates", "Number of feasibility registrations between penalty value updates."], [3, 2, 2, 0, "target_feasible", "Target percentage p_f \\in [0, 1] of feasible registrations in the last solutions_between_updates registrations."]], "pyvrp.Population": [[3, 1, 1, 0, "Population", "Creates a Population instance."], [3, 1, 1, 0, "PopulationParams", "Parameter configuration for the Population."]], "pyvrp.Population.Population": [[3, 3, 1, 0, "__iter__", "Iterates over the solutions contained in this population."], [3, 3, 1, 0, "__len__", "Returns the current population size."], [3, 3, 1, 0, "add", "Inserts the given solution in the appropriate feasible or infeasible (sub)population."], [3, 3, 1, 0, "clear", "Clears the population by removing all solutions currently in the population."], [3, 3, 1, 0, "num_feasible", "Returns the number of feasible solutions in the population."], [3, 3, 1, 0, "num_infeasible", "Returns the number of infeasible solutions in the population."], [3, 3, 1, 0, "select", "Selects two (if possible non-identical) parents by tournament, subject to a diversity restriction."], [3, 3, 1, 0, "tournament", "Selects a solution from this population by k-ary tournament, based on the (internal) fitness values of the selected solutions."]], "pyvrp.Population.Population.__init__": [[3, 2, 2, 0, "diversity_op", "Operator to use to determine pairwise diversity between solutions."], [3, 2, 2, 0, "params", "Population parameters."]], "pyvrp.Population.Population.add": [[3, 2, 2, 0, "cost_evaluator", "CostEvaluator to use to compute the cost."], [3, 2, 2, 0, "solution", "Solution to add to the population."]], "pyvrp.Population.Population.select": [[3, 2, 2, 0, "cost_evaluator", "Cost evaluator to use when computing the fitness."], [3, 2, 2, 0, "k", "The number of solutions to draw for the tournament."], [3, 2, 2, 0, "rng", "Random number generator."]], "pyvrp.Population.Population.tournament": [[3, 2, 2, 0, "cost_evaluator", "Cost evaluator to use when computing the fitness."], [3, 2, 2, 0, "k", "The number of solutions to draw for the tournament."], [3, 2, 2, 0, "rng", "Random number generator."]], "pyvrp.Population.PopulationParams": [[3, 4, 1, 0, "generation_size", "The size of a single generation, that is, the number of new solutions inserted into a subpopulation between survivor selections."], [3, 4, 1, 0, "lb_diversity", "A lower bound on the diversity of the solutions selected for tournament. See select() for details."], [3, 5, 1, 0, "max_pop_size", "Returns the maximum subpopulation size."], [3, 4, 1, 0, "min_pop_size", "Minimum subpopulation size. This is the size of the subpopulation after survivor selection."], [3, 4, 1, 0, "nb_close", "Number of close solutions. These are used to determine similarity between solutions, which is an important component of fitness."], [3, 4, 1, 0, "nb_elite", "Number of elite solutions. This number of fittest solutions are always survivors."], [3, 4, 1, 0, "ub_diversity", "An upper bound on the diversity of the solutions selected for tournament. See select() for details."]], "pyvrp.Population.PopulationParams.__init__": [[3, 2, 2, "pyvrp.Population.PopulationParams", "generation_size", "Parameter configuration for the Population."], [3, 2, 2, "pyvrp.Population.PopulationParams", "lb_diversity", "Parameter configuration for the Population."], [3, 2, 2, "pyvrp.Population.PopulationParams", "min_pop_size", "Parameter configuration for the Population."], [3, 2, 2, "pyvrp.Population.PopulationParams", "nb_close", "Parameter configuration for the Population."], [3, 2, 2, "pyvrp.Population.PopulationParams", "nb_elite", "Parameter configuration for the Population."], [3, 2, 2, "pyvrp.Population.PopulationParams", "ub_diversity", "Parameter configuration for the Population."]], "pyvrp.Result": [[3, 1, 1, 0, "Result", "Stores the outcomes of a single run. An instance of this class is returned once the GeneticAlgorithm completes."]], "pyvrp.Result.Result": [[3, 3, 1, 0, "cost", "Returns the cost (objective) value of the best solution. Returns inf if the best solution is infeasible."], [3, 3, 1, 0, "is_feasible", "Returns whether the best solution is feasible."], [3, 3, 1, 0, "summary", "Returns a nicely formatted result summary."]], "pyvrp.Result.Result.__init__": [[3, 2, 2, 0, "best", "The best observed solution."], [3, 2, 2, 0, "num_iterations", "Number of iterations performed by the genetic algorithm."], [3, 2, 2, 0, "runtime", "Total runtime of the main genetic algorithm loop."], [3, 2, 2, 0, "stats", "A Statistics object containing runtime statistics."]], "pyvrp.Statistics": [[3, 1, 1, 0, "Statistics", "The Statistics object tracks various (population-level) statistics of genetic algorithm runs. This can be helpful in analysing the algorithm's performance."]], "pyvrp.Statistics.Statistics": [[3, 3, 1, 0, "collect_from", "Collects statistics from the given population object."], [3, 3, 1, 0, "from_csv", "Reads a Statistics object from the CSV file at the given filesystem location."], [3, 3, 1, 0, "to_csv", "Writes this Statistics object to the given location, as a CSV file."]], "pyvrp.Statistics.Statistics.__init__": [[3, 2, 2, 0, "collect_stats", "Whether to collect statistics at all."]], "pyvrp.Statistics.Statistics.collect_from": [[3, 2, 2, 0, "cost_evaluator", "CostEvaluator used to compute costs for solutions."], [3, 2, 2, 0, "population", "Population instance to collect statistics from."]], "pyvrp.Statistics.Statistics.from_csv": [[3, 2, 2, 0, "delimiter", "Value separator."], [3, 2, 2, 0, "kwargs", "Additional keyword arguments."], [3, 2, 2, 0, "where", "Filesystem location to read from."]], "pyvrp.Statistics.Statistics.to_csv": [[3, 2, 2, 0, "delimiter", "Value separator."], [3, 2, 2, 0, "kwargs", "Additional keyword arguments."], [3, 2, 2, 0, "quoting", "Quoting strategy."], [3, 2, 2, 0, "where", "Filesystem location to write to."]], "pyvrp._pyvrp": [[3, 1, 1, 0, "Client", "Simple data object storing all client data as properties. See also ../setup/concepts for further information about these properties."], [3, 1, 1, 0, "ClientGroup", "A client group that imposes additional restrictions on visits to clients in the group."], [3, 1, 1, 0, "CostEvaluator", "Creates a CostEvaluator instance."], [3, 1, 1, 0, "Depot", "Simple data object storing all depot data as (read-only) properties."], [3, 1, 1, 0, "DynamicBitset", "A simple dynamic bitset implementation. This class functions as a fast set for membership checks on the integers. That is particularly useful for testing if e.g. clients are in a solution or not."], [3, 1, 1, 0, "ProblemData", "Creates a problem data instance. This instance contains all information needed to solve the vehicle routing problem."], [3, 1, 1, 0, "RandomNumberGenerator", "This class implements a XOR-shift pseudo-random number generator (RNG). It generates the next number of a sequence by repeatedly taking the 'exclusive or' (the ^ operator) of a number with a bit-shifted version of itself. See here for more details."], [3, 1, 1, 0, "Route", "A simple class that stores the route plan and some statistics. Internally, a route consists of one or more Trip objects."], [3, 1, 1, 0, "ScheduledVisit", "Simple object that stores some data about a client or depot visit."], [3, 1, 1, 0, "Solution", "Encodes VRP solutions."], [3, 1, 1, 0, "Trip", "A simple class that stores the trip plan and some related statistics. The start and end depots default to the vehicle type's start and end depots if not explicitly given."], [3, 1, 1, 0, "VehicleType", "Simple data object storing all vehicle type data as properties. See also ../setup/concepts for further information about these properties."]], "pyvrp._pyvrp.Client": [[3, 4, 1, 0, "delivery", "Client delivery amounts shipped from the depot."], [3, 4, 1, 0, "group", "Indicates membership of the given client group, if any."], [3, 4, 1, 0, "name", "Free-form name field for this client."], [3, 4, 1, 0, "pickup", "Client pickup amounts returned to the depot."], [3, 4, 1, 0, "prize", "Prize collected by visiting this client."], [3, 4, 1, 0, "release_time", "Earliest time at which a vehicle may leave the depot on a trip to visit this client."], [3, 4, 1, 0, "required", "Whether visiting this client is required."], [3, 4, 1, 0, "service_duration", "Amount of time a vehicle needs to spend at this client before resuming its route."], [3, 4, 1, 0, "tw_early", "Earliest time at which this client may be visited to start service."], [3, 4, 1, 0, "tw_late", "Latest time at which this client may be visited to start service."], [3, 4, 1, 0, "x", "Horizontal coordinate of this client."], [3, 4, 1, 0, "y", "Vertical coordinate of this client."]], "pyvrp._pyvrp.Client.__init__": [[3, 2, 2, 0, "delivery", "The amounts this client demands from the depot."], [3, 2, 2, 0, "group", "Indicates membership of the given client group, if any."], [3, 2, 2, 0, "name", "Free-form name field for this client."], [3, 2, 2, 0, "pickup", "The amounts this client ships back to the depot."], [3, 2, 2, 0, "prize", "Prize collected by visiting this client."], [3, 2, 2, 0, "release_time", "Earliest time at which this client is released, that is, the earliest time at which a vehicle may leave the depot on a trip to visit this client."], [3, 2, 2, 0, "required", "Whether this client must be part of a feasible solution."], [3, 2, 2, 0, "service_duration", "Amount of time a vehicle needs to spend at this client before resuming its route."], [3, 2, 2, 0, "tw_early", "Earliest time at which this client may be visited to start service. Default 0."], [3, 2, 2, 0, "tw_late", "Latest time at which this client may be visited to start service. Unconstrained if not provided."], [3, 2, 2, 0, "x", "Horizontal coordinate of this client, that is, the 'x' part of the client's (x, y) location tuple."], [3, 2, 2, 0, "y", "Vertical coordinate of this client, that is, the 'y' part of the client's (x, y) location tuple."]], "pyvrp._pyvrp.ClientGroup": [[3, 3, 1, 0, "__iter__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "__len__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "add_client", "!! processed by numpydoc !!"], [3, 3, 1, 0, "clear", "!! processed by numpydoc !!"], [3, 4, 1, 0, "clients", "The clients in the group."], [3, 4, 1, 0, "mutually_exclusive", "When True, exactly one of the clients in this group must be visited if the group is required, and at most one if the group is not required."], [3, 4, 1, 0, "required", "Whether visiting this client group is required."]], "pyvrp._pyvrp.ClientGroup.__init__": [[3, 2, 2, 0, "clients", "The clients in the group."], [3, 2, 2, 0, "required", "Whether visiting this client group is required."]], "pyvrp._pyvrp.ClientGroup.__iter__": [[3, 2, 2, "pyvrp._pyvrp.ClientGroup.__iter__", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.ClientGroup.__len__": [[3, 2, 2, "pyvrp._pyvrp.ClientGroup.__len__", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.ClientGroup.add_client": [[3, 2, 2, "pyvrp._pyvrp.ClientGroup.add_client", "client", "!! processed by numpydoc !!"], [3, 2, 2, "pyvrp._pyvrp.ClientGroup.add_client", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.ClientGroup.clear": [[3, 2, 2, "pyvrp._pyvrp.ClientGroup.clear", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.CostEvaluator": [[3, 3, 1, 0, "cost", "Hand-waving some details, each solution consists of a set of non-empty routes \\mathcal{R}. Each route R \\in \\mathcal{R} is a sequence of edges, starting and ending at a depot. Each route R has an assigned vehicle type, through which the route is equipped with a fixed vehicle cost f_R, and unit distance and duration costs c^\\text{distance}_R and c^\\text{duration}_R, respectively. Let V_R = \\{i : (i, j) \\in R \\} be the set of locations visited by route R, and d_R and t_R the total route distance and duration, respectively. The objective value is then given by"], [3, 3, 1, 0, "dist_penalty", "Computes the time warp penalty for the given time warp."], [3, 3, 1, 0, "load_penalty", "Computes the total excess load penalty for the given load and vehicle capacity, and dimension."], [3, 3, 1, 0, "penalised_cost", "Computes a smoothed objective (penalised cost) for a given solution."], [3, 3, 1, 0, "tw_penalty", "Computes the time warp penalty for the given time warp."]], "pyvrp._pyvrp.CostEvaluator.__init__": [[3, 2, 2, 0, "dist_penalty", "The penalty for each unit of distance in excess of the vehicle's maximum distance constraint."], [3, 2, 2, 0, "load_penalties", "The penalty terms (one for each load dimension) for each unit of load in excess of the vehicle capacity."], [3, 2, 2, 0, "tw_penalty", "The penalty for each unit of time warp."]], "pyvrp._pyvrp.CostEvaluator.cost": [[3, 2, 2, "pyvrp._pyvrp.CostEvaluator.cost", "self", "Hand-waving some details, each solution consists of a set of non-empty routes \\mathcal{R}. Each route R \\in \\mathcal{R} is a sequence of edges, starting and ending at a depot. Each route R has an assigned vehicle type, through which the route is equipped with a fixed vehicle cost f_R, and unit distance and duration costs c^\\text{distance}_R and c^\\text{duration}_R, respectively. Let V_R = \\{i : (i, j) \\in R \\} be the set of locations visited by route R, and d_R and t_R the total route distance and duration, respectively. The objective value is then given by"], [3, 2, 2, "pyvrp._pyvrp.CostEvaluator.cost", "solution", "Hand-waving some details, each solution consists of a set of non-empty routes \\mathcal{R}. Each route R \\in \\mathcal{R} is a sequence of edges, starting and ending at a depot. Each route R has an assigned vehicle type, through which the route is equipped with a fixed vehicle cost f_R, and unit distance and duration costs c^\\text{distance}_R and c^\\text{duration}_R, respectively. Let V_R = \\{i : (i, j) \\in R \\} be the set of locations visited by route R, and d_R and t_R the total route distance and duration, respectively. The objective value is then given by"]], "pyvrp._pyvrp.CostEvaluator.dist_penalty": [[3, 2, 2, "pyvrp._pyvrp.CostEvaluator.dist_penalty", "distance", "Computes the time warp penalty for the given time warp."], [3, 2, 2, "pyvrp._pyvrp.CostEvaluator.dist_penalty", "max_distance", "Computes the time warp penalty for the given time warp."], [3, 2, 2, "pyvrp._pyvrp.CostEvaluator.dist_penalty", "self", "Computes the time warp penalty for the given time warp."]], "pyvrp._pyvrp.CostEvaluator.load_penalty": [[3, 2, 2, "pyvrp._pyvrp.CostEvaluator.load_penalty", "capacity", "Computes the total excess load penalty for the given load and vehicle capacity, and dimension."], [3, 2, 2, "pyvrp._pyvrp.CostEvaluator.load_penalty", "dimension", "Computes the total excess load penalty for the given load and vehicle capacity, and dimension."], [3, 2, 2, "pyvrp._pyvrp.CostEvaluator.load_penalty", "load", "Computes the total excess load penalty for the given load and vehicle capacity, and dimension."], [3, 2, 2, "pyvrp._pyvrp.CostEvaluator.load_penalty", "self", "Computes the total excess load penalty for the given load and vehicle capacity, and dimension."]], "pyvrp._pyvrp.CostEvaluator.penalised_cost": [[3, 2, 2, "pyvrp._pyvrp.CostEvaluator.penalised_cost", "self", "Computes a smoothed objective (penalised cost) for a given solution."], [3, 2, 2, "pyvrp._pyvrp.CostEvaluator.penalised_cost", "solution", "Computes a smoothed objective (penalised cost) for a given solution."]], "pyvrp._pyvrp.CostEvaluator.tw_penalty": [[3, 2, 2, "pyvrp._pyvrp.CostEvaluator.tw_penalty", "self", "Computes the time warp penalty for the given time warp."], [3, 2, 2, "pyvrp._pyvrp.CostEvaluator.tw_penalty", "time_warp", "Computes the time warp penalty for the given time warp."]], "pyvrp._pyvrp.Depot": [[3, 4, 1, 0, "name", "Free-form name field for this depot."], [3, 4, 1, 0, "tw_early", "Opening time of this depot."], [3, 4, 1, 0, "tw_late", "Closing time of this depot."], [3, 4, 1, 0, "x", "Horizontal coordinate of this depot."], [3, 4, 1, 0, "y", "Vertical coordinate of this depot."]], "pyvrp._pyvrp.Depot.__init__": [[3, 2, 2, 0, "name", "Free-form name field for this depot."], [3, 2, 2, 0, "tw_early", "Opening time of this depot."], [3, 2, 2, 0, "tw_late", "Closing time of this depot."], [3, 2, 2, 0, "x", "Horizontal coordinate of this depot, that is, the 'x' part of the depot's (x, y) location tuple."], [3, 2, 2, 0, "y", "Vertical coordinate of this depot, that is, the 'y' part of the depot's (x, y) location tuple."]], "pyvrp._pyvrp.DynamicBitset": [[3, 3, 1, 0, "__and__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "__eq__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "__getitem__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "__invert__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "__len__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "__or__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "__setitem__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "__xor__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "all", "!! processed by numpydoc !!"], [3, 3, 1, 0, "any", "!! processed by numpydoc !!"], [3, 3, 1, 0, "count", "!! processed by numpydoc !!"], [3, 3, 1, 0, "none", "!! processed by numpydoc !!"], [3, 3, 1, 0, "reset", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.__and__": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__and__", "other", "!! processed by numpydoc !!"], [3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__and__", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.__eq__": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__eq__", "other", "!! processed by numpydoc !!"], [3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__eq__", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.__getitem__": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__getitem__", "idx", "!! processed by numpydoc !!"], [3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__getitem__", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.__init__": [[3, 2, 2, 0, "num_bits", "Number of integers in [0, num_bits) this bitset must be able to store."]], "pyvrp._pyvrp.DynamicBitset.__invert__": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__invert__", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.__len__": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__len__", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.__or__": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__or__", "other", "!! processed by numpydoc !!"], [3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__or__", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.__setitem__": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__setitem__", "idx", "!! processed by numpydoc !!"], [3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__setitem__", "self", "!! processed by numpydoc !!"], [3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__setitem__", "value", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.__xor__": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__xor__", "other", "!! processed by numpydoc !!"], [3, 2, 2, "pyvrp._pyvrp.DynamicBitset.__xor__", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.all": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.all", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.any": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.any", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.count": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.count", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.none": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.none", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.DynamicBitset.reset": [[3, 2, 2, "pyvrp._pyvrp.DynamicBitset.reset", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.ProblemData": [[3, 3, 1, 0, "centroid", "Center point of all client locations (excluding depots)."], [3, 3, 1, 0, "clients", "Returns a list of all clients in the problem instance."], [3, 3, 1, 0, "depots", "Returns a list of all depots in the problem instance."], [3, 3, 1, 0, "distance_matrices", "Returns a list of all distance matrices in the problem instance."], [3, 3, 1, 0, "distance_matrix", "The full travel distance matrix associated with the given routing profile."], [3, 3, 1, 0, "duration_matrices", "Returns a list of all duration matrices in the problem instance."], [3, 3, 1, 0, "duration_matrix", "The full travel duration matrix associated with the given routing profile."], [3, 3, 1, 0, "group", "Returns the client group at the given index."], [3, 3, 1, 0, "groups", "Returns a list of all client groups in the problem instance."], [3, 3, 1, 0, "location", "Returns location data for the location at the given index. This can be a depot or a client: a depot if the idx argument is smaller than num_depots, and a client if the idx is bigger than that."], [3, 5, 1, 0, "num_clients", "Number of clients in this problem instance."], [3, 5, 1, 0, "num_depots", "Number of depots in this problem instance."], [3, 5, 1, 0, "num_groups", "Number of client groups in this problem instance."], [3, 5, 1, 0, "num_load_dimensions", "Number of load dimensions in this problem instance."], [3, 5, 1, 0, "num_locations", "Number of locations in this problem instance, that is, the number of depots plus the number of clients in the instance."], [3, 5, 1, 0, "num_profiles", "Number of routing profiles in this problem instance."], [3, 5, 1, 0, "num_vehicle_types", "Number of vehicle types in this problem instance."], [3, 5, 1, 0, "num_vehicles", "Number of vehicles in this problem instance."], [3, 3, 1, 0, "replace", "Returns a new ProblemData instance with the same data as this instance, except for the given parameters, which are used instead."], [3, 3, 1, 0, "vehicle_type", "Returns vehicle type data for the given vehicle type."], [3, 3, 1, 0, "vehicle_types", "Returns a list of all vehicle types in the problem instance."]], "pyvrp._pyvrp.ProblemData.__init__": [[3, 2, 2, 0, "clients", "List of clients to visit."], [3, 2, 2, 0, "depots", "List of depots."], [3, 2, 2, 0, "distance_matrices", "Distance matrices that give the travel distances between all locations (both depots and clients)."], [3, 2, 2, 0, "duration_matrices", "Duration matrices that give the travel durations between all locations (both depots and clients)."], [3, 2, 2, 0, "groups", "List of client groups."], [3, 2, 2, 0, "vehicle_types", "List of vehicle types in the problem instance."]], "pyvrp._pyvrp.ProblemData.centroid": [[3, 2, 2, "pyvrp._pyvrp.ProblemData.centroid", "self", "Center point of all client locations (excluding depots)."]], "pyvrp._pyvrp.ProblemData.clients": [[3, 2, 2, "pyvrp._pyvrp.ProblemData.clients", "self", "Returns a list of all clients in the problem instance."]], "pyvrp._pyvrp.ProblemData.depots": [[3, 2, 2, "pyvrp._pyvrp.ProblemData.depots", "self", "Returns a list of all depots in the problem instance."]], "pyvrp._pyvrp.ProblemData.distance_matrices": [[3, 2, 2, "pyvrp._pyvrp.ProblemData.distance_matrices", "self", "Returns a list of all distance matrices in the problem instance."]], "pyvrp._pyvrp.ProblemData.distance_matrix": [[3, 2, 2, 0, "profile", "Routing profile whose associated distance matrix to retrieve."], [3, 2, 2, "pyvrp._pyvrp.ProblemData.distance_matrix", "self", "The full travel distance matrix associated with the given routing profile."]], "pyvrp._pyvrp.ProblemData.duration_matrices": [[3, 2, 2, "pyvrp._pyvrp.ProblemData.duration_matrices", "self", "Returns a list of all duration matrices in the problem instance."]], "pyvrp._pyvrp.ProblemData.duration_matrix": [[3, 2, 2, 0, "profile", "Routing profile whose associated duration matrix to retrieve."], [3, 2, 2, "pyvrp._pyvrp.ProblemData.duration_matrix", "self", "The full travel duration matrix associated with the given routing profile."]], "pyvrp._pyvrp.ProblemData.group": [[3, 2, 2, 0, "group", "Group index whose information to retrieve."], [3, 2, 2, "pyvrp._pyvrp.ProblemData.group", "self", "Returns the client group at the given index."]], "pyvrp._pyvrp.ProblemData.groups": [[3, 2, 2, "pyvrp._pyvrp.ProblemData.groups", "self", "Returns a list of all client groups in the problem instance."]], "pyvrp._pyvrp.ProblemData.location": [[3, 2, 2, 0, "idx", "Location index whose information to retrieve."], [3, 2, 2, "pyvrp._pyvrp.ProblemData.location", "self", "Returns location data for the location at the given index. This can be a depot or a client: a depot if the idx argument is smaller than num_depots, and a client if the idx is bigger than that."]], "pyvrp._pyvrp.ProblemData.replace": [[3, 2, 2, 0, "clients", "Optional list of clients."], [3, 2, 2, 0, "depots", "Optional list of depots."], [3, 2, 2, 0, "distance_matrices", "Optional distance matrices, one per routing profile."], [3, 2, 2, 0, "duration_matrices", "Optional duration matrices, one per routing profile."], [3, 2, 2, 0, "groups", "Optional client groups."], [3, 2, 2, "pyvrp._pyvrp.ProblemData.replace", "self", "Returns a new ProblemData instance with the same data as this instance, except for the given parameters, which are used instead."], [3, 2, 2, 0, "vehicle_types", "Optional list of vehicle types."]], "pyvrp._pyvrp.ProblemData.vehicle_type": [[3, 2, 2, "pyvrp._pyvrp.ProblemData.vehicle_type", "self", "Returns vehicle type data for the given vehicle type."], [3, 2, 2, 0, "vehicle_type", "Vehicle type number whose information to retrieve."]], "pyvrp._pyvrp.ProblemData.vehicle_types": [[3, 2, 2, "pyvrp._pyvrp.ProblemData.vehicle_types", "self", "Returns a list of all vehicle types in the problem instance."]], "pyvrp._pyvrp.RandomNumberGenerator": [[3, 3, 1, 0, "__call__", "!! processed by numpydoc !!"], [3, 3, 1, 0, "max", "!! processed by numpydoc !!"], [3, 3, 1, 0, "min", "!! processed by numpydoc !!"], [3, 3, 1, 0, "rand", "!! processed by numpydoc !!"], [3, 3, 1, 0, "randint", "!! processed by numpydoc !!"], [3, 3, 1, 0, "state", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.RandomNumberGenerator.__call__": [[3, 2, 2, "pyvrp._pyvrp.RandomNumberGenerator.__call__", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.RandomNumberGenerator.__init__": [[3, 2, 2, 0, "seed", "Seed used to set the initial RNG state."]], "pyvrp._pyvrp.RandomNumberGenerator.rand": [[3, 2, 2, "pyvrp._pyvrp.RandomNumberGenerator.rand", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.RandomNumberGenerator.randint": [[3, 2, 2, "pyvrp._pyvrp.RandomNumberGenerator.randint", "high", "!! processed by numpydoc !!"], [3, 2, 2, "pyvrp._pyvrp.RandomNumberGenerator.randint", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.RandomNumberGenerator.state": [[3, 2, 2, "pyvrp._pyvrp.RandomNumberGenerator.state", "self", "!! processed by numpydoc !!"]], "pyvrp._pyvrp.Route": [[3, 3, 1, 0, "centroid", "Center point of the client locations on this route."], [3, 3, 1, 0, "delivery", "Total client delivery load on this route."], [3, 3, 1, 0, "distance", "Total distance travelled on this route."], [3, 3, 1, 0, "distance_cost", "Total cost of the distance travelled on this route."], [3, 3, 1, 0, "duration", "Total route duration, including travel, service and waiting time."], [3, 3, 1, 0, "duration_cost", "Total cost of the duration of this route."], [3, 3, 1, 0, "end_depot", "Location index of the route's ending depot."], [3, 3, 1, 0, "end_time", "End time of the route. This is equivalent to start_time + duration - time_warp."], [3, 3, 1, 0, "excess_distance", "Distance in excess of the vehicle's maximum distance constraint."], [3, 3, 1, 0, "excess_load", "Pickup or delivery loads in excess of the vehicle's capacity."], [3, 3, 1, 0, "has_excess_distance", "Returns whether this route violates maximum distance constraints."], [3, 3, 1, 0, "has_excess_load", "Returns whether this route violates capacity constraints."], [3, 3, 1, 0, "has_time_warp", "Returns whether this route violates time window or maximum duration constraints."], [3, 3, 1, 0, "is_feasible", "Returns whether this route is feasible."], [3, 3, 1, 0, "num_trips", "Returns the number of trips in this route."], [3, 3, 1, 0, "pickup", "Total client pickup load on this route."], [3, 3, 1, 0, "prizes", "Total prize value collected on this route."], [3, 3, 1, 0, "release_time", "Earliest time at which this route can leave the depot. Follows from the release times of clients visited on the first trip of this route."], [3, 3, 1, 0, "schedule", "Statistics about each visit and the overall route schedule. This includes all client visits, but also starting and leaving depots."], [3, 3, 1, 0, "service_duration", "Total duration of client service on this route."], [3, 3, 1, 0, "slack", "Time by which departure from the depot can be delayed without resulting in (additional) time warp or increased route duration."], [3, 3, 1, 0, "start_depot", "Location index of the route's starting depot."], [3, 3, 1, 0, "start_time", "Start time of this route. This is the earliest possible time at which the route can leave the depot and have a minimal duration and time warp. If there is positive slack(), the start time can be delayed by at most slack() time units without increasing the total (minimal) route duration, or time warp."], [3, 3, 1, 0, "time_warp", "Amount of time warp incurred on this route."], [3, 3, 1, 0, "travel_duration", "Total duration of travel on this route."], [3, 3, 1, 0, "trip", "Returns the trip at the given index."], [3, 3, 1, 0, "trips", "Returns the trips that make up this route."], [3, 3, 1, 0, "vehicle_type", "Index of the type of vehicle used on this route."], [3, 3, 1, 0, "visits", "Route visits, as a list of clients."], [3, 3, 1, 0, "wait_duration", "Total waiting duration on this route."]], "pyvrp._pyvrp.Route.__init__": [[3, 2, 2, "pyvrp._pyvrp.Route", "data", "A simple class that stores the route plan and some statistics. Internally, a route consists of one or more Trip objects."], [3, 2, 2, "pyvrp._pyvrp.Route", "vehicle_type", "A simple class that stores the route plan and some statistics. Internally, a route consists of one or more Trip objects."], [3, 2, 2, "pyvrp._pyvrp.Route", "visits", "A simple class that stores the route plan and some statistics. Internally, a route consists of one or more Trip objects."]], "pyvrp._pyvrp.Route.centroid": [[3, 2, 2, "pyvrp._pyvrp.Route.centroid", "self", "Center point of the client locations on this route."]], "pyvrp._pyvrp.Route.delivery": [[3, 2, 2, "pyvrp._pyvrp.Route.delivery", "self", "Total client delivery load on this route."]], "pyvrp._pyvrp.Route.distance": [[3, 2, 2, "pyvrp._pyvrp.Route.distance", "self", "Total distance travelled on this route."]], "pyvrp._pyvrp.Route.distance_cost": [[3, 2, 2, "pyvrp._pyvrp.Route.distance_cost", "self", "Total cost of the distance travelled on this route."]], "pyvrp._pyvrp.Route.duration": [[3, 2, 2, "pyvrp._pyvrp.Route.duration", "self", "Total route duration, including travel, service and waiting time."]], "pyvrp._pyvrp.Route.duration_cost": [[3, 2, 2, "pyvrp._pyvrp.Route.duration_cost", "self", "Total cost of the duration of this route."]], "pyvrp._pyvrp.Route.end_depot": [[3, 2, 2, "pyvrp._pyvrp.Route.end_depot", "self", "Location index of the route's ending depot."]], "pyvrp._pyvrp.Route.end_time": [[3, 2, 2, "pyvrp._pyvrp.Route.end_time", "self", "End time of the route. This is equivalent to start_time + duration - time_warp."]], "pyvrp._pyvrp.Route.excess_distance": [[3, 2, 2, "pyvrp._pyvrp.Route.excess_distance", "self", "Distance in excess of the vehicle's maximum distance constraint."]], "pyvrp._pyvrp.Route.excess_load": [[3, 2, 2, "pyvrp._pyvrp.Route.excess_load", "self", "Pickup or delivery loads in excess of the vehicle's capacity."]], "pyvrp._pyvrp.Route.has_excess_distance": [[3, 2, 2, "pyvrp._pyvrp.Route.has_excess_distance", "self", "Returns whether this route violates maximum distance constraints."]], "pyvrp._pyvrp.Route.has_excess_load": [[3, 2, 2, "pyvrp._pyvrp.Route.has_excess_load", "self", "Returns whether this route violates capacity constraints."]], "pyvrp._pyvrp.Route.has_time_warp": [[3, 2, 2, "pyvrp._pyvrp.Route.has_time_warp", "self", "Returns whether this route violates time window or maximum duration constraints."]], "pyvrp._pyvrp.Route.is_feasible": [[3, 2, 2, "pyvrp._pyvrp.Route.is_feasible", "self", "Returns whether this route is feasible."]], "pyvrp._pyvrp.Route.num_trips": [[3, 2, 2, "pyvrp._pyvrp.Route.num_trips", "self", "Returns the number of trips in this route."]], "pyvrp._pyvrp.Route.pickup": [[3, 2, 2, "pyvrp._pyvrp.Route.pickup", "self", "Total client pickup load on this route."]], "pyvrp._pyvrp.Route.prizes": [[3, 2, 2, "pyvrp._pyvrp.Route.prizes", "self", "Total prize value collected on this route."]], "pyvrp._pyvrp.Route.release_time": [[3, 2, 2, "pyvrp._pyvrp.Route.release_time", "self", "Earliest time at which this route can leave the depot. Follows from the release times of clients visited on the first trip of this route."]], "pyvrp._pyvrp.Route.schedule": [[3, 2, 2, "pyvrp._pyvrp.Route.schedule", "self", "Statistics about each visit and the overall route schedule. This includes all client visits, but also starting and leaving depots."]], "pyvrp._pyvrp.Route.service_duration": [[3, 2, 2, "pyvrp._pyvrp.Route.service_duration", "self", "Total duration of client service on this route."]], "pyvrp._pyvrp.Route.slack": [[3, 2, 2, "pyvrp._pyvrp.Route.slack", "self", "Time by which departure from the depot can be delayed without resulting in (additional) time warp or increased route duration."]], "pyvrp._pyvrp.Route.start_depot": [[3, 2, 2, "pyvrp._pyvrp.Route.start_depot", "self", "Location index of the route's starting depot."]], "pyvrp._pyvrp.Route.start_time": [[3, 2, 2, "pyvrp._pyvrp.Route.start_time", "self", "Start time of this route. This is the earliest possible time at which the route can leave the depot and have a minimal duration and time warp. If there is positive slack(), the start time can be delayed by at most slack() time units without increasing the total (minimal) route duration, or time warp."]], "pyvrp._pyvrp.Route.time_warp": [[3, 2, 2, "pyvrp._pyvrp.Route.time_warp", "self", "Amount of time warp incurred on this route."]], "pyvrp._pyvrp.Route.travel_duration": [[3, 2, 2, "pyvrp._pyvrp.Route.travel_duration", "self", "Total duration of travel on this route."]], "pyvrp._pyvrp.Route.trip": [[3, 2, 2, "pyvrp._pyvrp.Route.trip", "idx", "Returns the trip at the given index."], [3, 2, 2, "pyvrp._pyvrp.Route.trip", "self", "Returns the trip at the given index."]], "pyvrp._pyvrp.Route.trips": [[3, 2, 2, "pyvrp._pyvrp.Route.trips", "self", "Returns the trips that make up this route."]], "pyvrp._pyvrp.Route.vehicle_type": [[3, 2, 2, "pyvrp._pyvrp.Route.vehicle_type", "self", "Index of the type of vehicle used on this route."]], "pyvrp._pyvrp.Route.visits": [[3, 2, 2, "pyvrp._pyvrp.Route.visits", "self", "Route visits, as a list of clients."]], "pyvrp._pyvrp.Route.wait_duration": [[3, 2, 2, "pyvrp._pyvrp.Route.wait_duration", "self", "Total waiting duration on this route."]], "pyvrp._pyvrp.ScheduledVisit": [[3, 4, 1, 0, "end_service", "Time at which service completes."], [3, 4, 1, 0, "location", "Index of the visited location (client or depot)."], [3, 4, 1, 0, "service_duration", "Duration of the service."], [3, 4, 1, 0, "start_service", "Time at which service begins."], [3, 4, 1, 0, "time_warp", "If the vehicle arrives late, this is the duration it has to 'travel back in time' to begin service. Non-zero time warp indicates an infeasible route."], [3, 4, 1, 0, "trip", "Index of the trip visiting this location."], [3, 4, 1, 0, "wait_duration", "If the vehicle arrives early, this is the duration it has to wait until it can begin service."]], "pyvrp._pyvrp.Solution": [[3, 3, 1, 0, "distance", "Returns the total distance over all routes."], [3, 3, 1, 0, "distance_cost", "Total cost of the distance travelled on routes in this solution."], [3, 3, 1, 0, "duration", "Total duration of all routes in this solution."], [3, 3, 1, 0, "duration_cost", "Total cost of the duration of all routes in this solution."], [3, 3, 1, 0, "excess_distance", "Returns the total distance in excess of maximum duration constraints, over all routes."], [3, 3, 1, 0, "excess_load", "Aggregate pickup or delivery loads in excess of the vehicle's capacity of all routes."], [3, 3, 1, 0, "fixed_vehicle_cost", "Returns the fixed vehicle cost of all vehicles used in this solution."], [3, 3, 1, 0, "has_excess_distance", "Returns whether this solution violates maximum distance constraints."], [3, 3, 1, 0, "has_excess_load", "Returns whether this solution violates capacity constraints."], [3, 3, 1, 0, "has_time_warp", "Returns whether this solution violates time window or maximum duration constraints."], [3, 3, 1, 0, "is_complete", "Returns whether this solution is complete, which it is when it has all required clients."], [3, 3, 1, 0, "is_feasible", "Whether this solution is feasible."], [3, 3, 1, 0, "is_group_feasible", "Returns whether this solution is feasible w.r.t. the client group restrictions."], [3, 3, 1, 0, "make_random", "Creates a randomly generated solution."], [3, 3, 1, 0, "neighbours", "Returns a list of neighbours for each client, by index."], [3, 3, 1, 0, "num_clients", "Number of clients in this solution."], [3, 3, 1, 0, "num_missing_clients", "Number of required clients that are not in this solution."], [3, 3, 1, 0, "num_routes", "Number of routes in this solution."], [3, 3, 1, 0, "num_trips", "Number of trips in this solution."], [3, 3, 1, 0, "prizes", "Returns the total collected prize value over all routes."], [3, 3, 1, 0, "routes", "The solution's routing decisions."], [3, 3, 1, 0, "time_warp", "Returns the total time warp load over all routes."], [3, 3, 1, 0, "uncollected_prizes", "Total prize value of all clients not visited in this solution."]], "pyvrp._pyvrp.Solution.__init__": [[3, 2, 2, 0, "data", "Data instance."], [3, 2, 2, 0, "routes", "Route list to use."]], "pyvrp._pyvrp.Solution.distance": [[3, 2, 2, "pyvrp._pyvrp.Solution.distance", "self", "Returns the total distance over all routes."]], "pyvrp._pyvrp.Solution.distance_cost": [[3, 2, 2, "pyvrp._pyvrp.Solution.distance_cost", "self", "Total cost of the distance travelled on routes in this solution."]], "pyvrp._pyvrp.Solution.duration": [[3, 2, 2, "pyvrp._pyvrp.Solution.duration", "self", "Total duration of all routes in this solution."]], "pyvrp._pyvrp.Solution.duration_cost": [[3, 2, 2, "pyvrp._pyvrp.Solution.duration_cost", "self", "Total cost of the duration of all routes in this solution."]], "pyvrp._pyvrp.Solution.excess_distance": [[3, 2, 2, "pyvrp._pyvrp.Solution.excess_distance", "self", "Returns the total distance in excess of maximum duration constraints, over all routes."]], "pyvrp._pyvrp.Solution.excess_load": [[3, 2, 2, "pyvrp._pyvrp.Solution.excess_load", "self", "Aggregate pickup or delivery loads in excess of the vehicle's capacity of all routes."]], "pyvrp._pyvrp.Solution.fixed_vehicle_cost": [[3, 2, 2, "pyvrp._pyvrp.Solution.fixed_vehicle_cost", "self", "Returns the fixed vehicle cost of all vehicles used in this solution."]], "pyvrp._pyvrp.Solution.has_excess_distance": [[3, 2, 2, "pyvrp._pyvrp.Solution.has_excess_distance", "self", "Returns whether this solution violates maximum distance constraints."]], "pyvrp._pyvrp.Solution.has_excess_load": [[3, 2, 2, "pyvrp._pyvrp.Solution.has_excess_load", "self", "Returns whether this solution violates capacity constraints."]], "pyvrp._pyvrp.Solution.has_time_warp": [[3, 2, 2, "pyvrp._pyvrp.Solution.has_time_warp", "self", "Returns whether this solution violates time window or maximum duration constraints."]], "pyvrp._pyvrp.Solution.is_complete": [[3, 2, 2, "pyvrp._pyvrp.Solution.is_complete", "self", "Returns whether this solution is complete, which it is when it has all required clients."]], "pyvrp._pyvrp.Solution.is_feasible": [[3, 2, 2, "pyvrp._pyvrp.Solution.is_feasible", "self", "Whether this solution is feasible."]], "pyvrp._pyvrp.Solution.is_group_feasible": [[3, 2, 2, "pyvrp._pyvrp.Solution.is_group_feasible", "self", "Returns whether this solution is feasible w.r.t. the client group restrictions."]], "pyvrp._pyvrp.Solution.make_random": [[3, 2, 2, 0, "data", "Data instance."], [3, 2, 2, 0, "rng", "Random number generator to use."]], "pyvrp._pyvrp.Solution.neighbours": [[3, 2, 2, "pyvrp._pyvrp.Solution.neighbours", "self", "Returns a list of neighbours for each client, by index."]], "pyvrp._pyvrp.Solution.num_clients": [[3, 2, 2, "pyvrp._pyvrp.Solution.num_clients", "self", "Number of clients in this solution."]], "pyvrp._pyvrp.Solution.num_missing_clients": [[3, 2, 2, "pyvrp._pyvrp.Solution.num_missing_clients", "self", "Number of required clients that are not in this solution."]], "pyvrp._pyvrp.Solution.num_routes": [[3, 2, 2, "pyvrp._pyvrp.Solution.num_routes", "self", "Number of routes in this solution."]], "pyvrp._pyvrp.Solution.num_trips": [[3, 2, 2, "pyvrp._pyvrp.Solution.num_trips", "self", "Number of trips in this solution."]], "pyvrp._pyvrp.Solution.prizes": [[3, 2, 2, "pyvrp._pyvrp.Solution.prizes", "self", "Returns the total collected prize value over all routes."]], "pyvrp._pyvrp.Solution.routes": [[3, 2, 2, "pyvrp._pyvrp.Solution.routes", "self", "The solution's routing decisions."]], "pyvrp._pyvrp.Solution.time_warp": [[3, 2, 2, "pyvrp._pyvrp.Solution.time_warp", "self", "Returns the total time warp load over all routes."]], "pyvrp._pyvrp.Solution.uncollected_prizes": [[3, 2, 2, "pyvrp._pyvrp.Solution.uncollected_prizes", "self", "Total prize value of all clients not visited in this solution."]], "pyvrp._pyvrp.Trip": [[3, 3, 1, 0, "centroid", "Center point of the client locations on this trip."], [3, 3, 1, 0, "delivery", "Total client delivery load on this trip."], [3, 3, 1, 0, "distance", "Total distance travelled on this trip."], [3, 3, 1, 0, "end_depot", "Location index of the trip's ending depot."], [3, 3, 1, 0, "excess_load", "Pickup or delivery loads in excess of the vehicle's capacity."], [3, 3, 1, 0, "has_excess_load", "Returns whether this trip violates capacity constraints."], [3, 3, 1, 0, "load", "Maximum load at any point of this trip."], [3, 3, 1, 0, "pickup", "Total client pickup load on this trip."], [3, 3, 1, 0, "prizes", "Total prize value collected on this trip."], [3, 3, 1, 0, "release_time", "Earliest time at which this trip can leave the depot. Follows from the release times of clients visited on this trip."], [3, 3, 1, 0, "service_duration", "Total duration of service on this trip."], [3, 3, 1, 0, "start_depot", "Location index of the trip's starting depot."], [3, 3, 1, 0, "travel_duration", "Total duration of travel on this trip."], [3, 3, 1, 0, "vehicle_type", "Index of the type of vehicle used on this trip."], [3, 3, 1, 0, "visits", "Trip visits, as a list of clients."]], "pyvrp._pyvrp.Trip.__init__": [[3, 2, 2, "pyvrp._pyvrp.Trip", "data", "A simple class that stores the trip plan and some related statistics. The start and end depots default to the vehicle type's start and end depots if not explicitly given."], [3, 2, 2, "pyvrp._pyvrp.Trip", "end_depot", "A simple class that stores the trip plan and some related statistics. The start and end depots default to the vehicle type's start and end depots if not explicitly given."], [3, 2, 2, "pyvrp._pyvrp.Trip", "start_depot", "A simple class that stores the trip plan and some related statistics. The start and end depots default to the vehicle type's start and end depots if not explicitly given."], [3, 2, 2, "pyvrp._pyvrp.Trip", "vehicle_type", "A simple class that stores the trip plan and some related statistics. The start and end depots default to the vehicle type's start and end depots if not explicitly given."], [3, 2, 2, "pyvrp._pyvrp.Trip", "visits", "A simple class that stores the trip plan and some related statistics. The start and end depots default to the vehicle type's start and end depots if not explicitly given."]], "pyvrp._pyvrp.Trip.centroid": [[3, 2, 2, "pyvrp._pyvrp.Trip.centroid", "self", "Center point of the client locations on this trip."]], "pyvrp._pyvrp.Trip.delivery": [[3, 2, 2, "pyvrp._pyvrp.Trip.delivery", "self", "Total client delivery load on this trip."]], "pyvrp._pyvrp.Trip.distance": [[3, 2, 2, "pyvrp._pyvrp.Trip.distance", "self", "Total distance travelled on this trip."]], "pyvrp._pyvrp.Trip.end_depot": [[3, 2, 2, "pyvrp._pyvrp.Trip.end_depot", "self", "Location index of the trip's ending depot."]], "pyvrp._pyvrp.Trip.excess_load": [[3, 2, 2, "pyvrp._pyvrp.Trip.excess_load", "self", "Pickup or delivery loads in excess of the vehicle's capacity."]], "pyvrp._pyvrp.Trip.has_excess_load": [[3, 2, 2, "pyvrp._pyvrp.Trip.has_excess_load", "self", "Returns whether this trip violates capacity constraints."]], "pyvrp._pyvrp.Trip.load": [[3, 2, 2, "pyvrp._pyvrp.Trip.load", "self", "Maximum load at any point of this trip."]], "pyvrp._pyvrp.Trip.pickup": [[3, 2, 2, "pyvrp._pyvrp.Trip.pickup", "self", "Total client pickup load on this trip."]], "pyvrp._pyvrp.Trip.prizes": [[3, 2, 2, "pyvrp._pyvrp.Trip.prizes", "self", "Total prize value collected on this trip."]], "pyvrp._pyvrp.Trip.release_time": [[3, 2, 2, "pyvrp._pyvrp.Trip.release_time", "self", "Earliest time at which this trip can leave the depot. Follows from the release times of clients visited on this trip."]], "pyvrp._pyvrp.Trip.service_duration": [[3, 2, 2, "pyvrp._pyvrp.Trip.service_duration", "self", "Total duration of service on this trip."]], "pyvrp._pyvrp.Trip.start_depot": [[3, 2, 2, "pyvrp._pyvrp.Trip.start_depot", "self", "Location index of the trip's starting depot."]], "pyvrp._pyvrp.Trip.travel_duration": [[3, 2, 2, "pyvrp._pyvrp.Trip.travel_duration", "self", "Total duration of travel on this trip."]], "pyvrp._pyvrp.Trip.vehicle_type": [[3, 2, 2, "pyvrp._pyvrp.Trip.vehicle_type", "self", "Index of the type of vehicle used on this trip."]], "pyvrp._pyvrp.Trip.visits": [[3, 2, 2, "pyvrp._pyvrp.Trip.visits", "self", "Trip visits, as a list of clients."]], "pyvrp._pyvrp.VehicleType": [[3, 4, 1, 0, "capacity", "Capacities of this vehicle type, per load dimension."], [3, 4, 1, 0, "end_depot", "End location associated with these vehicles."], [3, 4, 1, 0, "fixed_cost", "Fixed cost of using a vehicle of this type."], [3, 4, 1, 0, "initial_load", "Load already on the vehicle that need to be dropped off at a depot. This load is present irrespective of any client visits."], [3, 4, 1, 0, "max_distance", "Maximum travel distance of the route this vehicle type is assigned to. This is a very large number when the maximum distance is unconstrained."], [3, 4, 1, 0, "max_duration", "Maximum duration of the route this vehicle type is assigned to. This is a very large number when the maximum duration is unconstrained."], [3, 4, 1, 0, "max_reloads", "Maximum number of reloads the vehicle may perform on a route."], [3, 4, 1, 0, "name", "Free-form name field for this vehicle type."], [3, 4, 1, 0, "num_available", "Number of vehicles of this type that are available."], [3, 4, 1, 0, "profile", "This vehicle type's routing profile."], [3, 4, 1, 0, "reload_depots", "List of reload locations this vehicle may visit along it route, to empty and reload."], [3, 3, 1, 0, "replace", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 4, 1, 0, "start_depot", "Start location associated with these vehicles."], [3, 4, 1, 0, "start_late", "Latest start of the vehicle type's shift. This is equal to tw_late when the latest start is not constrained."], [3, 4, 1, 0, "tw_early", "Start of the vehicle type's shift, if specified."], [3, 4, 1, 0, "tw_late", "End of the vehicle type's shift, if specified."], [3, 4, 1, 0, "unit_distance_cost", "Cost per unit of distance travelled by vehicles of this type."], [3, 4, 1, 0, "unit_duration_cost", "Cost per unit of duration on routes using vehicles of this type."]], "pyvrp._pyvrp.VehicleType.__init__": [[3, 2, 2, 0, "capacity", "Capacities of this vehicle type, per load dimension."], [3, 2, 2, 0, "end_depot", "Depot (location index) where vehicles of this type end routes. Default 0 (first depot)."], [3, 2, 2, 0, "fixed_cost", "Fixed cost of using a vehicle of this type."], [3, 2, 2, 0, "initial_load", "Load already on the vehicle that need to be dropped off at a depot. This load is present irrespective of any client visits."], [3, 2, 2, 0, "max_distance", "Maximum route distance."], [3, 2, 2, 0, "max_duration", "Maximum route duration."], [3, 2, 2, 0, "max_reloads", "Maximum number of reloads the vehicle may perform on a route. Unconstrained if not explicitly provided."], [3, 2, 2, 0, "name", "Free-form name field for this vehicle type."], [3, 2, 2, 0, "num_available", "Number of vehicles of this type that are available."], [3, 2, 2, 0, "profile", "This vehicle type's routing profile."], [3, 2, 2, 0, "reload_depots", "List of reload depots (location indices) this vehicle may visit along its route, to empty and reload for subsequent client visits."], [3, 2, 2, 0, "start_depot", "Depot (location index) where vehicles of this type start their routes."], [3, 2, 2, 0, "start_late", "Latest start of the vehicle type's shift."], [3, 2, 2, 0, "tw_early", "Start of the vehicle type's shift."], [3, 2, 2, 0, "tw_late", "End of the vehicle type's shift."], [3, 2, 2, 0, "unit_distance_cost", "Cost per unit of distance travelled by vehicles of this type."], [3, 2, 2, 0, "unit_duration_cost", "Cost per unit of duration on routes serviced by vehicles of this type."]], "pyvrp._pyvrp.VehicleType.replace": [[3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "capacity", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "end_depot", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "fixed_cost", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "initial_load", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "max_distance", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "max_duration", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "max_reloads", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "name", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "num_available", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "profile", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "reload_depots", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "self", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "start_depot", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "start_late", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "tw_early", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "tw_late", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "unit_distance_cost", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."], [3, 2, 2, "pyvrp._pyvrp.VehicleType.replace", "unit_duration_cost", "Returns a new VehicleType with the same data as this one, except for the given parameters, which are used instead."]], "pyvrp.crossover": [[0, 0, 0, 1, "ordered_crossover", ""], [0, 0, 0, 1, "selective_route_exchange", ""]], "pyvrp.crossover.ordered_crossover": [[0, 6, 1, 0, "ordered_crossover", "Performs an ordered crossover (OX) operation between the two given parents. The clients between two randomly selected indices of the first route are copied into a new solution, and any missing clients that are present in the second route are then copied in as well. See Rdd0370444073-1 for details."]], "pyvrp.crossover.ordered_crossover.ordered_crossover": [[0, 2, 2, 0, "cost_evaluator", "Cost evaluator object."], [0, 2, 2, 0, "data", "The problem instance."], [0, 2, 2, 0, "parents", "The two parent solutions to create an offspring from."], [0, 2, 2, 0, "rng", "The random number generator to use."]], "pyvrp.crossover.selective_route_exchange": [[0, 6, 1, 0, "selective_route_exchange", "The selective route exchange crossover (SREX) operator due to Nagata and Kobayashi R8de92506ce4f-1 combines routes from both parents to generate a new offspring solution. It does this by carefully selecting routes from the second parent that could be exchanged with routes from the first parent. This often results in incomplete offspring that can then be repaired using a search method."]], "pyvrp.crossover.selective_route_exchange.selective_route_exchange": [[0, 2, 2, 0, "cost_evaluator", "The cost evaluator used to evaluate the offspring."], [0, 2, 2, 0, "data", "The problem instance."], [0, 2, 2, 0, "parents", "The two parent solutions to create an offspring from."], [0, 2, 2, 0, "rng", "The random number generator to use."]], "pyvrp.diversity": [[1, 0, 0, 1, "_diversity", ""]], "pyvrp.diversity._diversity": [[1, 6, 1, 0, "broken_pairs_distance", "Computes the symmetric broken pairs distance (BPD) between the given two solutions. This function determines whether each location in the problem shares neighbours between the first and second solution. If not, the location is part of a 'broken pair': a link that is part of one solution, but not of the other."]], "pyvrp.diversity._diversity.broken_pairs_distance": [[1, 2, 2, 0, "first", "First solution."], [1, 2, 2, 0, "second", "Second solution."]], "pyvrp.exceptions": [[3, 7, 1, 0, "PenaltyBoundWarning", "Raised when a penalty parameter has reached its maximum value. This means PyVRP struggles to find a feasible solution for the instance that's being solved, either because the instance has no feasible solution, or it is just very hard to find one."], [3, 7, 1, 0, "ScalingWarning", "Raised when the distance or duration values in the problem are very large, which could cause the algorithm to suffer from numerical issues."], [3, 7, 1, 0, "TspWarning", "Raised when the problem is a TSP but a component is used that explicitly requires the presence of two or more vehicles (i.e., a proper VRP)."]], "pyvrp.minimise_fleet": [[3, 6, 1, 0, "minimise_fleet", "Attempts to reduce the number of vehicles needed to achieve a feasible solution to the given problem instance, subject to a stopping criterion."]], "pyvrp.minimise_fleet.minimise_fleet": [[3, 2, 2, 0, "data", "Problem instance with a given vehicle composition."], [3, 2, 2, 0, "params", "Solver parameters to use."], [3, 2, 2, 0, "seed", "Seed value to use for the random number stream."], [3, 2, 2, 0, "stop", "Stopping criterion that determines how much effort to spend on finding smaller fleet compositions."]], "pyvrp.plotting": [[2, 0, 0, 1, "plot_coordinates", ""], [2, 0, 0, 1, "plot_demands", ""], [2, 0, 0, 1, "plot_diversity", ""], [2, 0, 0, 1, "plot_instance", ""], [2, 0, 0, 1, "plot_objectives", ""], [2, 0, 0, 1, "plot_result", ""], [2, 0, 0, 1, "plot_route_schedule", ""], [2, 0, 0, 1, "plot_runtimes", ""], [2, 0, 0, 1, "plot_solution", ""], [2, 0, 0, 1, "plot_time_windows", ""]], "pyvrp.plotting.plot_coordinates": [[2, 6, 1, 0, "plot_coordinates", "Plots coordinates for clients and depot."]], "pyvrp.plotting.plot_coordinates.plot_coordinates": [[2, 2, 2, 0, "ax", "Axes object to draw the plot on."], [2, 2, 2, 0, "data", "Data instance."], [2, 2, 2, 0, "title", "Title to add to the plot."]], "pyvrp.plotting.plot_demands": [[2, 6, 1, 0, "plot_demands", "Plots demands for clients, as vertical bars sorted by demand."]], "pyvrp.plotting.plot_demands.plot_demands": [[2, 2, 2, 0, "ax", "Axes object to draw the plot on."], [2, 2, 2, 0, "data", "Data instance."], [2, 2, 2, 0, "dimension", "Load dimension to plot."], [2, 2, 2, 0, "title", "Title to add to the plot."]], "pyvrp.plotting.plot_diversity": [[2, 6, 1, 0, "plot_diversity", "Plots population diversity statistics."]], "pyvrp.plotting.plot_diversity.plot_diversity": [[2, 2, 2, 0, "ax", "Axes object to draw the plot on."], [2, 2, 2, 0, "result", "Result for which to plot diversity."]], "pyvrp.plotting.plot_instance": [[2, 6, 1, 0, "plot_instance", "Plots client coordinate, time window and demand data of the given instance."]], "pyvrp.plotting.plot_instance.plot_instance": [[2, 2, 2, 0, "data", "Data instance."], [2, 2, 2, 0, "fig", "Optional Figure to draw on."]], "pyvrp.plotting.plot_objectives": [[2, 6, 1, 0, "plot_objectives", "Plots each subpopulation's objective values."]], "pyvrp.plotting.plot_objectives.plot_objectives": [[2, 2, 2, 0, "ax", "Axes object to draw the plot on."], [2, 2, 2, 0, "num_to_skip", "Number of initial iterations to skip when plotting."], [2, 2, 2, 0, "result", "Result for which to plot objectives."], [2, 2, 2, 0, "ylim_adjust", "Bounds the y-axis to (best * ylim_adjust[0], best * ylim_adjust[1]) where best denotes the best found feasible objective value."]], "pyvrp.plotting.plot_result": [[2, 6, 1, 0, "plot_result", "Plots the results of a run, including the best solution and detailed statistics about the algorithm's performance."]], "pyvrp.plotting.plot_result.plot_result": [[2, 2, 2, 0, "data", "Data instance underlying the result's solution."], [2, 2, 2, 0, "fig", "Optional Figure to draw on."], [2, 2, 2, 0, "result", "Result to be plotted."]], "pyvrp.plotting.plot_route_schedule": [[2, 6, 1, 0, "plot_route_schedule", "Plots a route schedule. This function plots multiple time statistics as a function of distance travelled:"]], "pyvrp.plotting.plot_route_schedule.plot_route_schedule": [[2, 2, 2, 0, "ax", "Axes object to draw the plot on."], [2, 2, 2, 0, "data", "Data instance for which to plot the route schedule."], [2, 2, 2, 0, "legend", "Whether or not to show the legends."], [2, 2, 2, 0, "load_dimension", "Load dimension to plot."], [2, 2, 2, 0, "route", "Route (list of clients) whose schedule to plot."], [2, 2, 2, 0, "title", "Title to add to the plot."]], "pyvrp.plotting.plot_runtimes": [[2, 6, 1, 0, "plot_runtimes", "Plots iteration runtimes."]], "pyvrp.plotting.plot_runtimes.plot_runtimes": [[2, 2, 2, 0, "ax", "Axes object to draw the plot on."], [2, 2, 2, 0, "result", "Result for which to plot runtimes."]], "pyvrp.plotting.plot_solution": [[2, 6, 1, 0, "plot_solution", "Plots the given solution."]], "pyvrp.plotting.plot_solution.plot_solution": [[2, 2, 2, 0, "ax", "Axes object to draw the plot on."], [2, 2, 2, 0, "data", "Data instance underlying the solution."], [2, 2, 2, 0, "plot_clients", "Whether to plot all clients as dots."], [2, 2, 2, 0, "solution", "Solution to plot."]], "pyvrp.plotting.plot_time_windows": [[2, 6, 1, 0, "plot_time_windows", "Plots client time windows, as vertical bars sorted by time window."]], "pyvrp.plotting.plot_time_windows.plot_time_windows": [[2, 2, 2, 0, "ax", "Axes object to draw the plot on."], [2, 2, 2, 0, "data", "Data instance."], [2, 2, 2, 0, "title", "Title to add to the plot."]], "pyvrp.read": [[3, 6, 1, 0, "read", "Reads the VRPLIB file at the given location, and returns a ProblemData instance."], [3, 6, 1, 0, "read_solution", "Reads a solution in VRPLIB format from the give file location, and returns the corresponding Solution object."]], "pyvrp.read.read": [[3, 2, 2, 0, "round_func", "Optional rounding function that is applied to all data values in the instance."], [3, 2, 2, 0, "where", "File location to read."]], "pyvrp.read.read_solution": [[3, 2, 2, 0, "data", "Problem data instance that the solution is based on."], [3, 2, 2, 0, "where", "File location to read."]], "pyvrp.repair": [[4, 0, 0, 1, "_repair", ""]], "pyvrp.repair._repair": [[4, 6, 1, 0, "greedy_repair", "Greedy repair operator. This operator inserts each client in the list of unplanned clients into the given routes. It does so by evaluating all possible moves and applying the best one for each client, resulting in a quadratic runtime."], [4, 6, 1, 0, "nearest_route_insert", "Nearest route insert operator. This operator inserts each client in the list of unplanned clients into one of the given routes. It does so by first determining which route has a center point closest to the client, and then evaluating all possible insert moves of the client into that closest route. The best move is applied. This operator has a quadratic runtime in the worst case, but is typically much more efficient than greedy_repair(), at the cost of some solution quality."]], "pyvrp.repair._repair.greedy_repair": [[4, 2, 2, 0, "cost_evaluator", "Cost evaluator to use when evaluating insertion moves."], [4, 2, 2, 0, "data", "Problem data instance."], [4, 2, 2, 0, "routes", "List of routes."], [4, 2, 2, 0, "unplanned", "Unplanned clients to insert into the routes."]], "pyvrp.repair._repair.nearest_route_insert": [[4, 2, 2, 0, "cost_evaluator", "Cost evaluator to use when evaluating insertion moves."], [4, 2, 2, 0, "data", "Problem data instance."], [4, 2, 2, 0, "routes", "List of routes."], [4, 2, 2, 0, "unplanned", "Unplanned clients to insert into the routes."]], "pyvrp.search": [[5, 0, 0, 1, "LocalSearch", ""], [5, 0, 0, 1, "SearchMethod", ""], [5, 0, 0, 1, "_search", ""], [5, 0, 0, 1, "neighbourhood", ""]], "pyvrp.search.LocalSearch": [[5, 1, 1, 0, "LocalSearch", "Local search method. This search method explores a granular neighbourhood in a very efficient manner using user-provided node and route operators. This quickly results in much improved solutions."]], "pyvrp.search.LocalSearch.LocalSearch": [[5, 3, 1, 0, "__call__", "This method uses the search() and intensify() methods to iteratively improve the given solution. First, search() is applied. Thereafter, intensify() is applied. This repeats until no further improvements are found. Finally, the improved solution is returned."], [5, 3, 1, 0, "add_node_operator", "Adds a node operator to this local search object. The node operator will be used by search() to improve a solution."], [5, 3, 1, 0, "add_route_operator", "Adds a route operator to this local search object. The route operator will be used by intensify() to improve a solution using more expensive route operators."], [5, 3, 1, 0, "intensify", "This method uses the intensifying route operators on this local search object to improve the given solution."], [5, 3, 1, 0, "neighbours", "Returns the granular neighbourhood currently used by the local search."], [5, 3, 1, 0, "search", "This method uses the node operators on this local search object to improve the given solution."], [5, 3, 1, 0, "set_neighbours", "Convenience method to replace the current granular neighbourhood used by the local search object."]], "pyvrp.search.LocalSearch.LocalSearch.__call__": [[5, 2, 2, 0, "cost_evaluator", "Cost evaluator to use."], [5, 2, 2, 0, "solution", "The solution to improve through local search."]], "pyvrp.search.LocalSearch.LocalSearch.__init__": [[5, 2, 2, 0, "data", "Data object describing the problem to be solved."], [5, 2, 2, 0, "neighbours", "List of lists that defines the local search neighbourhood."], [5, 2, 2, 0, "rng", "Random number generator."]], "pyvrp.search.LocalSearch.LocalSearch.add_node_operator": [[5, 2, 2, 0, "op", "The node operator to add to this local search object."]], "pyvrp.search.LocalSearch.LocalSearch.add_route_operator": [[5, 2, 2, 0, "op", "The route operator to add to this local search object."]], "pyvrp.search.LocalSearch.LocalSearch.intensify": [[5, 2, 2, 0, "cost_evaluator", "Cost evaluator to use."], [5, 2, 2, 0, "solution", "The solution to improve."]], "pyvrp.search.LocalSearch.LocalSearch.search": [[5, 2, 2, 0, "cost_evaluator", "Cost evaluator to use."], [5, 2, 2, 0, "solution", "The solution to improve."]], "pyvrp.search.LocalSearch.LocalSearch.set_neighbours": [[5, 2, 2, 0, "neighbours", "A new granular neighbourhood."]], "pyvrp.search.SearchMethod": [[5, 1, 1, 0, "SearchMethod", "Protocol that search methods must implement."]], "pyvrp.search.SearchMethod.SearchMethod": [[5, 3, 1, 0, "__call__", "Search around the given solution, and returns a new solution that is hopefully better."]], "pyvrp.search.SearchMethod.SearchMethod.__call__": [[5, 2, 2, 0, "cost_evaluator", "Cost evaluator to use when evaluating improvements."], [5, 2, 2, 0, "solution", "The solution to improve."]], "pyvrp.search.SearchMethod.SearchMethod.__init__": [[5, 2, 2, "pyvrp.search.SearchMethod.SearchMethod", "args", "Protocol that search methods must implement."], [5, 2, 2, "pyvrp.search.SearchMethod.SearchMethod", "kwargs", "Protocol that search methods must implement."]], "pyvrp.search._search": [[5, 1, 1, 0, "Exchange10", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."], [5, 1, 1, 0, "Exchange11", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."], [5, 1, 1, 0, "Exchange20", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."], [5, 1, 1, 0, "Exchange21", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."], [5, 1, 1, 0, "Exchange22", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."], [5, 1, 1, 0, "Exchange30", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."], [5, 1, 1, 0, "Exchange31", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."], [5, 1, 1, 0, "Exchange32", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."], [5, 1, 1, 0, "Exchange33", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."], [5, 1, 1, 0, "NodeOperator", "!! processed by numpydoc !!"], [5, 1, 1, 0, "RouteOperator", "!! processed by numpydoc !!"], [5, 1, 1, 0, "SwapRoutes", "This operator evaluates exchanging the visits of two routes U and V."], [5, 1, 1, 0, "SwapStar", "Explores the SWAP* neighbourhood of R6fbb5e500902-1. The SWAP* neighbourhood consists of free form re-insertions of clients U and V in the given routes (so the clients are swapped, but they are not necessarily inserted in the place of the other swapped client)."], [5, 1, 1, 0, "SwapTails", "Given two nodes U and V, tests whether replacing the arc of U to its successor n(U) and V to n(V) by U \\rightarrow n(V) and V \\rightarrow n(U) is an improving move."], [5, 1, 1, 0, "TripRelocate", "Tests if inserting a reload depot while relocating U after V results in an improving move. Concretely, this operator implements the second and third insertion scheme of Francois et al. R6cc30bb34989-1."]], "pyvrp.search._search.Exchange10.__init__": [[5, 2, 2, "pyvrp.search._search.Exchange10", "data", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."]], "pyvrp.search._search.Exchange11.__init__": [[5, 2, 2, "pyvrp.search._search.Exchange11", "data", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."]], "pyvrp.search._search.Exchange20.__init__": [[5, 2, 2, "pyvrp.search._search.Exchange20", "data", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."]], "pyvrp.search._search.Exchange21.__init__": [[5, 2, 2, "pyvrp.search._search.Exchange21", "data", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."]], "pyvrp.search._search.Exchange22.__init__": [[5, 2, 2, "pyvrp.search._search.Exchange22", "data", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."]], "pyvrp.search._search.Exchange30.__init__": [[5, 2, 2, "pyvrp.search._search.Exchange30", "data", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."]], "pyvrp.search._search.Exchange31.__init__": [[5, 2, 2, "pyvrp.search._search.Exchange31", "data", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."]], "pyvrp.search._search.Exchange32.__init__": [[5, 2, 2, "pyvrp.search._search.Exchange32", "data", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."]], "pyvrp.search._search.Exchange33.__init__": [[5, 2, 2, "pyvrp.search._search.Exchange33", "data", "The (N, M)-exchange operators exchange N consecutive clients from U's route (starting at U) with M consecutive clients from V's route (starting at V). This includes the RELOCATE and SWAP operators as special cases."]], "pyvrp.search._search.SwapRoutes.__init__": [[5, 2, 2, "pyvrp.search._search.SwapRoutes", "data", "This operator evaluates exchanging the visits of two routes U and V."]], "pyvrp.search._search.SwapStar.__init__": [[5, 2, 2, "pyvrp.search._search.SwapStar", "data", "Explores the SWAP* neighbourhood of R6fbb5e500902-1. The SWAP* neighbourhood consists of free form re-insertions of clients U and V in the given routes (so the clients are swapped, but they are not necessarily inserted in the place of the other swapped client)."], [5, 2, 2, "pyvrp.search._search.SwapStar", "overlap_tolerance", "Explores the SWAP* neighbourhood of R6fbb5e500902-1. The SWAP* neighbourhood consists of free form re-insertions of clients U and V in the given routes (so the clients are swapped, but they are not necessarily inserted in the place of the other swapped client)."]], "pyvrp.search._search.SwapTails.__init__": [[5, 2, 2, "pyvrp.search._search.SwapTails", "data", "Given two nodes U and V, tests whether replacing the arc of U to its successor n(U) and V to n(V) by U \\rightarrow n(V) and V \\rightarrow n(U) is an improving move."]], "pyvrp.search._search.TripRelocate.__init__": [[5, 2, 2, "pyvrp.search._search.TripRelocate", "data", "Tests if inserting a reload depot while relocating U after V results in an improving move. Concretely, this operator implements the second and third insertion scheme of Francois et al. R6cc30bb34989-1."]], "pyvrp.search.neighbourhood": [[5, 1, 1, 0, "NeighbourhoodParams", "Configuration for calculating a granular neighbourhood."], [5, 6, 1, 0, "compute_neighbours", "Computes neighbours defining the neighbourhood for a problem instance."]], "pyvrp.search.neighbourhood.NeighbourhoodParams": [[5, 4, 1, 0, "nb_granular", "Number of other clients that are in each client's granular neighbourhood. This parameter determines the size of the overall neighbourhood."], [5, 4, 1, 0, "symmetric_neighbours", "Whether to symmetrise the neighbourhood structure. This ensures that when edge (i, j) is in, then so is (j, i). Note that this is not the same as symmetric_proximity."], [5, 4, 1, 0, "symmetric_proximity", "Whether to calculate a symmetric proximity matrix. This ensures edge (i, j) is given the same weight as (j, i)."], [5, 4, 1, 0, "weight_time_warp", "Penalty weight given to the minimum time warp aspect of the proximity calculation. A large time warp indicates the clients are far apart in duration/time."], [5, 4, 1, 0, "weight_wait_time", "Penalty weight given to the minimum wait time aspect of the proximity calculation. A large wait time indicates the clients are far apart in duration/time."]], "pyvrp.search.neighbourhood.NeighbourhoodParams.__init__": [[5, 2, 2, "pyvrp.search.neighbourhood.NeighbourhoodParams", "nb_granular", "Configuration for calculating a granular neighbourhood."], [5, 2, 2, "pyvrp.search.neighbourhood.NeighbourhoodParams", "symmetric_neighbours", "Configuration for calculating a granular neighbourhood."], [5, 2, 2, "pyvrp.search.neighbourhood.NeighbourhoodParams", "symmetric_proximity", "Configuration for calculating a granular neighbourhood."], [5, 2, 2, "pyvrp.search.neighbourhood.NeighbourhoodParams", "weight_time_warp", "Configuration for calculating a granular neighbourhood."], [5, 2, 2, "pyvrp.search.neighbourhood.NeighbourhoodParams", "weight_wait_time", "Configuration for calculating a granular neighbourhood."]], "pyvrp.search.neighbourhood.compute_neighbours": [[5, 2, 2, 0, "data", "ProblemData for which to compute the neighbourhood."], [5, 2, 2, 0, "params", "NeighbourhoodParams that define how the neighbourhood is computed."]], "pyvrp.show_versions": [[3, 6, 1, 0, "show_versions", "This function prints version information that is useful when filing bug reports."]], "pyvrp.solve": [[3, 1, 1, 0, "SolveParams", "Solver parameters for PyVRP's hybrid genetic search algorithm."], [3, 6, 1, 0, "solve", "Solves the given problem data instance."]], "pyvrp.solve.SolveParams": [[3, 3, 1, 0, "from_file", "Loads the solver parameters from a TOML file."]], "pyvrp.solve.SolveParams.__init__": [[3, 2, 2, 0, "genetic", "Genetic algorithm parameters."], [3, 2, 2, 0, "neighbourhood", "Neighbourhood parameters."], [3, 2, 2, 0, "node_ops", "Node operators to use in the search."], [3, 2, 2, 0, "penalty", "Penalty parameters."], [3, 2, 2, 0, "population", "Population parameters."], [3, 2, 2, 0, "route_ops", "Route operators to use in the search."]], "pyvrp.solve.SolveParams.from_file": [[3, 2, 2, "pyvrp.solve.SolveParams.from_file", "loc", "Loads the solver parameters from a TOML file."]], "pyvrp.solve.solve": [[3, 2, 2, 0, "collect_stats", "Whether to collect statistics about the solver's progress."], [3, 2, 2, 0, "data", "Problem data instance to solve."], [3, 2, 2, 0, "display", "Whether to display information about the solver progress."], [3, 2, 2, 0, "params", "Solver parameters to use."], [3, 2, 2, 0, "seed", "Seed value to use for the random number stream."], [3, 2, 2, 0, "stop", "Stopping criterion to use."]], "pyvrp.stop": [[6, 0, 0, 1, "FirstFeasible", ""], [6, 0, 0, 1, "MaxIterations", ""], [6, 0, 0, 1, "MaxRuntime", ""], [6, 0, 0, 1, "MultipleCriteria", ""], [6, 0, 0, 1, "NoImprovement", ""], [6, 0, 0, 1, "StoppingCriterion", ""]], "pyvrp.stop.FirstFeasible": [[6, 1, 1, 0, "FirstFeasible", "Terminates the search after a feasible solution has been observed."]], "pyvrp.stop.MaxIterations": [[6, 1, 1, 0, "MaxIterations", "Criterion that stops after a maximum number of iterations."]], "pyvrp.stop.MaxIterations.MaxIterations.__init__": [[6, 2, 2, "pyvrp.stop.MaxIterations.MaxIterations", "max_iterations", "Criterion that stops after a maximum number of iterations."]], "pyvrp.stop.MaxRuntime": [[6, 1, 1, 0, "MaxRuntime", "Criterion that stops after a specified maximum runtime (in seconds)."]], "pyvrp.stop.MaxRuntime.MaxRuntime.__init__": [[6, 2, 2, "pyvrp.stop.MaxRuntime.MaxRuntime", "max_runtime", "Criterion that stops after a specified maximum runtime (in seconds)."]], "pyvrp.stop.MultipleCriteria": [[6, 1, 1, 0, "MultipleCriteria", "Simple aggregate class that manages multiple stopping criteria at once."]], "pyvrp.stop.MultipleCriteria.MultipleCriteria.__init__": [[6, 2, 2, "pyvrp.stop.MultipleCriteria.MultipleCriteria", "criteria", "Simple aggregate class that manages multiple stopping criteria at once."]], "pyvrp.stop.NoImprovement": [[6, 1, 1, 0, "NoImprovement", "Criterion that stops if the best solution has not been improved for a fixed number of iterations."]], "pyvrp.stop.NoImprovement.NoImprovement.__init__": [[6, 2, 2, 0, "max_iterations", "The maximum number of non-improving iterations."]], "pyvrp.stop.StoppingCriterion": [[6, 1, 1, 0, "StoppingCriterion", "Protocol that stopping criteria must implement."]], "pyvrp.stop.StoppingCriterion.StoppingCriterion": [[6, 3, 1, 0, "__call__", "When called, this stopping criterion should return True if the algorithm should stop, and False otherwise."]], "pyvrp.stop.StoppingCriterion.StoppingCriterion.__call__": [[6, 2, 2, 0, "best_cost", "Cost of current best solution."]], "pyvrp.stop.StoppingCriterion.StoppingCriterion.__init__": [[6, 2, 2, "pyvrp.stop.StoppingCriterion.StoppingCriterion", "args", "Protocol that stopping criteria must implement."], [6, 2, 2, "pyvrp.stop.StoppingCriterion.StoppingCriterion", "kwargs", "Protocol that stopping criteria must implement."]]}, "objnames": {"0": ["py", "module", "Python module"], "1": ["py", "class", "Python class"], "2": ["py", "parameter", "Python parameter"], "3": ["py", "method", "Python method"], "4": ["py", "attribute", "Python attribute"], "5": ["py", "property", "Python property"], "6": ["py", "function", "Python function"], "7": ["py", "exception", "Python exception"]}, "objtypes": {"0": "py:module", "1": "py:class", "2": "py:parameter", "3": "py:method", "4": "py:attribute", "5": "py:property", "6": "py:function", "7": "py:exception"}, "terms": {"": [0, 1, 2, 3, 5, 6, 8, 10, 11, 12, 13, 15, 18, 19, 22], "0": [1, 2, 3, 5, 8, 10, 12, 13, 14, 16, 23], "00": [13, 14], "0055": 17, "01": 16, "05": 5, "07": 14, "09": 16, "0909": 5, "1": [0, 1, 2, 3, 5, 10, 11, 12, 13, 14, 16, 23], "10": [3, 5, 12, 13, 14, 16, 17], "100": [7, 12, 14], "1000": [3, 7, 12, 13, 14], "1002": 13, "1004": 13, "101": 12, "1016": 5, "102": 12, "103": 12, "1030": 13, "104": 12, "105": 12, "105643": 5, "106": 12, "107": 12, "108": 12, "109": 12, "11": [12, 13, 14, 16], "110": 12, "111": 12, "112": 12, "113": 12, "114": [12, 13], "115": 12, "116": 12, "117": 12, "118": 12, "1183": 13, "1186": 7, "119": 12, "12": [3, 12, 13, 14], "120": 12, "121": 12, "122": 12, "123": 12, "124": 12, "125": [12, 13], "126": 12, "127": 12, "1271": 13, "128": 12, "1287": [5, 17], "1289": 13, "129": 12, "13": [3, 12, 13, 14, 16], "130": 12, "131": 12, "132": 12, "1325": 12, "133": 12, "134": 12, "135": 12, "136": 12, "137": 12, "138": 12, "139": 12, "14": [12, 13, 14], "140": [5, 12], "141": 12, "142": 12, "143": 12, "144": 12, "145": 12, "146": 12, "147": 12, "148": 12, "149": 12, "15": [2, 12, 13, 14, 16], "150": 12, "151": 12, "152": 12, "153": 12, "154": 12, "155": 12, "156": 12, "157": 12, "158": 12, "159": 12, "16": [12, 13, 14, 16], "160": [12, 13], "161": 12, "162": 12, "1620": 13, "163": 12, "164": 12, "165": 12, "166": 12, "167": 12, "168": 12, "169": 12, "17": [12, 13, 14], "170": 12, "1700": 12, "1706": 5, "171": 12, "172": 12, "173": 12, "1730": 5, "174": 12, "175": 12, "176": 12, "177": 12, "17761": [12, 14], "178": 12, "179": 12, "18": [12, 13, 14, 16], "180": 12, "181": 12, "182": 12, "183": 12, "184": 12, "185": 12, "186": 12, "187": 12, "188": 12, "189": 12, "19": [12, 13, 14], "190": 12, "191": 12, "192": 12, "193": 12, "194": 12, "1940": 13, "195": 12, "196": 12, "197": 12, "198": 12, "1983": 12, "1987": 0, "199": 12, "1_": 1, "1_000": 13, "1f": 12, "2": [1, 3, 5, 7, 8, 12, 13, 14, 16], "20": [8, 12, 13, 14, 16], "200": [7, 12, 14], "2000": [7, 12], "20000": 3, "201": 12, "2010": 0, "2013": [7, 16, 22], "2014": 23, "2018": 7, "2019": 5, "202": 12, "2020": [7, 16], "2021": 5, "2022": [5, 17, 22], "2023": [7, 16, 17], "2024": [7, 16, 17], "2025": 16, "203": 12, "204": 12, "205": 12, "206": 12, "207": 12, "208": 12, "209": 12, "21": [12, 13, 14, 16], "210": 12, "211": 12, "212": 12, "213": 12, "214": 12, "215": 12, "216": 12, "217": 12, "218": 12, "2183": 7, "2187": 12, "219": 12, "22": [12, 13, 14, 16], "220": 12, "221": 12, "222": 12, "223": 12, "224": [0, 12], "225": 12, "226": 12, "2266": 12, "227": 12, "228": [12, 13], "229": 12, "23": [12, 13, 14, 16], "230": [0, 12], "231": 12, "232": 12, "233": 12, "234": 12, "235": 12, "236": 12, "237": 12, "238": 12, "239": 12, "24": [3, 12, 13, 14, 16], "240": [7, 12, 13], "2400": 12, "241": 12, "242": 12, "243": 12, "244": 12, "245": 12, "246": 12, "247": 12, "248": 12, "249": 12, "25": [3, 12, 13, 14, 16], "250": 12, "2500": 7, "251": 12, "252": 12, "253": 12, "254": 12, "255": 12, "256": 12, "257": 12, "25797": 12, "258": 12, "259": 12, "26": [12, 13, 14], "260": 12, "261": 12, "262": 12, "263": 12, "264": 12, "265": 12, "266": 12, "267": 12, "268": 12, "269": 12, "27": [12, 13, 14, 16], "270": 12, "2700": 12, "271": 12, "272": 12, "273": 12, "274": 12, "275": [12, 13], "276": 12, "27667": 12, "277": 12, "278": 12, "279": 12, "28": [12, 13, 14, 16], "280": 12, "281": 12, "282": 12, "283": 12, "284": 12, "285": [12, 14], "286": 12, "2860": 12, "287": 12, "288": 12, "289": 12, "29": [12, 13, 14, 16], "290": 12, "291": 12, "292": 12, "293": 12, "294": 12, "295": [12, 13], "296": 12, "297": 12, "298": 12, "299": 12, "2991": 12, "2n": 1, "3": [1, 3, 10, 12, 13, 14, 16], "30": [12, 13, 14], "300": 12, "301": 12, "302": [12, 13], "303": 12, "304": 12, "305": 12, "306": 12, "307": 12, "308": 12, "309": 12, "31": [12, 13, 14, 16], "310": 12, "311": 12, "312": 12, "313": 12, "314": 12, "3149": 12, "315": 12, "316": 12, "317": 12, "318": 12, "319": 12, "32": [3, 12, 14, 16], "320": [12, 13], "3200": 12, "321": 12, "322": 12, "323": 12, "324": 12, "3249": 12, "325": 12, "326": 12, "327": 12, "328": 12, "329": 12, "33": [12, 14], "330": 12, "331": 12, "332": 12, "333": 12, "334": [12, 13], "335": 12, "336": 12, "337": 12, "338": 12, "339": 12, "34": [3, 12, 14], "340": 12, "3400": 13, "341": 12, "342": [12, 13], "3429": 12, "343": 12, "344": 12, "345": 12, "346": 12, "347": [12, 13], "348": 12, "349": 12, "35": [12, 14, 16], "350": 12, "351": 12, "352": 12, "3529": 12, "353": 12, "354": 12, "3549": 12, "355": 12, "356": 12, "357": 12, "358": 12, "359": 12, "36": [12, 14, 17], "360": [7, 12], "361": 12, "362": 12, "363": 12, "36391": 12, "364": 12, "3649": 12, "365": 12, "366": 12, "36636": 12, "367": 12, "368": 12, "369": [12, 13], "37": [12, 14], "370": 12, "3702": 12, "371": 12, "372": 12, "373": 12, "374": 12, "375": 12, "376": 12, "377": 12, "378": 12, "379": 12, "38": [12, 14, 16], "380": [12, 13], "3802": 12, "381": 12, "3810": 12, "382": [12, 13], "3822": 12, "383": 12, "384": 12, "385": 12, "386": 12, "387": 12, "388": 12, "389": 12, "39": [12, 13, 14, 16], "390": 12, "391": 12, "392": 12, "3922": 12, "393": 12, "394": 12, "395": 12, "396": 12, "397": 12, "398": 12, "3980": 12, "399": [12, 13], "4": [1, 3, 7, 12, 13, 14, 16, 17], "40": [3, 5, 12, 13, 14, 16], "400": [12, 13], "401": 12, "402": 12, "403": 12, "404": 12, "405": 12, "406": 12, "4067": 16, "407": 12, "408": 12, "4080": 12, "409": [12, 13], "41": [12, 14, 16], "410": [12, 13], "4100": 12, "411": [12, 13], "412": 12, "413": [12, 13], "414": 12, "415": 12, "416": 12, "417": 12, "418": 12, "419": 12, "42": [12, 14, 16], "420": 12, "4200": 12, "421": 12, "422": 12, "423": 12, "4236": 12, "424": 12, "425": 12, "426": 12, "427": 12, "428": 12, "429": 12, "43": [3, 12, 14, 16], "430": 12, "431": 12, "432": 12, "433": 12, "4336": 12, "434": 12, "435": 12, "436": 12, "437": 12, "438": 12, "4394": 12, "44": [12, 14, 16], "4494": 12, "45": [12, 13, 14, 16], "4544": 12, "456": 13, "46": [12, 13, 14], "4644": 12, "4650": 12, "47": [12, 14], "471": 13, "4748": 12, "48": [12, 14, 16], "480": 13, "484": 13, "4848": 12, "49": [12, 14], "4959": 12, "5": [2, 3, 8, 12, 13, 14, 16], "50": [3, 12, 14], "500": [13, 14], "5059": 12, "51": [12, 14], "5145": 13, "5160": 12, "52": [12, 14], "523": 7, "5235": 13, "5260": 12, "53": [5, 12, 14], "5310": 12, "536": 0, "5375": 13, "54": [12, 14, 16], "5410": 12, "545": 0, "5473": 12, "55": [12, 14], "5573": 12, "56": [12, 14], "560": 13, "5633": 12, "57": [12, 14], "570": 13, "5728": 13, "5733": 12, "5796": 12, "58": [12, 14, 16], "5896": 12, "59": [12, 14], "5905": 13, "5920": 12, "6": [5, 8, 12, 13, 14, 16], "60": [8, 12, 14, 16], "6004": 13, "6016": 12, "61": [12, 14, 16], "6198": 13, "62": [12, 14, 16], "6208": 13, "6219": 13, "6230": 13, "63": [12, 14], "64": [3, 12, 14], "640": 13, "65": [12, 13, 14], "6528": 13, "66": [12, 14], "67": [12, 14], "68": [12, 14], "684": 13, "69": [12, 13, 14], "6995": 13, "7": [3, 12, 13, 14, 16], "70": [12, 14], "71": [12, 14, 16], "72": [12, 14, 16], "73": [12, 14], "74": [12, 14], "75": [12, 14], "76": [12, 14, 16], "77": [12, 14], "776": 12, "7761": [12, 14], "78": [12, 14], "79": [12, 13, 14], "79611": 14, "798": 13, "8": [3, 12, 13, 14, 16], "80": [8, 12, 13, 14], "81": [12, 14], "82": [12, 14], "83": [12, 14], "84": [12, 14], "85": [12, 14], "850": 13, "8565": 14, "8579": 14, "86": [12, 14], "87": [12, 14], "88": [12, 14], "882": 13, "89": [12, 14], "9": [3, 12, 13, 14, 16], "90": [7, 12, 14], "91": [12, 14], "912": 13, "92": [12, 14], "93": [12, 14, 16], "94": [12, 14], "943": 17, "95": [2, 12, 14, 16], "955": 17, "96": [12, 14], "960": 7, "97": [12, 14], "98": [12, 14], "99": [12, 14, 16], "999": 13, "A": [0, 1, 3, 5, 9, 10, 11, 12, 15, 17, 18], "As": [5, 23], "At": [1, 3, 23], "But": 14, "By": [3, 13, 14], "For": [7, 8, 10, 12, 14, 22, 23], "If": [1, 3, 8, 10, 11, 13, 15, 17, 19, 20, 22, 23], "In": [0, 3, 10, 11, 12, 13, 14, 16, 18, 22, 23], "It": [0, 3, 4, 10, 12, 13, 14, 15, 23], "No": 3, "OR": [13, 16], "Of": [10, 11, 12], "On": [10, 11], "One": [2, 8], "Or": 17, "Such": [8, 10, 13], "That": [1, 3, 14], "The": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 15, 16, 18, 19, 21, 22, 23], "Then": [1, 8, 21], "There": 8, "These": [2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16], "To": [4, 7, 8, 10, 11, 12, 14, 15, 20, 21], "With": 10, "_": [12, 13, 14], "__and__": 3, "__call__": [3, 5, 6], "__eq__": 3, "__getitem__": 3, "__invert__": 3, "__iter__": 3, "__len__": 3, "__or__": 3, "__setitem__": 3, "__xor__": 3, "_r": 3, "ab": 13, "abl": [3, 10], "about": [2, 3, 7, 8, 10, 12, 13, 14, 19], "abov": [3, 12, 13, 14], "accept": 8, "accord": 3, "account": 10, "achiev": 3, "actual": [3, 11, 12], "ad": [3, 5, 8, 10, 13, 14, 19, 22], "adapt": 5, "adc": 16, "add": [1, 2, 3, 5, 8, 10, 13, 14, 22, 23], "add_client": [3, 13], "add_client_group": 3, "add_depot": [3, 13], "add_edg": [3, 13], "add_node_oper": [5, 14], "add_profil": [3, 13], "add_route_oper": [5, 14], "add_vehicle_typ": [3, 13], "addit": [3, 10, 11, 13, 14, 15, 23], "addition": [7, 12, 13, 14, 18], "address": 16, "adher": 11, "adjust": 14, "admit": 3, "advanc": 3, "after": [3, 4, 5, 6, 7, 8, 10, 13, 18, 21, 22], "again": [12, 13, 14], "against": 16, "aggreg": [3, 6], "aim": 13, "al": [5, 7, 16, 22], "algo": 14, "algorithm": [0, 1, 2, 3, 6, 12, 16, 22, 23], "all": [2, 3, 4, 5, 6, 8, 10, 11, 12, 13, 14, 19, 20, 21, 23], "allow": [1, 3, 4, 8, 11, 12, 13, 14, 18, 22, 23], "along": [3, 13, 14, 15], "alpha": 13, "alreadi": [3, 10, 13, 18, 20], "also": [1, 3, 5, 10, 12, 13, 14, 17, 21, 22, 23], "altern": 3, "although": 11, "altogeth": 14, "alwai": [1, 3, 8, 10, 16], "amongst": 3, "amount": [3, 11, 12, 13, 20], "an": [0, 3, 5, 7, 8, 10, 11, 12, 13, 14, 15, 16, 19, 22, 23], "analys": 3, "analyz": 8, "ani": [0, 3, 4, 8, 10, 11, 23], "annot": 3, "anoth": [0, 8], "answer": 20, "anywher": 9, "apart": 5, "api": [12, 13, 14], "appendix": 16, "appl": 8, "appli": [3, 4, 5, 10, 13, 22], "applic": [0, 8, 23], "approach": 22, "appropri": [0, 3, 8, 12], "ar": [0, 1, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 23], "arc": [5, 10, 12, 23], "arda": 5, "area": 13, "arg": [5, 6], "argument": [3, 8, 12, 13], "ari": [3, 22], "around": [5, 10], "arrai": 11, "arriv": [3, 12, 18, 23], "art": [15, 23], "articl": 17, "arxiv": 17, "ask": [14, 19], "aspect": [5, 12], "assert": 14, "assign": [3, 11, 13, 18, 23], "associ": [3, 5], "assum": [0, 3, 7, 11, 12, 23], "attach": 8, "attain": 13, "attempt": 3, "attent": 8, "attribut": [3, 8, 18, 19], "august": 16, "author": [8, 17], "automat": [3, 8, 13], "avail": [3, 5, 7, 10, 12, 14, 17, 19, 23], "averag": [7, 12, 13, 14, 16], "avg": 13, "avoid": [3, 8, 13], "ax": [2, 12, 13], "axarr": 12, "axi": [2, 12], "b": 16, "back": [3, 11, 13, 22], "background": [2, 12], "backhaul": [11, 15], "backhaul_sect": 11, "bad": 14, "balanc": [1, 3, 14, 23], "bar": [2, 8, 12], "base": [3, 7, 8, 9, 11, 12, 14], "baselin": 10, "basi": 10, "basic": [10, 12, 13], "becaus": [3, 13, 14, 16], "becom": 13, "been": [3, 6, 16, 20, 23], "befor": [3, 12, 14, 16, 18, 23], "begin": [3, 12, 18, 23], "behaviour": 13, "being": [3, 13], "below": [8, 11, 15, 16, 18], "benchmark": [10, 11, 12, 14, 15, 23], "benefici": 14, "benefit": 15, "besid": 13, "best": [2, 3, 4, 6, 7, 12, 13, 14, 16, 22], "best_cost": 6, "better": [1, 2, 5, 12, 14, 22], "between": [0, 1, 3, 13, 15, 18], "bibtex": 17, "bicyclist": 3, "bigger": 3, "binari": [3, 15, 19], "bind": 10, "bit": 3, "bitset": 3, "bk": 12, "blank": 8, "block_siz": 3, "bodi": 8, "bool": [2, 3, 5, 6, 10, 13], "boost": 3, "booster": 3, "booster_cost_evalu": 3, "both": [0, 3, 13, 14, 22], "bottom": [12, 14], "bound": [2, 3], "box": 13, "bpd": 1, "branch": [8, 23], "break": 8, "breakpoint": 8, "brief": [13, 15], "briefli": 13, "broken": 1, "broken_pairs_dist": [1, 14], "brows": 20, "browser": [8, 21], "bug": 3, "build": [12, 14, 19], "build_extens": 8, "build_typ": 8, "built": 8, "bunch": 8, "bundl": 8, "button": 8, "c": [0, 3, 5, 8, 9, 10, 20, 22], "calcul": 5, "call": [3, 6, 8, 12, 13, 14], "callabl": 3, "can": [0, 2, 3, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 23], "candid": 22, "cannot": [3, 13, 19], "capabl": 22, "capac": [3, 11, 12, 13, 15, 23], "capacit": 15, "capacity_sect": 11, "car": 3, "carefulli": 0, "case": [0, 3, 4, 5, 10, 11, 12, 13, 18], "caus": [3, 10, 16], "cd": [8, 21], "center": [3, 4], "centroid": 3, "certain": [3, 18], "challeng": 12, "chang": [10, 13, 16], "charact": 8, "charg": 14, "cheap": 8, "check": [3, 8, 10, 23], "circl": 8, "cite": 15, "class": [3, 5, 6, 8, 10], "classic": 15, "classifi": 23, "classmethod": 3, "clear": [3, 8, 14], "click": 8, "client": [0, 1, 2, 3, 4, 5, 7, 8, 10, 11, 12, 13, 14, 15, 23], "clientgroup": 3, "clip": 3, "clone": [8, 21], "close": [3, 13, 14, 18], "closest": 4, "code": [8, 10, 16, 20], "codebas": 9, "collect": [3, 10, 12, 15, 16], "collect_from": 3, "collect_stat": 3, "color": 13, "com": [8, 21], "combin": [0, 12, 22], "come": [8, 10, 15], "comma": 3, "command": [7, 8, 20], "commonli": [11, 13], "compar": [7, 13, 16], "compat": 8, "compet": 23, "compil": [7, 8, 15, 19], "complet": [1, 3, 8, 12, 18, 23], "complex": 23, "compon": [3, 8, 10, 13, 15], "composit": 3, "comput": [1, 3, 5, 7, 9, 10, 11, 12, 14, 17, 23], "computation": 8, "compute_neighbour": [5, 10, 14], "concaten": 9, "concept": [3, 15], "concis": 8, "conclud": 13, "concret": [5, 10], "condit": 14, "confer": 0, "config": 7, "configur": [3, 5, 7, 8, 12], "conform": 8, "connect": 3, "consecut": 5, "consid": [1, 3, 10, 13, 17, 19, 23], "consider": 23, "consist": [0, 3, 5, 12, 13, 14, 23], "constrain": [3, 18], "constraint": [3, 10, 13, 15, 23], "construct": [3, 14], "constructor": 8, "contain": [1, 2, 3, 5, 6, 8, 12, 16, 20, 21], "content": 8, "contigu": [9, 11], "continu": [14, 22], "contribut": [10, 15, 23], "contributor": 8, "control": [3, 4], "conveni": [5, 12, 13], "convent": 12, "convers": [8, 20], "convert": [12, 13], "coord": 13, "coordin": [2, 3, 11, 12, 13], "copi": [0, 3], "copyright": 8, "cor": 5, "core": [3, 7], "corner": 8, "correct": [10, 16], "correctli": 8, "correspond": [3, 8], "cost": [0, 3, 4, 5, 6, 9, 10, 11, 12, 13, 15, 23], "cost_evalu": [0, 3, 4, 5, 14], "costevalu": [0, 3, 4, 5, 10, 14], "could": [0, 3, 10, 14], "count": [1, 3], "cours": [10, 12], "cover": 13, "cpp": 8, "cppconfig": 8, "cpu": [7, 8], "crama": 5, "creat": [0, 2, 3, 4, 5, 8, 14, 23], "criteria": [14, 15, 22], "criterion": [3, 6, 7, 12, 14, 16, 22], "cross": 8, "crossov": [3, 5, 15, 22], "crossover_op": 3, "csv": 3, "cup": [12, 23], "current": [3, 5, 6, 12, 14, 15, 16, 23], "custom": [9, 12], "customis": 14, "cut": 23, "cvrp": [5, 7, 10, 12, 16, 17, 23], "d": 0, "d_": [10, 12, 23], "d_r": 3, "dash": 2, "data": [0, 2, 3, 4, 5, 8, 10, 12, 13, 14, 18], "date": 16, "debugg": 8, "debugoptim": 8, "decid": 10, "decim": [12, 13], "decis": [3, 13], "decreas": 3, "def": [13, 14], "default": [2, 3, 8, 10, 11, 14], "defin": [1, 3, 5, 9, 11, 12, 13, 14], "definit": [1, 3, 14], "delai": 3, "delet": 8, "delimit": 3, "deliv": 13, "deliveri": [0, 3, 11, 12, 15, 23], "demand": [2, 3, 11, 12, 13, 23], "demand_sect": 11, "demonstr": 12, "denomin": 1, "denot": [2, 12, 23], "departur": 3, "depend": [3, 8, 10, 11, 15, 21], "depot": [1, 2, 3, 5, 10, 11, 12, 15, 19], "depot1": 13, "depot2": 13, "depot_sect": 11, "describ": [3, 5, 10, 11, 14], "descript": [11, 13], "destruct": 4, "detail": [0, 2, 3, 8, 12, 13, 14, 16], "determin": [1, 3, 4, 5, 13, 14, 23], "dev": 8, "develop": [8, 10, 19, 23], "diagnost": 12, "dictread": 3, "dictwrit": 3, "did": 8, "differ": [0, 1, 3, 5, 7, 12, 13, 14, 15, 18], "difficulti": 19, "dimac": [3, 7, 8, 12, 14, 16], "dimens": [2, 3, 11], "direct": 1, "directli": [8, 21], "directori": 8, "discov": 8, "discuss": [8, 10, 13, 20], "displai": [3, 10, 12, 13], "dissimilar": 14, "dist_penalti": [3, 14], "distanc": [1, 2, 3, 10, 11, 12, 13, 14, 15, 23], "distance_cost": 3, "distance_matric": 3, "distance_matrix": 3, "distinct": 13, "divers": [2, 3, 12, 14, 15, 22], "diversity_op": 3, "do": [1, 3, 8, 10, 12, 13, 14, 19, 20, 23], "doc": 8, "docstr": 8, "document": [8, 10, 12, 13, 14], "doe": [0, 3, 4, 10, 19], "doi": [5, 17], "don": 10, "done": [8, 14, 21], "dot": [2, 12, 22, 23], "down": 14, "draft": 10, "draw": [2, 3], "drive": [2, 12], "drop": 3, "due": [0, 13, 14, 16], "duplic": 3, "durat": [3, 5, 11, 12, 13, 14, 15, 19], "duration_cost": 3, "duration_matric": 3, "duration_matrix": [3, 13], "durationseg": 9, "dure": [10, 12, 18], "dynam": [3, 9], "dynamicbitset": 3, "e": [3, 10, 11, 20, 23], "e_i": [12, 23], "each": [0, 1, 2, 3, 4, 5, 7, 8, 11, 12, 13, 14, 16, 23], "earli": [2, 3, 10, 12], "earliest": [2, 3, 12, 18, 23], "easi": [8, 10], "easier": 20, "easiest": 14, "easili": [14, 15], "edg": [3, 5, 10, 11, 13, 14, 19], "edge_weight_format": 11, "edge_weight_sect": 11, "edge_weight_typ": 11, "edit": 8, "effect": [13, 22, 23], "effici": [4, 5, 22, 23], "effort": 3, "either": [3, 10, 13], "elit": 3, "els": 9, "emiss": 13, "empti": [3, 4, 5, 14], "encod": 3, "end": [3, 12, 13, 23], "end_depot": [3, 13], "end_servic": [3, 12], "end_tim": 3, "enforc": 18, "enorm": 23, "enough": 8, "ensur": [3, 5, 8, 10, 13, 14, 22], "enter": 13, "entri": [9, 17, 19], "enumer": [12, 13], "enviro": 19, "environ": [8, 13, 21], "equal": [3, 12], "equip": [3, 18], "equival": [3, 19], "error": [10, 16], "et": [5, 7, 16, 22], "euclidean": 12, "evalu": [0, 3, 4, 5, 10, 12, 23], "everi": [3, 8, 22], "everyth": [8, 10, 14, 21], "exact": [3, 7, 23], "exactli": [3, 11, 23], "exampl": [3, 6, 8, 9, 10, 12, 13, 14, 20, 23], "exce": [3, 22, 23], "exceed": 6, "except": 3, "excess": 3, "excess_dist": 3, "excess_load": 3, "exchang": [0, 5, 14], "exchange10": [5, 10], "exchange11": 5, "exchange20": 5, "exchange21": 5, "exchange22": 5, "exchange30": 5, "exchange31": 5, "exchange32": 5, "exchange33": 5, "exclud": [2, 3], "exclus": [3, 11], "execut": [8, 13, 18], "exercis": 10, "exist": [2, 3, 8, 10, 20, 22], "expens": 5, "experi": 10, "explain": [8, 11, 18], "explan": [3, 10], "explicitli": [0, 3, 8, 11], "exploit": 22, "explor": [5, 22], "expos": 3, "exposit": 12, "extend": 17, "extens": 23, "f": [1, 12, 13], "f_r": 3, "fals": [2, 3, 5, 6, 12, 13], "familiar": 13, "familiaris": 13, "faq": [13, 15, 20], "far": [5, 13, 14, 22], "fast": 3, "faster": 14, "favour": 22, "feasibl": [2, 3, 6, 9, 10, 12, 13, 14, 18, 23], "featur": [8, 10, 13, 22], "februari": 16, "feed": 3, "feel": [8, 9, 19, 20], "few": [8, 12, 14, 21], "fewer": 16, "field": [3, 10, 23], "fig": [2, 12, 14], "figsiz": [12, 13, 14], "figur": [2, 12, 13, 14, 18, 19], "file": [3, 8, 10, 11, 12], "filesystem": 3, "fill_between": 13, "final": [3, 5, 12, 13, 14], "find": [3, 7, 10, 12, 13, 14, 23], "fine": [4, 22], "first": [0, 1, 2, 3, 4, 5, 7, 8, 10, 11, 12, 13, 14, 15, 20, 21, 22], "firstfeas": 6, "fit": [1, 3, 22], "fittest": 3, "five": [7, 16], "fix": [3, 6, 11], "fixed_cost": 3, "fixed_vehicle_cost": 3, "fixeds": 3, "flatten": 12, "fleet": [3, 11, 15, 23], "float": [1, 2, 3, 5, 6], "fmt": 13, "focu": 23, "focus": 13, "folder": 21, "follow": [3, 7, 8, 11, 12, 13, 14, 17, 18, 20, 22, 23], "forc": 3, "forget": 10, "fork": 8, "form": [3, 5, 13, 14], "formal": 1, "format": [3, 15], "found": [2, 3, 5, 11, 12, 13, 16, 19, 22, 23], "frac": 1, "fraction": 3, "francoi": 5, "free": [3, 5, 8, 9, 13, 18, 19, 20], "frequent": 19, "frm": [3, 13], "frm_idx": 13, "from": [0, 1, 3, 5, 7, 8, 10, 11, 12, 13, 14, 15, 16, 19, 20, 22, 23], "from_csv": 3, "from_data": [3, 12], "from_fil": 3, "fulfil": 23, "full": [3, 13], "function": [1, 2, 3, 7, 8, 9, 10, 11, 12, 19], "further": [3, 5, 10, 14, 18, 22], "futur": 3, "g": [3, 10, 12, 23], "gain": 12, "gap": [7, 12, 16], "gdb": 8, "ge": [3, 12, 23], "gehr": [7, 12], "gener": [0, 3, 5, 11, 13, 22], "generalis": 15, "generation_s": 3, "genet": [0, 1, 2, 3, 5, 12, 22, 23], "geneticalgorithm": [3, 5, 6, 14], "geneticalgorithmparam": 3, "get": [3, 7, 8, 10, 21, 22, 23], "git": [7, 8, 21], "github": [20, 21], "give": [3, 12, 13], "given": [0, 1, 2, 3, 4, 5, 10, 11, 12, 13, 18, 22, 23], "global": 22, "glossari": [11, 15], "go": [2, 8, 12, 14, 21, 23], "goal": 23, "goe": 21, "good": [1, 3, 10, 12, 13, 14], "googl": 16, "gotcha": 10, "grain": 4, "granular": [5, 10], "graph": [12, 23], "graphic": 18, "greatli": 8, "greedi": 4, "greedy_repair": 4, "green": 8, "grei": [2, 12], "group": [3, 11, 15, 21], "grow": 14, "guarante": 23, "gui": 8, "guidelin": 10, "h": [12, 13, 23], "ha": [3, 4, 6, 7, 8, 12, 13, 20, 21, 22, 23], "hammer": 14, "hand": 3, "handl": [8, 9, 12], "happen": [5, 18], "hard": [3, 12, 13, 23], "has_excess_dist": 3, "has_excess_load": 3, "has_time_warp": 3, "have": [1, 2, 3, 8, 10, 12, 13, 14, 15, 16, 19, 20, 21, 23], "head": 23, "header": 12, "heavi": 13, "help": [2, 3, 7, 8, 10, 15, 19], "here": [1, 3, 10, 12, 13, 14, 19, 23], "heterogen": [11, 15], "heurist": [13, 23], "hfvrp": [7, 16], "hg": [15, 16, 17], "hide": 14, "high": [2, 3, 13, 14, 17, 22], "higher": 22, "highli": 1, "highlight": 13, "histori": 3, "hit": 3, "hold": 3, "holland": 0, "homberg": [7, 12], "homogen": [11, 23], "hood": 14, "hope": 10, "hopefulli": 5, "horizon": [12, 13], "horizont": 3, "hotspot": 8, "hour": 7, "how": [3, 5, 8, 10, 11, 12, 13, 14, 16, 18, 19], "howev": 10, "html": 12, "http": [5, 8, 17, 21], "hybrid": [3, 5, 14, 22], "i": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23], "icon": 8, "id": 8, "ideal": [0, 10], "ident": 3, "idx": [3, 12, 13], "ignor": [11, 13], "iinfo": 3, "ij": [10, 12, 23], "ijoc": 17, "il": 16, "ils_": 16, "immedi": 12, "implement": [3, 5, 6, 10, 14, 22, 23], "impli": 11, "implicit": 3, "import": [3, 5, 8, 10, 12, 13, 14, 20], "impos": [3, 15], "impress": 12, "improv": [3, 5, 6, 7, 10, 13, 14, 16, 22], "in_zon": 13, "includ": [2, 3, 5, 8, 12, 20, 23], "inclus": 8, "incomplet": 0, "inconsist": 3, "incorpor": 12, "incorrect": 16, "increas": [3, 13, 14], "increasingli": 12, "incredibli": 8, "incur": [3, 11], "inde": 19, "index": [3, 21], "indexerror": 3, "indic": [0, 1, 3, 5, 10, 11, 12, 13, 18], "inf": 3, "infeas": [2, 3, 9, 12, 13, 14], "inform": [3, 8, 17, 20], "inherit": [5, 22], "init": [7, 14], "init_from": [3, 14], "init_sol": 14, "initi": [2, 3, 10, 14, 22], "initial_load": 3, "initial_penalti": 3, "initial_solut": 3, "initialis": [3, 7, 22], "input": [13, 22], "insert": [3, 4, 5, 10], "insid": [8, 13], "inspect": [8, 12, 13], "inspir": 10, "instal": [3, 7, 15, 20], "instanc": [0, 2, 3, 4, 5, 7, 8, 10, 11, 13, 14, 16, 23], "instanti": [12, 14], "instead": [3, 13, 19], "instruct": [15, 16], "instrument": 8, "insuffici": 3, "int": [2, 3, 4, 5, 6, 10, 14], "int64": 3, "integ": [3, 5, 13], "integr": 12, "intend": 3, "intens": 8, "intensifi": 5, "interest": 23, "interfac": [3, 7, 12, 13, 14, 16], "intern": [0, 3, 12, 14], "interv": 3, "introduc": [3, 10, 13, 16, 23], "introduct": [13, 15], "invalid": 3, "investig": [2, 13, 14], "involv": 3, "irrespect": 3, "is_collect": 3, "is_complet": 3, "is_feas": [3, 14], "is_group_feas": 3, "issu": [3, 8, 10, 16, 20], "item": 13, "iter": [2, 3, 5, 6, 7, 12, 13, 14, 22], "iter_r": 14, "its": [3, 5, 10, 13, 14, 18], "itself": [3, 10], "j": [0, 3, 5, 10, 12, 23], "januari": 16, "job": 8, "jointli": 15, "journal": 17, "json": 8, "juli": 16, "june": 16, "jupyt": 21, "just": [0, 3, 5, 13, 14], "k": [3, 22], "k37": 12, "k_0": 23, "k_i": 23, "kdab": 8, "keep": [8, 10], "kei": [11, 12], "kept": 10, "keyword": 3, "know": 13, "known": [5, 7, 12, 14, 16], "kobayashi": 0, "kool": 17, "kwarg": [3, 5, 6], "l": [11, 14, 17, 22], "l_i": [12, 23], "lan": 17, "larg": [3, 4, 5, 7, 12, 14], "largest": [7, 12], "last": [3, 13], "late": [3, 18], "later": [2, 3, 10, 12], "latest": [2, 3, 12, 18, 19, 21, 23], "latter": [3, 10], "launch": 8, "lb_divers": 3, "ldot": [1, 23], "lead": 22, "learn": 14, "least": [3, 22], "leav": [3, 18, 19], "left": [3, 8, 12, 14, 23], "legend": [2, 12], "len": [12, 13, 14], "leon": 17, "less": [1, 8], "let": [1, 3, 8, 12, 13, 14], "level": [3, 14], "like": [3, 8, 11, 12, 13, 21], "limit": [7, 18, 20, 23], "line": [7, 8, 12, 13, 20], "linehaul_sect": 11, "link": 1, "linux": [8, 15, 19], "list": [2, 3, 4, 5, 6, 11, 13, 14, 15, 16, 19, 23], "literatur": [5, 16], "littl": 12, "ln": 4, "load": [2, 3, 12, 13, 19], "load_dimens": 2, "load_penalti": [3, 14], "loadseg": 9, "loc": 3, "local": [3, 5, 14, 15, 16, 22, 23], "localsearch": [5, 10, 14], "locat": [1, 3, 11, 12, 13, 23], "logic": 10, "long": [3, 8], "longer": 3, "look": [3, 10, 12, 13, 14, 15, 20], "loop": [3, 22], "lot": [10, 14], "lower": [3, 5, 11], "lowest": [22, 23], "lump": 8, "m": [0, 5, 13, 23], "mac": 15, "machin": 19, "maco": 8, "made": [8, 10, 21], "mai": [3, 7, 8, 10, 13, 16, 18, 23], "mail": 20, "main": [3, 19], "maintain": [1, 8, 22], "make": [3, 5, 8, 10, 14, 21], "make_random": [3, 14], "manag": [3, 6, 12], "mandatori": 23, "manhattan": 13, "mani": [3, 8, 14, 23], "manner": 5, "march": 16, "mark": 13, "mathcal": 3, "matplotlib": [3, 12, 13, 14], "matric": [3, 11], "matrix": [3, 5], "max": 3, "max_dist": 3, "max_dur": [3, 13], "max_iter": 6, "max_penalti": 3, "max_pop_s": 3, "max_reload": [3, 13], "max_runtim": [6, 8], "max_trip": 3, "maxim": 1, "maximis": 23, "maximum": [1, 3, 6, 11, 13, 15, 18, 22, 23], "maxiter": [6, 12, 14], "maxruntim": [6, 12, 13, 14], "mdvrp": 23, "mdvrptw": [7, 16], "mean": [3, 8, 16, 23], "meaning": 8, "meant": 10, "measur": 15, "mechan": 22, "meet": 8, "membership": [3, 11], "memet": 0, "memori": 3, "merg": [8, 10], "merit": 11, "meson": 8, "messag": 8, "met": [6, 14, 22], "metaheurist": [15, 23], "method": [0, 3, 6, 8, 10, 12, 15, 22, 23], "middl": 14, "might": [8, 10, 15, 21, 23], "min": [3, 12], "min_penalti": 3, "min_pop_s": 3, "minim": 3, "minimis": [12, 13, 23], "minimise_fleet": 3, "minimum": [3, 5, 22], "minut": [7, 8, 21], "miss": 0, "mit": 8, "mitig": 13, "mix": 8, "mode": 8, "model": [3, 12, 13, 14, 18, 20], "modern": 8, "modifi": [3, 7, 10, 13], "modul": [0, 1, 2, 3, 4, 5, 6, 10], "modular": 14, "more": [0, 3, 4, 5, 8, 9, 10, 12, 13, 14, 16, 20, 22, 23], "moreov": 12, "most": [3, 8, 13, 16, 21, 23], "motiv": 23, "move": [4, 5, 10, 14], "mtvrptwr": [7, 16], "much": [3, 4, 5, 12, 14, 16, 20], "multi": [11, 15], "multigraph": 3, "multipl": [2, 3, 6, 10, 13, 15, 23], "multiplecriteria": 6, "multipli": 3, "multitrip": 5, "must": [3, 5, 6, 8, 10, 11, 12, 13, 23], "mutual": [3, 11], "mutually_exclus": 3, "mutually_exclusive_group_sect": 11, "myself": 19, "n": [1, 5, 7, 12, 17, 22, 23], "n439": 12, "nagata": 0, "name": [3, 8, 16], "nativ": 8, "natur": 0, "navig": 21, "nb_close": 3, "nb_elit": 3, "nb_granular": 5, "nb_iter_no_improv": 3, "ndarrai": 3, "ne": 1, "nearest": [3, 4, 12], "nearest_route_insert": 4, "nearli": 14, "necessari": [3, 8, 10], "necessarili": [3, 5], "need": [3, 7, 8, 10, 13, 14, 20, 21, 22], "neg": 3, "neighborhood": 5, "neighbour": [1, 3, 5, 10, 14], "neighbourhood": [3, 4, 5, 10, 14], "neighbourhoodparam": [3, 5], "new": [0, 3, 4, 5, 8, 9, 12, 13, 14, 15, 16, 19, 20, 22, 23], "new_sol": 14, "newli": [5, 10], "next": [3, 8, 10], "nice": 3, "niel": 17, "node": 3, "node_coord_sect": 11, "node_op": [3, 14], "node_oper": [3, 5, 14], "nodeoper": [3, 5], "noimprov": 6, "non": [3, 5, 6, 11], "none": [2, 3, 10, 19, 23], "nonzero": 3, "note": [12, 13], "notebook": [12, 13, 14, 21, 23], "novemb": 16, "now": [3, 8, 10, 12, 13, 14, 21], "np": [3, 23], "num_avail": 3, "num_bit": 3, "num_client": 3, "num_depot": 3, "num_feas": [3, 14], "num_group": 3, "num_infeas": [3, 14], "num_iter": 3, "num_load_dimens": 3, "num_loc": 3, "num_missing_cli": 3, "num_profil": 3, "num_rout": 3, "num_stop": 12, "num_to_skip": 2, "num_trip": 3, "num_vehicl": 3, "num_vehicle_typ": 3, "number": [0, 1, 2, 3, 4, 5, 6, 8, 10, 11, 12, 13, 17, 18], "numer": [3, 13], "numpi": [3, 8], "o": [15, 22], "object": [0, 1, 2, 3, 5, 7, 10, 12, 13, 14, 18, 19, 22, 23], "obscur": 2, "observ": [3, 6, 12, 14], "obtain": [3, 8, 10, 12, 16, 22, 23], "occur": [3, 7], "off": [3, 13, 14], "offer": 13, "offset": 3, "offspr": [0, 3, 5, 12, 14, 22], "often": [0, 2, 3, 8, 13, 23], "oliv": 0, "onc": [3, 6, 8, 21, 23], "one": [0, 1, 3, 4, 7, 8, 9, 11, 12, 13, 14, 16, 23], "onli": [2, 3, 4, 8, 10, 16, 21, 22], "onlin": 8, "onward": 16, "op": 5, "open": [3, 5, 8, 10, 12, 13, 15, 18, 20, 21, 23], "oper": [1, 3, 8, 10, 14, 15, 16, 22, 23], "opt": [5, 10, 13], "optim": [12, 13, 23], "optimis": [8, 12], "option": [2, 3, 7, 13, 15, 21, 23], "order": 0, "ordered_crossov": 0, "org": [5, 17], "orient": 15, "origin": [3, 8], "other": [1, 3, 5, 8, 11, 13, 18], "otherwis": [3, 6], "our": [1, 8, 10, 11, 12, 13, 14, 20], "ourself": 13, "out": [2, 3, 7, 13, 14, 19, 23], "outcom": 3, "output": [8, 13, 20, 22], "over": [3, 4, 13, 14, 16, 20, 22, 23], "overal": [3, 5], "overarch": 23, "overbrac": 10, "overlap_toler": 5, "overrid": 13, "overview": [12, 20], "own": [3, 14, 20], "ox": 0, "p_": 1, "p_1": 22, "p_2": 22, "p_d": 3, "p_f": [1, 3], "p_i": [3, 10, 23], "packag": [6, 11, 14, 15, 16, 17, 21], "page": [3, 10, 11, 13, 15, 16, 17, 18, 21, 22, 23], "pai": 8, "pair": [1, 3, 11], "pairwis": 3, "paper": [16, 17, 22], "parallel": 0, "param": [3, 5], "paramet": [0, 1, 2, 3, 4, 5, 6, 14, 23], "parent": [0, 3, 22], "pars": [8, 16], "part": [1, 3, 9, 10, 12, 14], "particular": [3, 10, 13, 18], "particularli": [0, 3, 7, 10, 13], "partit": [12, 23], "pass": [3, 5, 12, 13], "passmark": 7, "patch": 10, "path": 3, "pc": 23, "pcvrptw": [7, 16], "pen_manag": 14, "penalis": [3, 9], "penalised_cost": [3, 14], "penalti": [3, 5, 9, 14], "penalty_decreas": 3, "penalty_increas": 3, "penalty_manag": 3, "penaltyboundwarn": 3, "penaltymanag": [3, 9, 14], "penaltyparam": 3, "per": [3, 11, 13], "perceiv": 16, "percentag": [1, 3, 16], "perf": 8, "perform": [0, 1, 2, 3, 5, 7, 8, 10, 16, 17, 22], "period": [8, 14], "permiss": 8, "permut": 0, "pessoa": 7, "pick": 11, "pickup": [0, 3, 15], "pip": [8, 15, 21], "place": [5, 8, 10, 12, 13, 20], "plan": 3, "pleas": [8, 10, 15, 17, 20], "plot": [12, 13, 14, 15], "plot_": 12, "plot_client": [2, 13], "plot_coordin": [2, 12, 13], "plot_demand": 2, "plot_divers": 2, "plot_inst": [2, 12], "plot_object": 2, "plot_result": [2, 12, 14], "plot_route_schedul": [2, 12], "plot_runtim": 2, "plot_solut": [2, 13], "plot_time_window": 2, "plt": [12, 13, 14], "plu": [3, 9], "pm": 14, "poetri": [8, 21], "point": [3, 4, 10, 22], "pool": [3, 13], "pop": 14, "popul": [1, 2, 3, 12, 22], "populationparam": 3, "posit": [3, 5], "possibl": [2, 3, 4, 8, 10, 23], "possibli": [3, 10], "postal": 23, "potenti": 23, "ppsn": 0, "pre": [8, 15], "prebuilt": 19, "preced": [1, 3], "pred": 3, "predecessor": 3, "prepar": 8, "preprint": 17, "presenc": 3, "present": [0, 3, 11, 12], "pretti": 14, "previou": 16, "primari": 23, "primarili": 23, "print": [3, 12, 13, 14], "prize": [3, 10, 11, 15, 16], "prize_sect": 11, "probabl": 3, "problem": [0, 1, 2, 3, 4, 5, 7, 10, 11, 12, 13, 14, 15, 20, 22], "problemdata": [0, 2, 3, 4, 5, 8, 12], "proce": [8, 10], "procedur": [12, 22], "proceed": 0, "process": [8, 14], "produc": [12, 13], "profil": [3, 13, 15], "program": 8, "progress": [3, 13, 14], "project": 21, "proper": 3, "properti": 3, "protocol": [5, 6], "prove": 10, "provid": [0, 1, 2, 3, 4, 5, 7, 11, 13, 14, 22], "proxim": 5, "pseudo": 3, "pseudocod": 22, "publish": 17, "pull": [8, 10], "pure": [2, 13], "purg": 14, "put": 14, "pvvrp": 8, "py": 8, "pyplot": [12, 13, 14], "pytest": 8, "python": [3, 8, 10, 20, 21], "pythonconfig": 8, "pythoncpp": 8, "pyvrp": [0, 1, 2, 4, 5, 6, 7, 8, 10, 11, 12, 13, 18, 19, 20, 21, 22, 23], "q": 23, "q_": [12, 23], "quadrat": 4, "qualiti": [1, 4, 12, 14, 22], "quantiti": 11, "queiroga": [7, 16], "question": [10, 19, 20], "quick": 15, "quickli": [5, 7, 9, 12, 14, 16], "quot": 3, "quote_minim": 3, "r": [0, 3], "rais": [0, 2, 3, 4, 5, 10], "ran": 7, "rand": 3, "randint": 3, "random": [0, 3, 5, 22], "randomli": [0, 3], "randomnumbergener": [0, 3, 5, 14], "rang": [3, 7, 13, 14], "rather": [13, 16], "rc208": [12, 14], "rc2_10_5": [8, 12], "re": [5, 13, 14, 23], "reach": [3, 14, 16, 22], "read": [3, 11, 14, 15], "read_solut": [3, 12], "readi": [8, 13, 14], "reason": 8, "recal": 14, "recent": [3, 8, 9, 16], "recommend": 10, "record": 8, "rectangular": 13, "red": [8, 13], "reduc": [3, 8], "refer": [0, 3, 5, 7, 10, 22], "referenc": 3, "regardless": 10, "region": 12, "regist": [3, 22], "registr": 3, "regress": 10, "regular": 13, "regularli": 9, "rel": [1, 23], "relat": [3, 18], "releas": [3, 8, 10, 11, 15], "release_tim": 3, "release_time_sect": 11, "relev": [8, 20], "reli": 14, "relicens": 8, "reload": [3, 5, 11, 15], "reload_depot": [3, 13], "reloc": 5, "remain": [2, 12, 13], "remov": [3, 10, 22], "repair": [0, 3, 15], "repair_boost": 3, "repair_prob": 3, "repar": 3, "repeat": [5, 22], "repeatedli": 3, "replac": [3, 5], "report": [3, 16], "repositori": [7, 8, 10, 11, 16, 20, 21], "repres": [1, 3, 5, 12, 16, 23], "reproduc": 20, "request": [8, 10, 13, 23], "requir": [3, 8, 10, 11, 13, 15, 23], "research": [17, 23], "reset": 3, "resolv": 21, "respect": [1, 3, 10, 13, 14], "respons": [0, 4, 5], "restart": 3, "restrict": [3, 14, 15], "result": [0, 2, 3, 4, 5, 8, 10, 12, 13, 14, 16, 22], "resum": 3, "retain": 8, "retriev": 3, "return": [0, 1, 2, 3, 4, 5, 6, 10, 12, 13, 14, 22, 23], "reus": 14, "reveal": 13, "review": 10, "reward": 13, "rich": 18, "right": [3, 12, 14, 23], "rightarrow": 5, "rigor": 23, "rng": [0, 3, 5, 14], "road": 3, "root": 8, "round": [3, 7, 12], "round_func": [3, 8, 12], "rout": [0, 1, 2, 3, 4, 9, 11, 12, 13, 14, 15, 18, 22], "route_op": [3, 14], "route_oper": [3, 5, 14], "routeoper": [3, 5], "run": [2, 3, 6, 7, 8, 12, 13, 14, 15, 16, 20], "runtim": [2, 3, 4, 6, 7, 12, 13, 14], "runtimeerror": 3, "s_": [1, 12, 22, 23], "s_1": 22, "s_f": 1, "sake": 12, "salesman": 0, "same": [1, 3, 5, 13, 23], "sane": 10, "save": 23, "saw": 14, "scale": [3, 7, 13], "scalingwarn": 3, "schedul": [2, 3, 12], "scheduledvisit": 3, "scheme": [5, 9, 14], "scienc": 5, "score": [1, 7], "script": 8, "search": [0, 2, 3, 4, 6, 8, 10, 12, 13, 15, 16, 22, 23], "search_method": 3, "searchmethod": [3, 5], "second": [0, 1, 3, 5, 6, 7, 12, 13, 14], "section": [8, 13, 23], "see": [0, 3, 8, 9, 10, 11, 12, 13, 14, 16, 20, 22, 23], "seed": [3, 7, 8, 12, 14, 16], "seen": 13, "segment": 9, "select": [0, 3, 8, 12, 13, 14, 22], "selective_route_exchang": [0, 14], "self": [3, 20], "semi": 12, "sens": 10, "sensibl": 3, "separ": [3, 12, 14], "sequenc": [3, 9], "serv": 11, "servic": [2, 3, 11, 12, 13, 15, 18, 19, 23], "service_dur": [3, 12], "service_tim": 11, "service_time_sect": 11, "set": [3, 4, 7, 10, 12, 13, 14, 15, 16, 18, 21, 22, 23], "set_neighbour": 5, "setup": 8, "sever": [3, 11, 12, 13, 14, 18], "shade": 2, "share": 1, "shift": [3, 13, 15], "ship": [3, 6], "shipment": 13, "short": [3, 13, 20], "shorter": 16, "shortest_rout": 12, "should": [0, 1, 3, 6, 7, 8, 10, 11, 12, 16], "show": [2, 10, 12, 13, 14], "show_vers": [3, 20], "showcas": 13, "signatur": 14, "signific": [3, 16], "silent": 11, "similar": [3, 12], "similarli": [1, 3], "simpl": [3, 6, 10, 23], "simpli": [8, 21], "simplifi": 16, "simultan": 15, "sinc": [0, 16, 17], "singl": [3, 7, 10, 23], "site": [11, 15], "size": [3, 5, 7, 13, 22], "skip": [2, 10], "slack": [2, 3, 12], "slightli": 13, "small": [10, 13], "smaller": 3, "smallest": 3, "smith": 0, "smooth": 3, "smoothli": 8, "snippet": 20, "so": [4, 5, 8, 10, 11, 13, 14, 19, 21, 22], "softwar": 8, "sol": [3, 12, 14], "solid": 2, "solomon": [7, 12, 14], "solut": [0, 1, 2, 3, 4, 5, 6, 7, 10, 12, 13, 16, 18, 22, 23], "solutions_between_upd": 3, "solv": [0, 3, 5, 10, 13, 23], "solveparam": 3, "solver": [3, 12, 13, 15, 17, 23], "some": [3, 4, 6, 8, 10, 11, 12, 13, 14, 23], "someth": [8, 20], "sometim": 13, "sort": 2, "sourc": [0, 2, 3, 5, 6, 8, 15, 19], "sp": 16, "sp_": 16, "space": [14, 22], "special": 5, "specif": [3, 13], "specifi": [3, 6, 7, 8, 11, 13, 14, 20, 23], "spend": 3, "spike": 14, "srex": [0, 14], "stand": 3, "standard": [8, 10, 11, 12], "standardis": 11, "start": [3, 5, 8, 10, 11, 12, 13, 14, 18, 22, 23], "start_depot": [3, 13], "start_lat": 3, "start_servic": [3, 12], "start_tim": [3, 12], "stat": 3, "state": [3, 15, 23], "statist": [2, 3, 9, 10, 12, 13], "step": [8, 10], "stick": 13, "still": [13, 14, 23], "stop": [3, 7, 8, 12, 13, 14, 15, 16, 22], "stoppingcriterion": [3, 6, 14], "store": 3, "str": [2, 3], "straightforward": 21, "strategi": 3, "stream": 3, "strictli": 10, "string": 3, "structur": 5, "struggl": 3, "stub": 10, "studi": [0, 23], "studio": 8, "style": 8, "sub": [3, 14], "subject": [3, 8], "submiss": 8, "submit": 8, "submodul": 7, "subplot": [12, 13], "subpopul": [2, 3], "subramanian": 16, "subsequ": 3, "subsystem": 19, "succ": 3, "succeed": 1, "success": 10, "successor": [3, 5], "suffer": 3, "suffici": [3, 10, 14], "suggest": [1, 9, 19, 22], "suit": 8, "sum": 11, "sum_": [1, 3, 10], "summari": [3, 13], "support": [3, 8, 11, 12, 13, 15, 16, 18], "suppos": 13, "sure": [3, 8, 21], "survivor": [3, 14, 22], "swap": 5, "swaprout": 5, "swapstar": 5, "swaptail": 5, "symbol": 8, "symmetr": [1, 5], "symmetric_neighbour": 5, "symmetric_proxim": 5, "symmetris": 5, "system": 8, "t": [3, 10], "t_": [12, 23], "t_r": 3, "tab": 8, "tabl": 16, "tablefmt": 12, "tabul": 12, "tackl": [14, 23], "take": [3, 4, 7, 8, 10, 13, 14, 18, 20, 21, 22], "target": [3, 8], "target_feas": 3, "team": 15, "templat": [5, 8, 20], "temporari": 3, "temporarili": 3, "temptat": 8, "ten": [7, 16], "term": [3, 9, 10, 14], "termin": [6, 13], "test": [3, 5, 8, 10], "text": [1, 3, 10], "than": [0, 1, 3, 4, 8, 10, 12, 13, 16, 20, 22], "thei": [1, 5, 8, 10, 13, 14, 23], "them": [13, 23], "thereaft": 5, "therefor": 16, "thi": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23], "thibaut": 5, "thing": [10, 19], "think": 10, "third": 5, "thorough": 22, "those": [2, 7, 10, 13, 14, 20], "thread": 20, "through": [3, 5, 8, 11, 13, 15], "thu": [1, 7, 10, 13, 15], "tight_layout": [12, 14], "time": [0, 1, 2, 3, 5, 6, 7, 8, 10, 11, 14, 15, 16, 19, 20], "time_r": 14, "time_warp": [3, 12], "time_warpdeliveri": 12, "time_window": 13, "time_window_sect": 11, "titl": [2, 12, 17], "to_csv": 3, "to_idx": 13, "togeth": [3, 14, 18], "toml": [3, 7], "tomli": 3, "too": [3, 8, 12, 13, 14], "tool": [8, 12, 13, 14, 15, 16], "top": [3, 8, 12, 14], "total": [3, 10, 12, 13, 23], "toth": 23, "touch": 8, "tournament": [3, 22], "toward": 3, "tqdm": 3, "track": [3, 7, 8, 12, 13, 14, 16], "trajectori": 2, "transpar": 12, "transport": [5, 11, 23], "travel": [0, 2, 3, 11, 12, 13, 23], "travel_dur": 3, "triag": 20, "trick": 20, "trigger": 3, "trip": [3, 11, 12, 13, 14, 15], "tripreloc": 5, "troubl": 8, "troubleshoot": 19, "trsc": 5, "truck": [3, 12, 13], "true": [2, 3, 5, 6, 10, 13], "trunc": 3, "truncat": [3, 12], "try": 8, "tsp": [0, 3], "tspwarn": 3, "tune": 22, "tupl": [0, 2, 3], "turn": 3, "tutori": [12, 15, 22], "tw_earli": [3, 13], "tw_late": [3, 13], "tw_penalti": [3, 14], "twice": 3, "two": [0, 1, 3, 5, 7, 10, 12, 13, 14, 18, 22], "type": [0, 1, 3, 4, 5, 6, 8, 10, 13], "typeerror": 3, "typic": [3, 4, 8, 10, 11, 12], "u": [5, 8, 12, 13, 14, 20], "ub_divers": 3, "uchoa": 7, "uint64": 3, "unabl": 3, "uncollect": [3, 10], "uncollected_pr": 3, "unconstrain": [3, 11], "under": [8, 14], "underli": [2, 3], "understand": [2, 11, 14], "understood": 11, "unexpect": 13, "unit": [3, 11], "unit_distance_cost": 3, "unit_duration_cost": 3, "unless": [3, 8, 13], "unlik": 13, "unlimit": 11, "unplan": 4, "unrel": 8, "unrestrict": 13, "unsur": 10, "until": [3, 5, 14, 18, 22], "unus": 0, "unvisit": 3, "up": [2, 3, 10, 11, 13, 14, 15, 21], "updat": [3, 10, 16, 21], "upgrad": [8, 21], "upon": 22, "upper": 3, "urban": 13, "url": 17, "us": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22], "user": 5, "usernam": 8, "usual": [8, 16], "v": [3, 5, 12, 23], "v0": [13, 16], "v9": 16, "v_c": [12, 23], "v_d": 23, "v_r": 3, "valu": [1, 2, 3, 10, 11, 13, 16, 22], "valueerror": [0, 2, 3, 4, 5], "vari": [8, 9], "variant": [12, 13, 15, 16], "variou": [2, 3, 6, 10, 12, 13, 22, 23], "ve": 12, "vehicl": [0, 2, 3, 4, 5, 10, 11, 12, 13, 15, 19, 22], "vehicle_typ": 3, "vehicles_allowed_clients_sect": 11, "vehicles_depot_sect": 11, "vehicles_fixed_cost_sect": 11, "vehicles_max_dist": 11, "vehicles_max_distance_sect": 11, "vehicles_max_dur": 11, "vehicles_max_duration_sect": 11, "vehicles_max_reload": 11, "vehicles_max_reloads_sect": 11, "vehicles_reload_depot_sect": 11, "vehicles_unit_distance_cost_sect": 11, "vehicletyp": [3, 13, 18], "veri": [2, 3, 5, 10, 12, 13, 14], "verifi": 8, "version": [3, 8, 10, 16, 20, 21], "vertex": [12, 23], "vertic": [2, 3, 12], "via": [5, 8, 15, 20, 21], "vidal": [5, 7, 16, 17, 22], "view": 3, "vigo": 23, "violat": 3, "virtual": [8, 21], "visit": [3, 5, 9, 10, 11, 12, 13, 15, 23], "visual": 8, "visualis": 12, "volum": 17, "vrp": [3, 5, 8, 14, 15, 17, 22], "vrpb": [7, 16], "vrplib": [3, 12, 15], "vrpsolvereasi": 23, "vrptw": [7, 8, 10, 13, 14, 16, 23], "vscode": 8, "w": [3, 17], "wa": [3, 5, 10], "wai": [3, 8, 14, 19, 21, 23], "wait": [2, 3, 5, 12, 18, 23], "wait_dur": [3, 12], "want": [1, 3, 8, 10, 21], "warp": [2, 3, 5], "wast": 23, "wave": 3, "we": [1, 3, 7, 8, 9, 10, 11, 12, 13, 14, 16, 18, 19, 21, 22, 23], "websit": 8, "weight": [5, 11, 12, 23], "weight_time_warp": 5, "weight_wait_tim": 5, "well": [0, 11, 12, 14], "were": 16, "what": [2, 3, 8, 10, 11, 12], "when": [0, 2, 3, 4, 5, 6, 8, 10, 11, 13, 14, 18, 23], "whenev": [6, 8], "where": [0, 2, 3, 10, 12, 13, 21, 23], "whether": [1, 2, 3, 5, 10], "which": [1, 2, 3, 4, 5, 8, 10, 12, 13, 14, 18, 22, 23], "whichev": 7, "while": [3, 5, 8, 10, 23], "whose": [2, 3], "wide": 12, "window": [0, 2, 3, 5, 8, 11, 15, 18, 19], "within": [3, 8, 23], "without": [3, 7, 13, 15, 19], "work": [8, 10, 11, 14, 22, 23], "world": 13, "wors": 12, "worst": 4, "worth": 13, "wouda": 17, "wouda_lan_kool_pyvrp_2024": 17, "wouter": 17, "write": [3, 8, 10], "written": [8, 10], "wrote": 8, "wsl": 19, "x": [3, 7, 8, 11, 12, 13], "x_": 10, "xcode": 8, "xi": 0, "xo": 22, "xor": 3, "y": [0, 2, 3, 5, 8, 11, 12, 13], "y_i": 10, "yang": 7, "year": 17, "yet": [3, 13, 14, 21], "ylim_adjust": 2, "you": [0, 3, 8, 10, 13, 15, 17, 19, 20, 21, 22, 23], "your": [2, 3, 7, 8, 10, 13, 17, 20, 21], "yourself": [8, 15], "zero": [1, 3, 11, 12], "zip": 12}, "titles": ["Crossover operators", "Diversity measures", "Plotting tools", "PyVRP", "Repair operators", "Search methods", "Stopping criteria", "Benchmarking", "Contributing", "Glossary", "Supporting new VRP variants", "The VRPLIB format", "Classic VRPs", "A quick tutorial", "Using PyVRP\u2019s components", "Contents", "Benchmarks", "Citing PyVRP", "Concepts", "FAQ", "Getting help", "Installation instructions", "A brief introduction to HGS", "A brief introduction to VRP"], "titleterms": {"": 14, "A": [13, 14, 22, 23], "The": [11, 12, 14], "algorithm": 14, "api": 15, "benchmark": [7, 16], "brief": [22, 23], "bug": 20, "build": 8, "capacit": [12, 13, 23], "chang": 8, "cite": 17, "classic": 12, "client": 18, "codespac": 8, "collect": [13, 23], "commit": 8, "compon": 14, "concept": 18, "conclus": [12, 14], "constraint": 18, "content": 15, "contribut": 8, "criteria": 6, "crossov": [0, 14], "data": 11, "debug": 8, "deliveri": 13, "depot": [13, 23], "develop": 15, "divers": 1, "durat": 18, "evalu": 14, "exampl": [15, 21], "extens": 8, "faq": 19, "featur": 20, "format": 11, "from": 21, "function": 14, "gener": 14, "genet": 14, "get": [15, 20], "github": 8, "glossari": 9, "help": 20, "hg": 22, "hint": [3, 10, 15, 16, 22, 23], "instal": [8, 19, 21], "instanc": 12, "instruct": 21, "introduct": [22, 23], "larger": 12, "licens": 8, "local": [8, 21], "manag": 14, "measur": 1, "method": [5, 14], "model": 19, "multi": [13, 23], "new": 10, "node": 5, "note": [0, 1, 3, 5, 8, 10, 11, 16, 20, 22, 23], "number": 14, "oper": [0, 4, 5], "pickup": 13, "plot": 2, "popul": 14, "prize": [13, 23], "problem": 23, "profil": 8, "pyvrp": [3, 14, 15, 16, 17], "quick": 13, "random": 14, "read": 12, "refer": [15, 16], "reload": 13, "repair": 4, "report": 20, "represent": 14, "request": 20, "restrict": 13, "rout": [5, 23], "run": 21, "search": [5, 14], "section": 11, "set": 8, "simultan": 13, "solut": 14, "solv": [12, 14], "solver": 16, "sourc": 21, "specif": 11, "start": 15, "stop": 6, "submit": 20, "support": [10, 23], "time": [12, 13, 18, 23], "tool": 2, "tour": 14, "tutori": 13, "up": 8, "us": 14, "variant": [10, 23], "vehicl": [18, 23], "vrp": [10, 12, 13, 16, 23], "vrplib": 11, "vrptw": 12, "warn": [0, 3, 13], "window": [12, 13, 23], "zone": 13}})