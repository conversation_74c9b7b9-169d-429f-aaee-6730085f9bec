Citing PyVRP
============

If you use PyVRP in your research, please consider citing the following paper:

   <PERSON><PERSON><PERSON>, N.A., <PERSON><PERSON>, and <PERSON><PERSON> (2024). 
   PyVRP: a high-performance VRP solver package. *INFORMS Journal on Computing*, 36(4): 943-955.
   `<https://doi.org/10.1287/ijoc.2023.0055>`_.

Or, using the following BibTeX entry:

.. code-block:: latex

   @article{<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Kool_PyVRP_2024,
     doi = {10.1287/ijoc.2023.0055},
     url = {https://doi.org/10.1287/ijoc.2023.0055},
     year = {2024},
     volume = {36},
     number = {4},
     pages = {943--955},
     publisher = {INFORMS},
     author = {<PERSON><PERSON> and <PERSON> and <PERSON><PERSON>},
     title = {{PyVRP}: a high-performance {VRP} solver package},
     journal = {INFORMS Journal on Computing},
   }

A preprint of this paper is available on `arXiv <https://arxiv.org/abs/2403.13795>`_. 
Since PyVRP extends `HGS-CVRP <https://github.com/vidalt/HGS-CVRP/>`_, please also consider citing `Vidal (2022) <https://doi.org/10.1016/j.cor.2021.105643>`_.
